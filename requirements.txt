# Project LEVIATHAN - AI-based Cryptocurrency Futures Auto-Trading System
# Core Dependencies

# Data Science & Machine Learning
pandas>=2.0.0
numpy>=1.24.0
scikit-learn>=1.3.0
lightgbm>=4.0.0

# Data Collection & Market APIs
yfinance>=0.2.0
ccxt>=4.0.0
requests>=2.31.0

# Visualization & Dashboard
matplotlib>=3.7.0
plotly>=5.15.0
streamlit>=1.25.0
seaborn>=0.12.0

# Configuration & Environment
pydantic>=2.0.0
python-dotenv>=1.0.0
pyyaml>=6.0

# Testing & Development
pytest>=7.4.0
pytest-cov>=4.1.0
black>=23.0.0
flake8>=6.0.0

# Utilities
tqdm>=4.65.0
loguru>=0.7.0
schedule>=1.2.0

# Real-time Trading & Communication
websocket-client>=1.6.0  # For real-time data streaming
python-telegram-bot>=20.0  # For notifications
fastapi>=0.100.0  # For API endpoints
