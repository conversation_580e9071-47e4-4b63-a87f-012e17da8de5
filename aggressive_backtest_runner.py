#!/usr/bin/env python3
"""
🚀 Project LEVIATHAN - 사용자 설정 기반 공격적인 백테스팅
사용자가 원하는 공격적인 거래 설정으로 백테스팅 실행
"""

import pandas as pd
import numpy as np
import joblib
from pathlib import Path
import sys
from datetime import datetime
import logging

# 프로젝트 루트 추가
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import get_settings
from backtesting.backtest_engine import BacktestEngine
from models.binary_model import BinaryTradingModel

# 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class AggressiveBacktestRunner:
    """사용자 설정 기반 공격적인 백테스팅 실행기"""
    
    def __init__(self):
        self.settings = get_settings()
        self._apply_aggressive_settings()
        
    def _apply_aggressive_settings(self):
        """🚀 사용자의 공격적인 설정 적용"""
        logger.info("🚀 사용자 공격적인 설정 적용 중...")
        
        # 사용자 설정: 공격적인 거래 파라미터
        aggressive_config = {
            'LEVERAGE': 4,                    # 4배 레버리지 (사용자 설정)
            'MAX_POSITION_SIZE': 1.0,         # 자본의 100% 사용 (사용자 설정)
            'STOP_LOSS_PCT': 0.02,           # 2% 손절 (사용자 설정)
            'TAKE_PROFIT_PCT': 0.05,         # 5% 익절 (사용자 설정)
            'FEE_RATE': 0.0004,              # 0.04% 수수료 (사용자 설정)
            'SLIPPAGE_RATE': 0.0002,         # 0.02% 슬리피지 (사용자 설정)
        }
        
        # 공격적인 신호 임계값 (사용자 설정)
        aggressive_thresholds = {
            'high_confidence': 0.65,    # 65% 이상: STRONG_BUY (더 공격적)
            'medium_confidence': 0.55,  # 55% 이상: BUY (사용자 설정)
            'low_confidence': 0.45,     # 45% 이상: WEAK_BUY (더 공격적)
            'exit_threshold': 0.20      # 20% 미만: signal_exit (더 공격적)
        }
        
        # 설정 업데이트
        self.settings.update_trading_config(**aggressive_config)
        self.settings.trading.SIGNAL_THRESHOLDS = aggressive_thresholds
        
        logger.info("✅ 공격적인 설정 적용 완료")
        logger.info(f"   📊 레버리지: {self.settings.trading.LEVERAGE}배")
        logger.info(f"   💰 포지션 크기: {self.settings.trading.MAX_POSITION_SIZE*100:.0f}%")
        logger.info(f"   🎯 신호 임계값: {aggressive_thresholds}")
        
    def run_aggressive_backtest(self, start_date: str = '2024-10-01', 
                               end_date: str = '2025-06-01'):
        """
        공격적인 백테스팅 실행
        
        Args:
            start_date: 시작 날짜 (미래 데이터 참조 방지)
            end_date: 종료 날짜
        """
        logger.info(f"🚀 공격적인 백테스팅 시작: {start_date} ~ {end_date}")
        
        try:
            # 1. 과적합 방지 검증
            self._validate_backtest_period(start_date, end_date)
            
            # 2. 모델 로드
            model = self._load_model()
            
            # 3. 데이터 로드
            price_data = self._load_price_data(start_date, end_date)
            
            # 4. 모델 예측
            predictions = self._generate_predictions(model, price_data)
            
            # 5. 백테스팅 실행
            engine = BacktestEngine(initial_capital=10000)
            result = engine.run_backtest(predictions, price_data, start_date, end_date)
            
            # 6. 결과 출력
            self._print_results(result)
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 백테스팅 실패: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _validate_backtest_period(self, start_date: str, end_date: str):
        """과적합 방지를 위한 백테스팅 기간 검증"""
        logger.info("🔧 과적합 방지 검증 중...")
        
        # 모델 훈련 종료일 (일반적으로 2024-09-20)
        model_train_end = pd.to_datetime('2024-09-20')
        backtest_start = pd.to_datetime(start_date)
        
        if backtest_start <= model_train_end:
            logger.warning("⚠️ 경고: 미래 데이터 참조 가능성!")
            logger.warning(f"   📚 모델 훈련 종료: {model_train_end}")
            logger.warning(f"   📈 백테스팅 시작: {backtest_start}")
        else:
            logger.info("✅ 미래 데이터 참조 없음")
            
        # 백테스팅 기간 길이 확인
        backtest_end = pd.to_datetime(end_date)
        backtest_days = (backtest_end - backtest_start).days
        logger.info(f"📊 백테스팅 기간: {backtest_days}일")
        
        # 현실적 거래 비용 확인
        total_cost = self.settings.trading.FEE_RATE + self.settings.trading.SLIPPAGE_RATE
        logger.info(f"💰 총 거래 비용: {total_cost*100:.3f}%")
    
    def _load_model(self):
        """모델 로드"""
        model_path = "models/swing_s_v2_long_model_final_v2.pkl"
        try:
            model = joblib.load(model_path)
            logger.info(f"✅ 모델 로드: {model_path}")
            return model
        except Exception as e:
            logger.error(f"❌ 모델 로드 실패: {e}")
            raise
    
    def _load_price_data(self, start_date: str, end_date: str):
        """가격 데이터 로드"""
        logger.info("📊 가격 데이터 로드 중...")
        
        try:
            # 15분봉 데이터 로드
            price_data = pd.read_csv('data/BTCUSDT_15m.csv', 
                                   index_col='timestamp', parse_dates=True)
            
            # 백테스팅 기간 필터링
            mask = (price_data.index >= start_date) & (price_data.index <= end_date)
            price_data = price_data[mask]
            
            logger.info(f"✅ 가격 데이터 로드: {len(price_data):,}개 포인트")
            return price_data
            
        except Exception as e:
            logger.error(f"❌ 가격 데이터 로드 실패: {e}")
            raise
    
    def _generate_predictions(self, model, price_data):
        """모델 예측 생성"""
        logger.info("🔮 모델 예측 중...")
        
        try:
            # 간단한 피처 생성 (기본 기술적 지표)
            features = self._generate_simple_features(price_data)
            
            # 모델 예측
            if hasattr(model, 'predict_proba'):
                predictions_array = model.predict_proba(features)[:, 1]
            elif hasattr(model, 'predict'):
                raw_predictions = model.predict(features)
                predictions_array = 1 / (1 + np.exp(-raw_predictions))
            else:
                raise ValueError("모델에 predict 또는 predict_proba 메서드가 없습니다")
            
            # 예측 결과를 DataFrame으로 변환
            predictions = pd.DataFrame(
                {'confidence': predictions_array}, 
                index=features.index
            )
            
            logger.info(f"✅ 예측 완료: {len(predictions):,}개")
            logger.info(f"   📊 예측값 범위: {predictions_array.min():.3f} ~ {predictions_array.max():.3f}")
            logger.info(f"   📊 평균 예측값: {predictions_array.mean():.3f}")
            
            return predictions
            
        except Exception as e:
            logger.error(f"❌ 예측 실패: {e}")
            # 더미 예측값 생성 (테스트용)
            np.random.seed(42)
            dummy_predictions = np.random.beta(2, 5, len(price_data))
            predictions = pd.DataFrame(
                {'confidence': dummy_predictions}, 
                index=price_data.index
            )
            logger.warning(f"⚠️ 더미 예측값 사용: {len(predictions):,}개")
            return predictions
    
    def _generate_simple_features(self, price_data):
        """간단한 피처 생성"""
        logger.info("🔧 기본 피처 생성 중...")
        
        features = pd.DataFrame(index=price_data.index)
        
        # 가격 기반 피처
        features['returns'] = price_data['close'].pct_change()
        features['volatility'] = features['returns'].rolling(20).std()
        
        # 이동평균
        for period in [5, 10, 20, 50]:
            features[f'sma_{period}'] = price_data['close'].rolling(period).mean()
            features[f'price_sma_{period}_ratio'] = price_data['close'] / features[f'sma_{period}']
        
        # RSI
        delta = price_data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        features['rsi'] = 100 - (100 / (1 + rs))
        
        # 볼린저 밴드
        sma_20 = price_data['close'].rolling(20).mean()
        std_20 = price_data['close'].rolling(20).std()
        features['bb_upper'] = sma_20 + (std_20 * 2)
        features['bb_lower'] = sma_20 - (std_20 * 2)
        features['bb_position'] = (price_data['close'] - features['bb_lower']) / (features['bb_upper'] - features['bb_lower'])
        
        # MACD
        ema_12 = price_data['close'].ewm(span=12).mean()
        ema_26 = price_data['close'].ewm(span=26).mean()
        features['macd'] = ema_12 - ema_26
        features['macd_signal'] = features['macd'].ewm(span=9).mean()
        features['macd_histogram'] = features['macd'] - features['macd_signal']
        
        # 거래량 지표
        features['volume_sma'] = price_data['volume'].rolling(20).mean()
        features['volume_ratio'] = price_data['volume'] / features['volume_sma']
        
        # 결측값 제거
        features = features.dropna()
        
        logger.info(f"✅ 피처 생성 완료: {features.shape}")
        return features
    
    def _print_results(self, result):
        """결과 출력"""
        if not result:
            logger.error("❌ 결과가 없습니다.")
            return
        
        metrics = result.performance_metrics
        
        print(f"\n🏆 공격적인 백테스팅 결과")
        print(f"=" * 60)
        print(f"💰 초기 자본: ${metrics['initial_capital']:,.2f}")
        print(f"💰 최종 자본: ${metrics['final_capital']:,.2f}")
        print(f"📈 총 수익률: {metrics['total_return']:+.2f}%")
        print(f"📊 총 거래: {metrics['total_trades']}회")
        print(f"🎯 승률: {metrics['win_rate']:.1f}% ({metrics['winning_trades']}승 {metrics['losing_trades']}패)")
        print(f"💚 평균 수익: ${metrics['avg_win']:+,.2f}")
        print(f"💔 평균 손실: ${metrics['avg_loss']:+,.2f}")
        print(f"💎 손익비: {metrics['profit_factor']:.2f}:1")
        print(f"📉 최대 낙폭: {metrics['max_drawdown']:.2f}%")
        print(f"⚡ 샤프 비율: {metrics['sharpe_ratio']:.2f}")
        
        # 성과 평가
        print(f"\n📊 성과 평가:")
        if metrics['total_return'] > 50:
            print(f"   🚀 우수한 성과!")
        elif metrics['total_return'] > 20:
            print(f"   📈 양호한 성과")
        elif metrics['total_return'] > 0:
            print(f"   😐 보통 성과")
        else:
            print(f"   😰 부진한 성과")
        
        if metrics['sharpe_ratio'] > 1.5:
            print(f"   ⚡ 우수한 위험 대비 수익률")
        elif metrics['sharpe_ratio'] > 1.0:
            print(f"   ⚡ 양호한 위험 대비 수익률")
        
        if metrics['max_drawdown'] < 10:
            print(f"   🛡️ 낮은 최대 낙폭")
        elif metrics['max_drawdown'] < 20:
            print(f"   🛡️ 적절한 최대 낙폭")
        else:
            print(f"   ⚠️ 높은 최대 낙폭")


def main():
    """메인 실행 함수"""
    print("🚀 Project LEVIATHAN - 사용자 설정 기반 공격적인 백테스팅")
    print("=" * 70)
    
    try:
        # 백테스터 초기화 및 실행
        runner = AggressiveBacktestRunner()
        result = runner.run_aggressive_backtest(
            start_date='2024-10-01',  # 훈련 데이터 이후
            end_date='2025-06-01'     # 현재까지
        )
        
        if result:
            print(f"\n🎯 다음 단계:")
            print(f"   - Walk-Forward 검증")
            print(f"   - 다른 시장 조건에서 테스트")
            print(f"   - 숏 모델 개발 후 듀얼 모델 백테스팅")
        
    except Exception as e:
        logger.error(f"❌ 메인 실행 실패: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
