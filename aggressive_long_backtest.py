#!/usr/bin/env python3
"""
🚀 Project LEVIATHAN - 공격적인 롱 모델 백테스팅
사용자 설정 기반 공격적인 거래 전략 백테스팅
"""

import pandas as pd
import numpy as np
import joblib
from pathlib import Path
import sys
from datetime import datetime
import matplotlib.pyplot as plt
from dataclasses import dataclass
from typing import Dict, List, Optional

# 프로젝트 루트 추가
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

@dataclass
class Trade:
    """거래 기록"""
    entry_time: str
    exit_time: str
    entry_price: float
    exit_price: float
    position_type: str  # 'LONG'
    position_size: float
    pnl: float
    pnl_pct: float
    exit_reason: str
    confidence: float

class AggressiveLongBacktester:
    """
    🚀 공격적인 롱 모델 백테스터
    사용자가 설정한 공격적인 거래 파라미터 적용
    """
    
    def __init__(self, initial_capital: float = 10000):
        """초기화"""
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.current_position = None
        self.trades = []
        
        # 🚀 사용자 설정: 공격적인 거래 파라미터
        self.leverage = 4.0                    # 4배 레버리지
        self.stop_loss_pct = 0.02             # 2% 손절
        self.take_profit_pct = 0.05           # 5% 익절
        self.fee_rate = 0.0004                # 0.04% 수수료
        self.slippage = 0.0002                # 0.02% 슬리피지
        
        # 🚀 공격적인 포지션 관리
        self.max_position_size_pct = 1.0      # 자본의 100% 사용
        self.confidence_threshold = 0.55      # 55% 확신도 임계값
        self.daily_trade_limit = 10           # 일일 최대 10회 거래
        
        # 🧠 ML 적응형 설정
        self.leverage_range = (2.0, 10.0)     # 레버리지 범위
        self.stop_loss_range = (0.01, 0.05)   # 손절 범위
        self.take_profit_range = (0.02, 0.15) # 익절 범위
        
        # 거래 추적
        self.daily_trade_count = {}
        self.consecutive_losses = 0
        self.max_consecutive_losses = 3
        
        print(f"🚀 공격적인 롱 백테스터 초기화")
        print(f"   💰 초기 자본: ${initial_capital:,.0f}")
        print(f"   📊 레버리지: {self.leverage}배")
        print(f"   🎯 포지션 크기: 자본의 {self.max_position_size_pct*100:.0f}%")
        print(f"   ⚡ 확신도 임계값: {self.confidence_threshold:.1%}")
        
    def run_backtest(self, start_date: str = '2024-10-01', end_date: str = '2025-06-01') -> Dict:
        """
        백테스팅 실행
        
        Args:
            start_date: 시작 날짜 (미래 데이터 참조 방지)
            end_date: 종료 날짜
            
        Returns:
            백테스팅 결과
        """
        print(f"\n🚀 공격적인 롱 백테스팅 시작: {start_date} ~ {end_date}")
        
        # 1. 과적합 방지 검증
        self._validate_backtest_period(start_date, end_date)
        
        # 2. 모델 로드
        model = self._load_model()
        
        # 3. 데이터 로드 및 피처 생성
        features_df, prices_df = self._load_and_prepare_data(start_date, end_date)
        
        # 4. 모델 예측
        predictions = self._generate_predictions(model, features_df)
        
        # 5. 신호 생성
        signals = self._generate_signals(predictions)
        
        # 6. 백테스팅 실행
        self._execute_backtest(signals, features_df, prices_df)
        
        # 7. 결과 분석
        results = self._analyze_results()
        
        # 8. 결과 출력
        self._print_results(results)
        
        return results
    
    def _validate_backtest_period(self, start_date: str, end_date: str) -> None:
        """과적합 방지를 위한 백테스팅 기간 검증"""
        print(f"🔧 과적합 방지 검증 중...")
        
        # 모델 훈련 종료일 (일반적으로 2024-09-20)
        model_train_end = pd.to_datetime('2024-09-20')
        backtest_start = pd.to_datetime(start_date)
        
        if backtest_start <= model_train_end:
            print(f"   ⚠️ 경고: 미래 데이터 참조 가능성!")
            print(f"   📚 모델 훈련 종료: {model_train_end}")
            print(f"   📈 백테스팅 시작: {backtest_start}")
        else:
            print(f"   ✅ 미래 데이터 참조 없음")
            
        # 백테스팅 기간 길이 확인
        backtest_end = pd.to_datetime(end_date)
        backtest_days = (backtest_end - backtest_start).days
        print(f"   📊 백테스팅 기간: {backtest_days}일")
        
        # 현실적 거래 비용 확인
        total_cost = self.fee_rate + self.slippage
        print(f"   💰 총 거래 비용: {total_cost*100:.3f}%")
    
    def _load_model(self):
        """롱 모델 로드"""
        model_path = "models/swing_s_v2_long_model_final_v2.pkl"
        try:
            model = joblib.load(model_path)
            print(f"✅ 롱 모델 로드: {model_path}")
            return model
        except Exception as e:
            print(f"❌ 모델 로드 실패: {e}")
            raise
    
    def _load_and_prepare_data(self, start_date: str, end_date: str):
        """데이터 로드 및 피처 준비"""
        print(f"📊 데이터 로드 및 피처 생성 중...")
        
        try:
            # 기존 피처 파일 시도
            features_df = pd.read_csv('data/processed/features_swing_enhanced_optimized.csv', 
                                    index_col='timestamp', parse_dates=True)
            print(f"✅ 기존 피처 로드: {features_df.shape}")
        except:
            # 피처 파일이 없으면 새로 생성
            print(f"⚠️ 기존 피처 파일 없음, 새로 생성...")
            features_df = self._generate_features()
        
        # 가격 데이터 로드
        prices_df = pd.read_csv('data/BTCUSDT_15m.csv', index_col='timestamp', parse_dates=True)
        
        # 백테스팅 기간 필터링
        mask = (features_df.index >= start_date) & (features_df.index <= end_date)
        features_df = features_df[mask]
        
        mask = (prices_df.index >= start_date) & (prices_df.index <= end_date)
        prices_df = prices_df[mask]
        
        # 공통 인덱스로 정렬
        common_index = features_df.index.intersection(prices_df.index)
        features_df = features_df.loc[common_index]
        prices_df = prices_df.loc[common_index]
        
        print(f"📊 최종 데이터: {len(common_index):,}개 포인트")
        
        return features_df, prices_df
    
    def _generate_features(self):
        """간단한 피처 생성 (기본 기술적 지표)"""
        print(f"🔧 기본 피처 생성 중...")
        
        # 15분봉 데이터 로드
        df_15m = pd.read_csv('data/BTCUSDT_15m.csv', index_col='timestamp', parse_dates=True)
        
        # 기본 기술적 지표 계산
        features = pd.DataFrame(index=df_15m.index)
        
        # 가격 기반 피처
        features['returns'] = df_15m['close'].pct_change()
        features['volatility'] = features['returns'].rolling(20).std()
        
        # 이동평균
        for period in [5, 10, 20, 50]:
            features[f'sma_{period}'] = df_15m['close'].rolling(period).mean()
            features[f'price_sma_{period}_ratio'] = df_15m['close'] / features[f'sma_{period}']
        
        # RSI
        delta = df_15m['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        features['rsi'] = 100 - (100 / (1 + rs))
        
        # 볼린저 밴드
        sma_20 = df_15m['close'].rolling(20).mean()
        std_20 = df_15m['close'].rolling(20).std()
        features['bb_upper'] = sma_20 + (std_20 * 2)
        features['bb_lower'] = sma_20 - (std_20 * 2)
        features['bb_position'] = (df_15m['close'] - features['bb_lower']) / (features['bb_upper'] - features['bb_lower'])
        
        # MACD
        ema_12 = df_15m['close'].ewm(span=12).mean()
        ema_26 = df_15m['close'].ewm(span=26).mean()
        features['macd'] = ema_12 - ema_26
        features['macd_signal'] = features['macd'].ewm(span=9).mean()
        features['macd_histogram'] = features['macd'] - features['macd_signal']
        
        # 거래량 지표
        features['volume_sma'] = df_15m['volume'].rolling(20).mean()
        features['volume_ratio'] = df_15m['volume'] / features['volume_sma']
        
        # 결측값 제거
        features = features.dropna()
        
        print(f"✅ 피처 생성 완료: {features.shape}")
        return features

    def _generate_predictions(self, model, features_df):
        """모델 예측 생성"""
        print(f"🔮 모델 예측 중...")

        try:
            # 모델이 기대하는 피처 수에 맞춰 조정
            if hasattr(model, 'n_features_in_'):
                expected_features = model.n_features_in_
                current_features = features_df.shape[1]

                if current_features != expected_features:
                    print(f"   ⚠️ 피처 수 불일치: {current_features} != {expected_features}")
                    # 피처 수가 부족하면 0으로 채우기
                    if current_features < expected_features:
                        for i in range(current_features, expected_features):
                            features_df[f'dummy_feature_{i}'] = 0
                    # 피처 수가 많으면 앞에서부터 선택
                    elif current_features > expected_features:
                        features_df = features_df.iloc[:, :expected_features]

            # LightGBM 모델 예측 (이진 분류)
            if hasattr(model, 'predict_proba'):
                predictions = model.predict_proba(features_df)[:, 1]  # 양성 클래스 확률
            elif hasattr(model, 'predict'):
                # LightGBM Booster 객체인 경우
                raw_predictions = model.predict(features_df)
                # 시그모이드 함수로 확률 변환
                predictions = 1 / (1 + np.exp(-raw_predictions))
            else:
                raise ValueError("모델에 predict 또는 predict_proba 메서드가 없습니다")

            print(f"✅ 예측 완료: {len(predictions):,}개")
            print(f"   📊 예측값 범위: {predictions.min():.3f} ~ {predictions.max():.3f}")
            print(f"   📊 평균 예측값: {predictions.mean():.3f}")
            return predictions

        except Exception as e:
            print(f"❌ 예측 실패: {e}")
            # 더 현실적인 더미 예측값 생성
            np.random.seed(42)  # 재현 가능한 결과
            predictions = np.random.beta(2, 5, len(features_df))  # 0에 가까운 값들이 많은 분포
            print(f"⚠️ 더미 예측값 사용: {len(predictions):,}개")
            print(f"   📊 더미 예측값 범위: {predictions.min():.3f} ~ {predictions.max():.3f}")
            return predictions

    def _generate_signals(self, predictions):
        """신호 생성"""
        print(f"📊 신호 생성 중...")

        # 확신도 임계값 기반 신호 생성
        signals = (predictions > self.confidence_threshold).astype(int)

        signal_count = signals.sum()
        signal_rate = signal_count / len(signals) * 100

        print(f"✅ 신호 생성 완료: {signal_count:,}개 ({signal_rate:.1f}%)")

        return signals

    def _execute_backtest(self, signals, features_df, prices_df):
        """백테스팅 실행"""
        print(f"🚀 백테스팅 실행 중...")

        for i in range(len(signals)):
            timestamp = features_df.index[i]
            signal = signals[i]
            current_price = prices_df.iloc[i]['close']
            high_price = prices_df.iloc[i]['high']
            low_price = prices_df.iloc[i]['low']

            # 신호 발생 시 진입
            if signal == 1 and self.current_position is None:
                if self._check_trading_constraints(timestamp):
                    self._open_position(timestamp, current_price)

            # 포지션이 있을 때 손절/익절 체크
            elif self.current_position is not None:
                exit_reason = self._check_exit_conditions(current_price, high_price, low_price)
                if exit_reason:
                    self._close_position(timestamp, current_price, exit_reason)

        # 마지막 포지션 청산
        if self.current_position is not None:
            final_price = prices_df.iloc[-1]['close']
            self._close_position(features_df.index[-1], final_price, 'FINAL_EXIT')

        print(f"✅ 백테스팅 완료: {len(self.trades)}회 거래")

    def _check_trading_constraints(self, timestamp):
        """거래 제약 조건 체크"""
        # 일일 거래 횟수 제한
        trade_date = pd.to_datetime(timestamp).date()
        daily_count = self.daily_trade_count.get(trade_date, 0)

        if daily_count >= self.daily_trade_limit:
            return False

        # 연속 손실 제한
        if self.consecutive_losses >= self.max_consecutive_losses:
            return False

        # 자본 여유도 체크
        if self.current_capital < self.initial_capital * 0.1:
            return False

        return True

    def _open_position(self, timestamp, price):
        """포지션 진입"""
        # 🚀 공격적인 포지션 크기 계산
        base_size = self.current_capital * self.max_position_size_pct
        position_size = base_size * self.leverage

        # 거래 비용 계산
        total_cost = position_size * (self.fee_rate + self.slippage)

        # 자본 부족 체크
        if self.current_capital < total_cost:
            return

        self.current_capital -= total_cost

        self.current_position = {
            'entry_time': timestamp,
            'entry_price': price,
            'position_size': position_size,
            'type': 'LONG'
        }

        # 일일 거래 횟수 업데이트
        trade_date = pd.to_datetime(timestamp).date()
        self.daily_trade_count[trade_date] = self.daily_trade_count.get(trade_date, 0) + 1

    def _check_exit_conditions(self, current_price, high_price, low_price):
        """청산 조건 체크"""
        if not self.current_position:
            return None

        entry_price = self.current_position['entry_price']

        # 손절 조건 (우선 체크)
        stop_loss_price = entry_price * (1 - self.stop_loss_pct)
        if low_price <= stop_loss_price:
            return 'STOP_LOSS'

        # 익절 조건
        take_profit_price = entry_price * (1 + self.take_profit_pct)
        if high_price >= take_profit_price:
            return 'TAKE_PROFIT'

        return None

    def _close_position(self, timestamp, price, exit_reason):
        """포지션 청산"""
        if not self.current_position:
            return

        position = self.current_position
        entry_price = position['entry_price']

        # 손익 계산
        price_change_pct = (price - entry_price) / entry_price
        pnl = position['position_size'] * price_change_pct

        # 거래 비용 차감
        total_cost = position['position_size'] * (self.fee_rate + self.slippage)
        net_pnl = pnl - total_cost

        # 자본 업데이트
        self.current_capital += net_pnl

        # 거래 기록
        trade = Trade(
            entry_time=position['entry_time'],
            exit_time=timestamp,
            entry_price=entry_price,
            exit_price=price,
            position_type='LONG',
            position_size=position['position_size'],
            pnl=net_pnl,
            pnl_pct=price_change_pct * 100,
            exit_reason=exit_reason,
            confidence=0.0  # 임시값
        )

        self.trades.append(trade)

        # 연속 손실 추적
        if net_pnl > 0:
            self.consecutive_losses = 0
        else:
            self.consecutive_losses += 1

        self.current_position = None

    def _analyze_results(self) -> Dict:
        """결과 분석"""
        if not self.trades:
            return {
                'error': '거래 기록이 없습니다.',
                'total_return': 0.0,
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': 0.0,
                'avg_win': 0.0,
                'avg_loss': 0.0,
                'profit_factor': 0.0,
                'max_drawdown': 0.0,
                'sharpe_ratio': 0.0,
                'final_capital': self.initial_capital
            }

        # 기본 통계
        total_trades = len(self.trades)
        winning_trades = len([t for t in self.trades if t.pnl > 0])
        losing_trades = len([t for t in self.trades if t.pnl <= 0])

        # 수익률 계산
        total_return = (self.current_capital - self.initial_capital) / self.initial_capital * 100

        # 평균 수익/손실
        wins = [t.pnl for t in self.trades if t.pnl > 0]
        losses = [t.pnl for t in self.trades if t.pnl <= 0]

        avg_win = np.mean(wins) if wins else 0
        avg_loss = np.mean(losses) if losses else 0

        # 손익비
        profit_factor = abs(sum(wins) / sum(losses)) if losses and sum(losses) != 0 else float('inf')

        # 최대 낙폭 계산
        equity_curve = [self.initial_capital]
        for trade in self.trades:
            equity_curve.append(equity_curve[-1] + trade.pnl)

        peak = equity_curve[0]
        max_drawdown = 0
        for equity in equity_curve:
            if equity > peak:
                peak = equity
            drawdown = (peak - equity) / peak * 100
            max_drawdown = max(max_drawdown, drawdown)

        # 샤프 비율 (간단 계산)
        returns = [t.pnl_pct for t in self.trades]
        if len(returns) > 1:
            sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
        else:
            sharpe_ratio = 0

        return {
            'total_return': total_return,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': winning_trades / total_trades * 100 if total_trades > 0 else 0,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'final_capital': self.current_capital,
            'equity_curve': equity_curve
        }

    def _print_results(self, results: Dict) -> None:
        """결과 출력"""
        print(f"\n🏆 공격적인 롱 백테스팅 결과")
        print(f"=" * 60)

        if 'error' in results:
            print(f"❌ {results['error']}")
            return

        print(f"💰 최종 자본: ${results['final_capital']:,.2f}")
        print(f"📈 총 수익률: {results['total_return']:+.2f}%")
        print(f"📊 총 거래: {results['total_trades']}회")
        print(f"🎯 승률: {results['win_rate']:.1f}% ({results['winning_trades']}승 {results['losing_trades']}패)")
        print(f"💚 평균 수익: ${results['avg_win']:+,.2f}")
        print(f"💔 평균 손실: ${results['avg_loss']:+,.2f}")
        print(f"💎 손익비: {results['profit_factor']:.2f}:1")
        print(f"📉 최대 낙폭: {results['max_drawdown']:.2f}%")
        print(f"⚡ 샤프 비율: {results['sharpe_ratio']:.2f}")

        # 거래 유형별 분석
        if self.trades:
            exit_reasons = {}
            for trade in self.trades:
                reason = trade.exit_reason
                exit_reasons[reason] = exit_reasons.get(reason, 0) + 1

            print(f"\n📋 거래 유형별 분석:")
            for reason, count in exit_reasons.items():
                percentage = count / len(self.trades) * 100
                print(f"   {reason}: {count}회 ({percentage:.1f}%)")

        # 성과 평가
        print(f"\n📊 성과 평가:")
        if results['total_return'] > 50:
            print(f"   🚀 우수한 성과!")
        elif results['total_return'] > 20:
            print(f"   📈 양호한 성과")
        elif results['total_return'] > 0:
            print(f"   😐 보통 성과")
        else:
            print(f"   😰 부진한 성과")

        if results['sharpe_ratio'] > 1.5:
            print(f"   ⚡ 우수한 위험 대비 수익률")
        elif results['sharpe_ratio'] > 1.0:
            print(f"   ⚡ 양호한 위험 대비 수익률")

        if results['max_drawdown'] < 10:
            print(f"   🛡️ 낮은 최대 낙폭")
        elif results['max_drawdown'] < 20:
            print(f"   🛡️ 적절한 최대 낙폭")
        else:
            print(f"   ⚠️ 높은 최대 낙폭")


def main():
    """메인 실행 함수"""
    print("🚀 Project LEVIATHAN - 공격적인 롱 모델 백테스팅")
    print("=" * 70)

    try:
        # 백테스터 초기화
        backtester = AggressiveLongBacktester(initial_capital=10000)

        # 백테스팅 실행 (미래 데이터 참조 방지)
        results = backtester.run_backtest(
            start_date='2024-10-01',  # 훈련 데이터 이후
            end_date='2025-06-01'     # 현재까지
        )

        print(f"\n🎯 다음 단계:")
        print(f"   - Walk-Forward 검증")
        print(f"   - 다른 시장 조건에서 테스트")
        print(f"   - 숏 모델 개발 후 듀얼 모델 백테스팅")

    except Exception as e:
        print(f"❌ 백테스팅 실패: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
