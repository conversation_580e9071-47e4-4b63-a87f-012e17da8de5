#!/usr/bin/env python3
"""
Project LEVIATHAN - 통합 전략 실행 스크립트
여러 전략(스윙, 단타 등)을 하나의 스크립트로 관리하는 범용 실행기

사용법:
    python run_strategy.py train --config=swing_s_v2
    python run_strategy.py backtest --config=daytrade_d_v1
    python run_strategy.py live --config=swing_s_v1
"""

import argparse
import sys
import os
from pathlib import Path
from typing import Optional, Dict, Any
import traceback

# 프로젝트 루트를 Python 경로에 추가
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 설정 관리자 import (통합된 config 폴더 사용)
from config import get_config, list_strategies, validate_strategy


class StrategyRunner:
    """전략 실행 관리자"""
    
    def __init__(self, config_name: str):
        """
        초기화
        
        Args:
            config_name: 사용할 전략 설정 이름 (예: swing_s_v2, daytrade_d_v1)
        """
        self.config_name = config_name
        self.config = get_config(config_name)
        
        print(f"🐋 Project LEVIATHAN - 전략 실행기")
        print(f"📋 선택된 전략: {self.config.strategy_name} {self.config.version}")
        print(f"📝 설명: {self.config.description}")
        print("=" * 70)
    
    def train_models(self) -> bool:
        """모델 훈련 실행"""
        print(f"🧠 {self.config.strategy_name} {self.config.version} 모델 훈련 시작...")
        
        try:
            # 설정에 따른 모델 훈련 로직
            if self.config.strategy_name == "LEVIATHAN-S":
                return self._train_swing_models()
            elif self.config.strategy_name == "LEVIATHAN-D":
                return self._train_daytrade_models()
            else:
                print(f"❌ 알 수 없는 전략: {self.config.strategy_name}")
                return False
                
        except Exception as e:
            print(f"❌ 모델 훈련 중 오류 발생: {e}")
            traceback.print_exc()
            return False
    
    def _train_swing_models(self) -> bool:
        """스윙 모델 훈련"""
        print(f"📊 스윙 모델 훈련 설정:")
        print(f"   - 시간 프레임: {self.config.execution_timeframe} + {self.config.trend_timeframe}")
        print(f"   - 최대 보유: {self.config.time_barrier_hours}시간")
        print(f"   - 손익절: -{self.config.stop_loss_pct*100:.1f}% / +{self.config.take_profit_pct*100:.1f}%")
        
        # 기존 모델 훈련 로직 호출
        try:
            from models.binary_model import BinaryTradingModel
            from models.short_model import ShortExpertModel
            
            # 롱 모델 훈련
            print("🔄 롱 모델 훈련 중...")
            long_model = BinaryTradingModel()
            long_model.train_binary_model(optimize=True, n_trials=30)
            long_model.save_model(self.config.get_model_paths()['long_model'])
            
            # 숏 모델 훈련
            print("🔄 숏 모델 훈련 중...")
            short_model = ShortExpertModel()
            short_model.train_short_model(optimize=True, n_trials=30)
            short_model.save_model(self.config.get_model_paths()['short_model'])
            
            print("✅ 스윙 모델 훈련 완료")
            return True
            
        except Exception as e:
            print(f"❌ 스윙 모델 훈련 실패: {e}")
            return False
    
    def _train_daytrade_models(self) -> bool:
        """단타 모델 훈련"""
        print(f"📊 단타 모델 훈련 설정:")
        print(f"   - 시간 프레임: {self.config.execution_timeframe} + {self.config.short_trend_timeframe}")
        print(f"   - 최대 보유: {self.config.time_barrier_hours}시간")
        print(f"   - 손익절: -{self.config.stop_loss_pct*100:.2f}% / +{self.config.take_profit_pct*100:.1f}%")
        
        # 단타용 모델 훈련 로직 (향후 구현)
        print("⚠️ 단타 모델 훈련 로직은 Phase 2에서 구현 예정")
        return True
    
    def run_backtest(self, start_date: Optional[str] = None, end_date: Optional[str] = None) -> bool:
        """ML 적응형 백테스팅 실행"""
        print(f"📈 {self.config.strategy_name} {self.config.version} ML 적응형 백테스팅 시작...")

        # 기본 날짜 설정
        if start_date is None:
            start_date = self.config.backtest_start_date
        if end_date is None:
            end_date = self.config.backtest_end_date

        print(f"📅 백테스팅 기간: {start_date} ~ {end_date}")

        # ML 적응형 설정 출력
        if hasattr(self.config, 'adaptive_take_profit') and self.config.adaptive_take_profit:
            print(f"🧠 ML 적응형 시스템 활성화:")
            print(f"   동적 레버리지: {self.config.min_leverage}-{self.config.max_leverage}배")
            print(f"   동적 손절: {self.config.min_stop_loss*100:.1f}-{self.config.max_stop_loss*100:.1f}%")
            print(f"   ML 동적 익절: {self.config.min_take_profit*100:.1f}-{self.config.max_take_profit*100:.1f}%")
            print(f"   시장별 적응: ✅")

        try:
            # ML 적응형 백테스터 초기화
            from backtesting.dual_model_backtest import DualModelBacktester

            backtester = DualModelBacktester(
                initial_capital=self.config.initial_capital
            )

            # ML 적응형 설정 적용
            backtester.leverage = self.config.leverage
            backtester.stop_loss_pct = self.config.stop_loss_pct
            backtester.take_profit_pct = self.config.take_profit_pct
            backtester.max_position_size_pct = self.config.max_position_size
            backtester.fee_rate = self.config.fee_rate
            backtester.slippage = self.config.slippage_rate

            # ML 적응형 설정 적용
            if hasattr(self.config, 'get_ml_adaptive_settings'):
                backtester.ml_adaptive_settings = self.config.get_ml_adaptive_settings()
                backtester.ml_adaptive_tp = getattr(self.config, 'adaptive_take_profit', False)
                print(f"🧠 ML 적응형 설정 적용 완료")

            results = backtester.run_backtest(
                start_date=start_date,
                end_date=end_date
            )

            # 결과 출력
            if results:
                print(f"\n🎉 ML 적응형 백테스팅 완료!")
                print(f"📊 최종 수익률: {results.get('total_return', 0):.2%}")
                print(f"📈 샤프 비율: {results.get('sharpe_ratio', 0):.3f}")
                print(f"📉 최대 낙폭: {results.get('max_drawdown', 0):.2%}")
                print(f"🎯 총 거래 수: {results.get('total_trades', 0):,}개")
                print(f"🏆 승률: {results.get('win_rate', 0):.1%}")

                # 결과 시각화
                backtester.plot_results(results)

            print("✅ 백테스팅 완료")
            return True

        except Exception as e:
            print(f"❌ 백테스팅 실패: {e}")
            traceback.print_exc()
            return False
    
    def run_walk_forward(self) -> bool:
        """Walk-Forward 검증 실행"""
        print(f"🔄 {self.config.strategy_name} {self.config.version} Walk-Forward 검증 시작...")
        
        try:
            from sliding_window_walk_forward import SlidingWindowWalkForward
            
            tester = SlidingWindowWalkForward(
                initial_capital=self.config.initial_capital,
                train_months=self.config.train_months,
                test_months=self.config.test_months,
                step_months=self.config.step_months
            )
            
            results = tester.run_sliding_window_test()
            
            print("✅ Walk-Forward 검증 완료")
            return True
            
        except Exception as e:
            print(f"❌ Walk-Forward 검증 실패: {e}")
            traceback.print_exc()
            return False
    
    def run_live_trading(self) -> bool:
        """실시간 거래 실행"""
        print(f"🚀 {self.config.strategy_name} {self.config.version} 실시간 거래 시작...")
        print("⚠️ 실시간 거래는 Phase 3에서 구현 예정")
        return True
    
    def print_config_summary(self):
        """설정 요약 출력"""
        self.config.print_summary()


def main():
    """메인 함수"""
    parser = argparse.ArgumentParser(
        description="Project LEVIATHAN 통합 전략 실행 스크립트",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
사용 예시:
  python run_strategy.py train --config=swing_s_v2
  python run_strategy.py backtest --config=swing_s_v1 --start=2023-01-01 --end=2024-12-31
  python run_strategy.py walkforward --config=daytrade_d_v1
  python run_strategy.py live --config=swing_s_v2
  python run_strategy.py info --config=swing_s_v1
  python run_strategy.py list
        """
    )
    
    parser.add_argument(
        'command',
        choices=['train', 'backtest', 'walkforward', 'live', 'info', 'list'],
        help='실행할 명령'
    )
    
    parser.add_argument(
        '--config',
        type=str,
        help='사용할 전략 설정 (예: swing_s_v2, daytrade_d_v1)'
    )
    
    parser.add_argument(
        '--start',
        type=str,
        help='백테스팅 시작 날짜 (YYYY-MM-DD)'
    )
    
    parser.add_argument(
        '--end',
        type=str,
        help='백테스팅 종료 날짜 (YYYY-MM-DD)'
    )
    
    args = parser.parse_args()
    
    # 전략 목록 출력
    if args.command == 'list':
        print("🐋 Project LEVIATHAN - 사용 가능한 전략들")
        print("=" * 50)
        for strategy in list_strategies():
            print(f"  📋 {strategy}")
        print(f"\n사용법: python run_strategy.py <command> --config=<strategy_name>")
        return
    
    # config 파라미터 필수 확인
    if args.config is None:
        print("❌ --config 파라미터가 필요합니다.")
        print(f"사용 가능한 전략: {list_strategies()}")
        return
    
    # 설정 유효성 검증
    if not validate_strategy(args.config):
        print(f"❌ 유효하지 않은 전략 설정: {args.config}")
        return
    
    # 전략 실행기 초기화
    runner = StrategyRunner(args.config)
    
    # 명령 실행
    if args.command == 'info':
        runner.print_config_summary()
    
    elif args.command == 'train':
        success = runner.train_models()
        if success:
            print(f"\n🎯 다음 단계: 백테스팅 실행")
            print(f"   python run_strategy.py backtest --config={args.config}")
    
    elif args.command == 'backtest':
        success = runner.run_backtest(args.start, args.end)
        if success:
            print(f"\n🎯 다음 단계: Walk-Forward 검증")
            print(f"   python run_strategy.py walkforward --config={args.config}")
    
    elif args.command == 'walkforward':
        success = runner.run_walk_forward()
        if success:
            print(f"\n🎯 다음 단계: 실시간 거래 (Phase 3)")
            print(f"   python run_strategy.py live --config={args.config}")
    
    elif args.command == 'live':
        runner.run_live_trading()


if __name__ == "__main__":
    main()
