"""
Project LEVIATHAN - Models Module
AI 기반 암호화폐 예측 모델 모듈
"""

__version__ = "5.0.0"
__author__ = "강현모"

from .target_generator import TargetGenerator
from .binary_model import BinaryTradingModel

# 존재하는 모듈만 import
try:
    from .hybrid_predictor import HybridPredictor
    HYBRID_AVAILABLE = True
except ImportError:
    HYBRID_AVAILABLE = False

__all__ = [
    "TargetGenerator",
    "BinaryTradingModel"
]

if HYBRID_AVAILABLE:
    __all__.append("HybridPredictor")
