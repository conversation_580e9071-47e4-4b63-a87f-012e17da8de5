"""
Project LEVIATHAN - Configuration Management
전역 설정 및 환경 변수 관리
"""

from pydantic_settings import BaseSettings
from pydantic import Field
from typing import Optional, Dict, Any
import os
from pathlib import Path


class TradingConfig(BaseSettings):
    """거래 관련 설정"""

    # 기본 거래 설정
    symbol: str = "BTC-USD"  # yfinance 호환 형식

    # 다중 시간 프레임 분석을 위한 중앙 설정
    timeframes: Dict[str, str] = {
        'EXECUTION': '15m',  # 진입/청산 신호 포착용 (15분봉)
        'TREND': '4h',       # 전체 시장 추세 판단용 (4시간봉)
    }

    # 레거시 호환성을 위한 기본 시간 프레임 (향후 제거 예정)
    timeframe: str = "15m"  # 실행 시간 프레임을 기본값으로 설정

    # === 전문가용 리스크 관리 설정 v2.0 (자본 보존 우선) ===

    # 레버리지 설정 (안정성을 위해 4배로 조정)
    leverage: int = 4

    # 손익절 설정 (자산 가격 변동률 기준)
    stop_loss_pct: float = 0.02   # -2% 가격 하락 시 손절 (노이즈 회피)
    take_profit_pct: float = 0.05  # +5% 가격 상승 시 익절 (손익비 2.5:1)

    # 포지션 관리
    max_position_size: float = 0.1  # 계좌 대비 최대 포지션 크기 (10%)

    # 거래 비용 설정
    fee_rate: float = 0.0004       # 시장가(Taker) 수수료: 0.04%
    slippage_rate: float = 0.0002  # 편도 슬리피지: 0.02%

    # 강제 청산 방지 (4배 레버리지 기준 조정)
    liquidation_buffer: float = 0.15  # 청산가 대비 15% 버퍼 (4배 레버리지에 맞춤)
    
    class Config:
        env_prefix = "TRADING_"


class DataConfig(BaseSettings):
    """데이터 관련 설정"""

    # 데이터 수집 기간 (학습 기간에 맞춰 조정)
    start_date: str = "2018-01-01"  # 데이터 수집 시작일 (학습 시작일과 동일)
    lookback_days: int = 1000  # 백테스팅용 데이터 기간
    feature_window: int = 20  # 피처 생성용 윈도우

    # 데이터 저장 경로
    data_dir: Path = Path("data")
    raw_data_dir: Path = data_dir / "raw"
    processed_data_dir: Path = data_dir / "processed"
    
    class Config:
        env_prefix = "DATA_"


class ModelConfig(BaseSettings):
    """모델 관련 설정"""
    
    # LightGBM 하이퍼파라미터
    lgb_params: Dict[str, Any] = {
        "objective": "binary",
        "metric": "binary_logloss",
        "boosting_type": "gbdt",
        "num_leaves": 31,
        "learning_rate": 0.05,
        "feature_fraction": 0.9,
        "bagging_fraction": 0.8,
        "bagging_freq": 5,
        "verbose": -1,
        "random_state": 42
    }
    
    # 모델 학습 설정
    train_test_split: float = 0.8
    validation_split: float = 0.2
    
    # 모델 저장 경로
    model_dir: Path = Path("models")
    
    class Config:
        env_prefix = "MODEL_"


class BacktestConfig(BaseSettings):
    """백테스팅 관련 설정"""

    # 초기 자본
    initial_capital: float = 100000.0  # $100,000

    # 거래 비용
    maker_fee: float = 0.0002  # 0.02%
    taker_fee: float = 0.0004  # 0.04%

    # Walk-Forward Analysis 설정 (다중 시간 프레임 최적화)
    train_start_date: str = "2018-01-01"  # 학습 시작일 (현대적 암호화폐 시장)
    train_end_date: str = "2022-12-31"    # 학습 종료일 (5년간 충분한 데이터)
    test_start_date: str = "2023-01-01"   # 백테스트 시작일 (최신 시장 환경)
    test_end_date: str = "2024-12-31"     # 백테스트 종료일 (2년간 검증)

    # 윈도우 전략 설정
    window_type: str = "hybrid"           # "rolling", "sliding", "hybrid"
    sliding_window_years: int = 5         # 슬라이딩 윈도우 크기 (년)
    retrain_frequency: str = "quarterly"  # 재학습 주기: "monthly", "quarterly", "yearly"
    min_train_samples: int = 1000         # 최소 학습 데이터 수

    # 시장 체제 변화 감지
    regime_change_threshold: float = 0.3  # 변화 감지 임계값
    volatility_lookback: int = 60         # 변동성 계산 기간 (일)

    class Config:
        env_prefix = "BACKTEST_"


class MonitoringConfig(BaseSettings):
    """모니터링 및 대시보드 설정"""
    
    # 대시보드 설정
    dashboard_port: int = 8501
    refresh_interval: int = 30  # 초 단위
    
    # 로깅 설정
    log_level: str = "INFO"
    log_dir: Path = Path("logs")
    
    # 알림 설정 (향후 Phase 3에서 사용)
    telegram_bot_token: Optional[str] = None
    telegram_chat_id: Optional[str] = None
    
    class Config:
        env_prefix = "MONITORING_"


class ExchangeConfig(BaseSettings):
    """거래소 API 설정 (Phase 2, 3에서 사용)"""
    
    # API 키 (환경변수에서 로드)
    api_key: Optional[str] = None
    api_secret: Optional[str] = None
    
    # 거래소 설정
    exchange_name: str = "binance"
    testnet: bool = True  # Phase 2에서는 테스트넷 사용
    
    class Config:
        env_prefix = "EXCHANGE_"


class LeviathanConfig(BaseSettings):
    """전체 시스템 설정을 통합하는 메인 클래스"""
    
    # 프로젝트 정보
    project_name: str = "LEVIATHAN"
    version: str = "1.0.0"
    phase: str = "1"  # 현재 개발 단계
    
    # 하위 설정들
    trading: TradingConfig = TradingConfig()
    data: DataConfig = DataConfig()
    model: ModelConfig = ModelConfig()
    backtest: BacktestConfig = BacktestConfig()
    monitoring: MonitoringConfig = MonitoringConfig()
    exchange: ExchangeConfig = ExchangeConfig()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 전역 설정 인스턴스
config = LeviathanConfig()

# ============================================================================
# 다중 시간 프레임 분석을 위한 중앙 설정 (리팩토링된 버전)
# ============================================================================

# 다중 시간 프레임 설정
TIMEFRAMES = {
    'EXECUTION': '15m',  # 실행용 타임프레임 (정밀한 진입/청산)
    'TREND': '4h',       # 추세 판단용 타임프레임 (시장 방향성)
}

# 기본 거래 설정 (바이낸스 API 사용)
TICKER = 'BTCUSDT'  # 바이낸스 심볼 형식
START_DATE = '2018-01-01'  # 데이터 수집 시작일 (바이낸스는 제한 없음)

# 데이터 파일 경로 설정 (CSV 기반 워크플로우)
DATA_PATHS = {
    '15m': f"data/{TICKER}_{TIMEFRAMES['EXECUTION']}.csv",  # 실행용 15분봉 데이터
    '4h': f"data/{TICKER}_{TIMEFRAMES['TREND']}.csv",       # 추세용 4시간봉 데이터
}

# 바이낸스 API 설정
BINANCE_CONFIG = {
    'base_url': 'https://api.binance.com',
    'rate_limit_per_minute': 1200,  # 바이낸스 API 제한
    'max_records_per_request': 1000,  # 한 번에 가져올 수 있는 최대 레코드 수
}

# === 전문가용 리스크 관리 상수 (자본 보존 우선 전략) ===

# 레버리지 설정 (안정성 우선)
LEVERAGE = 4

# 손익절 설정 (자산 가격 변동률 기준)
STOP_LOSS_PCT = 0.02   # -2% 가격 하락 시 손절 (노이즈 회피)
TAKE_PROFIT_PCT = 0.05  # +5% 가격 상승 시 익절 (손익비 2.5:1)

# 거래 비용 설정
FEE_RATE = 0.0004       # 시장가(Taker) 수수료: 0.04%
SLIPPAGE_RATE = 0.0002  # 편도 슬리피지: 0.02%

# 포지션 관리
MAX_POSITION_SIZE = 0.1  # 계좌 대비 최대 포지션 크기 (10%)

# 강제 청산 방지 (4배 레버리지 기준)
LIQUIDATION_BUFFER = 0.15  # 청산가 대비 15% 버퍼

# 리스크 관리 계산 결과
RISK_PER_TRADE = STOP_LOSS_PCT * LEVERAGE  # 거래당 최대 손실: 8%
REWARD_PER_TRADE = TAKE_PROFIT_PCT * LEVERAGE  # 거래당 기대 수익: 20%
RISK_REWARD_RATIO = REWARD_PER_TRADE / RISK_PER_TRADE  # 손익비: 2.5:1
BREAKEVEN_WIN_RATE = 1 / (1 + RISK_REWARD_RATIO)  # 손익분기점 승률: 28.6%

# === 트리플 배리어 메소드 설정 ===

# 최대 포지션 보유 기간 (15분봉 기준)
MAX_HOLD_PERIOD = 16  # 15분봉 * 16 = 4시간 (수직 장벽)

# 확률 임계값 설정 (신호 생성용)
PROBABILITY_THRESHOLD = 0.6  # 60% 이상 확신할 때만 거래 신호 생성


def setup_directories():
    """필요한 디렉토리들을 생성"""
    directories = [
        config.data.data_dir,
        config.data.raw_data_dir,
        config.data.processed_data_dir,
        config.model.model_dir,
        config.monitoring.log_dir,
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)


if __name__ == "__main__":
    # 설정 테스트
    setup_directories()
    print(f"🐋 Project: {config.project_name} v{config.version}")
    print(f"📊 Current Phase: {config.phase}")
    print(f"💰 Trading Symbol: {config.trading.symbol}")
    print(f"💵 Initial Capital: ${config.backtest.initial_capital:,.2f}")

    print(f"\n🛡️ 전문가용 리스크 관리 설정:")
    print(f"   ⚖️ 레버리지: {LEVERAGE}x")
    print(f"   📉 손절매: -{STOP_LOSS_PCT*100:.1f}%")
    print(f"   📈 익절: +{TAKE_PROFIT_PCT*100:.1f}%")
    print(f"   💸 수수료: {FEE_RATE*100:.2f}%")
    print(f"   🌊 슬리피지: {SLIPPAGE_RATE*100:.2f}%")

    print(f"\n📊 리스크 분석:")
    print(f"   🎯 손익비: {RISK_REWARD_RATIO:.1f}:1")
    print(f"   🎲 손익분기점 승률: {BREAKEVEN_WIN_RATE*100:.1f}%")
    print(f"   💥 거래당 최대 손실: {RISK_PER_TRADE*100:.1f}%")
    print(f"   🚀 거래당 기대 수익: {REWARD_PER_TRADE*100:.1f}%")
