"""
Project LEVIATHAN - CSV 기반 피처 엔지니어링
로컬 CSV 파일로부터 데이터를 읽어 다중 시간 프레임 피처를 생성합니다.
더 이상 API 호출을 하지 않으므로 빠르고 안정적입니다.
"""

import pandas as pd
import numpy as np
import os
import sys
from pathlib import Path

# 프로젝트 루트를 Python 경로에 추가
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 🎯 멀티 타임프레임 설정 (15분+4시간+1일+1주)
DATA_PATHS = {
    '15m': 'data/BTCUSDT_15m.csv',
    '4h': 'data/BTCUSDT_4h.csv',
    '1d': 'data/BTCUSDT_1D.csv',
    '1w': 'data/BTCUSDT_1W.csv'
}


def calculate_rsi(prices, period=14):
    """RSI (Relative Strength Index) 계산"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi


def calculate_macd(prices, fast=12, slow=26, signal=9):
    """MACD 계산"""
    ema_fast = prices.ewm(span=fast).mean()
    ema_slow = prices.ewm(span=slow).mean()
    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=signal).mean()
    histogram = macd_line - signal_line
    return macd_line, signal_line, histogram


def calculate_bollinger_bands(prices, period=20, std_dev=2):
    """볼린저 밴드 계산"""
    ma = prices.rolling(window=period).mean()
    std = prices.rolling(window=period).std()
    upper_band = ma + (std * std_dev)
    lower_band = ma - (std * std_dev)
    bb_width = (upper_band - lower_band) / ma
    bb_position = (prices - lower_band) / (upper_band - lower_band)
    return upper_band, ma, lower_band, bb_width, bb_position


def generate_features_from_csv():
    """
    🎯 멀티 타임프레임 피처 생성 (15분+4시간+1일+1주)
    로컬 CSV 파일로부터 데이터를 읽어 다중 시간 프레임 피처를 생성합니다.

    Returns:
        pd.DataFrame: 최종 피처 데이터프레임
    """
    # 1. 모든 타임프레임 데이터 파일 존재 확인
    for timeframe, path in DATA_PATHS.items():
        if not os.path.exists(path):
            raise FileNotFoundError(
                f"데이터 파일이 존재하지 않습니다: {path}\n"
                "data/collector.py를 먼저 실행하세요."
            )

    print("📂 멀티 타임프레임 CSV 파일에서 데이터를 로드합니다...")

    # 2. 모든 타임프레임 데이터 로드
    dataframes = {}
    for timeframe, path in DATA_PATHS.items():
        df = pd.read_csv(path, index_col='timestamp', parse_dates=True)
        dataframes[timeframe] = df
        print(f"   ✅ {timeframe} 데이터: {len(df)}개 레코드 ({df.index[0]} ~ {df.index[-1]})")

    # 기본 데이터프레임들
    df_exec = dataframes['15m']  # 실행용 (15분)
    df_trend = dataframes['4h']  # 추세용 (4시간)
    df_daily = dataframes['1d']  # 일봉
    df_weekly = dataframes['1w'] # 주봉

    # 3. 멀티 타임프레임 피처 계산
    print("📊 멀티 타임프레임 피처를 계산합니다...")

    # 3.1 4시간봉 추세 피처 (기존)
    print("   🔄 4시간봉 추세 피처 계산...")
    df_trend['SMA_50_trend'] = df_trend['close'].rolling(window=50).mean()
    df_trend['RSI_14_trend'] = calculate_rsi(df_trend['close'], 14)

    macd, macd_signal, macd_hist = calculate_macd(df_trend['close'])
    df_trend['MACD_trend'] = macd
    df_trend['MACD_signal_trend'] = macd_signal

    bb_upper, bb_middle, bb_lower, bb_width, bb_position = calculate_bollinger_bands(df_trend['close'])
    df_trend['BB_position_trend'] = bb_position
    df_trend['BB_width_trend'] = bb_width

    trend_features = df_trend[['SMA_50_trend', 'RSI_14_trend', 'MACD_trend',
                              'MACD_signal_trend', 'BB_position_trend', 'BB_width_trend']]

    # 3.2 1일봉 장기 추세 피처 (신규)
    print("   📅 1일봉 장기 추세 피처 계산...")
    df_daily['SMA_20_daily'] = df_daily['close'].rolling(window=20).mean()
    df_daily['SMA_50_daily'] = df_daily['close'].rolling(window=50).mean()
    df_daily['RSI_14_daily'] = calculate_rsi(df_daily['close'], 14)

    macd_daily, macd_signal_daily, _ = calculate_macd(df_daily['close'])
    df_daily['MACD_daily'] = macd_daily
    df_daily['MACD_signal_daily'] = macd_signal_daily

    # 일봉 추세 방향
    df_daily['Trend_direction_daily'] = (df_daily['SMA_20_daily'] > df_daily['SMA_50_daily']).astype(int)

    daily_features = df_daily[['SMA_20_daily', 'SMA_50_daily', 'RSI_14_daily',
                              'MACD_daily', 'MACD_signal_daily', 'Trend_direction_daily']]

    # 3.3 1주봉 초장기 추세 피처 (신규)
    print("   📊 1주봉 초장기 추세 피처 계산...")
    df_weekly['SMA_10_weekly'] = df_weekly['close'].rolling(window=10).mean()
    df_weekly['SMA_20_weekly'] = df_weekly['close'].rolling(window=20).mean()
    df_weekly['RSI_14_weekly'] = calculate_rsi(df_weekly['close'], 14)

    # 주봉 추세 강도
    df_weekly['Trend_strength_weekly'] = (df_weekly['close'] / df_weekly['SMA_20_weekly'] - 1) * 100

    # 주봉 변동성
    df_weekly['Volatility_weekly'] = df_weekly['close'].rolling(window=4).std() / df_weekly['close'].rolling(window=4).mean()

    weekly_features = df_weekly[['SMA_10_weekly', 'SMA_20_weekly', 'RSI_14_weekly',
                                'Trend_strength_weekly', 'Volatility_weekly']]

    print(f"   ✅ 4시간봉 피처: {len(trend_features.columns)}개")
    print(f"   ✅ 1일봉 피처: {len(daily_features.columns)}개")
    print(f"   ✅ 1주봉 피처: {len(weekly_features.columns)}개")

    # 4. 멀티 타임프레임 데이터 병합
    print("🔗 멀티 타임프레임 데이터를 15분봉에 병합합니다...")

    # 4.1 15분봉 기준으로 시작
    merged_df = df_exec.reset_index()

    # 4.2 4시간봉 피처 병합
    print("   🔄 4시간봉 피처 병합...")
    trend_features_reset = trend_features.reset_index()
    merged_df = pd.merge_asof(
        left=merged_df.sort_values('timestamp'),
        right=trend_features_reset.sort_values('timestamp'),
        on='timestamp',
        direction='backward'
    )

    # 4.3 1일봉 피처 병합
    print("   📅 1일봉 피처 병합...")
    daily_features_reset = daily_features.reset_index()
    merged_df = pd.merge_asof(
        left=merged_df.sort_values('timestamp'),
        right=daily_features_reset.sort_values('timestamp'),
        on='timestamp',
        direction='backward'
    )

    # 4.4 1주봉 피처 병합
    print("   📊 1주봉 피처 병합...")
    weekly_features_reset = weekly_features.reset_index()
    merged_df = pd.merge_asof(
        left=merged_df.sort_values('timestamp'),
        right=weekly_features_reset.sort_values('timestamp'),
        on='timestamp',
        direction='backward'
    )

    # 인덱스 복원
    merged_df.set_index('timestamp', inplace=True)

    print(f"   ✅ 멀티 타임프레임 병합 완료: {len(merged_df)}개 레코드")

    total_timeframe_features = len(trend_features.columns) + len(daily_features.columns) + len(weekly_features.columns)
    print(f"   📊 총 타임프레임 피처: {total_timeframe_features}개")

    # 5. 15분봉 실행 피처 계산
    print("⚡ 15분봉 실행 피처를 계산합니다...")

    # 5.1 기본 실행 피처들
    merged_df['SMA_20_exec'] = merged_df['close'].rolling(window=20).mean()
    merged_df['SMA_5_exec'] = merged_df['close'].rolling(window=5).mean()
    merged_df['RSI_14_exec'] = calculate_rsi(merged_df['close'], 14)

    macd_exec, macd_signal_exec, macd_hist_exec = calculate_macd(merged_df['close'])
    merged_df['MACD_exec'] = macd_exec
    merged_df['MACD_signal_exec'] = macd_signal_exec
    merged_df['MACD_histogram_exec'] = macd_hist_exec

    bb_upper_exec, bb_middle_exec, bb_lower_exec, bb_width_exec, bb_position_exec = calculate_bollinger_bands(merged_df['close'])
    merged_df['BB_upper_exec'] = bb_upper_exec
    merged_df['BB_middle_exec'] = bb_middle_exec
    merged_df['BB_lower_exec'] = bb_lower_exec
    merged_df['BB_width_exec'] = bb_width_exec
    merged_df['BB_position_exec'] = bb_position_exec

    # 거래량 피처
    merged_df['Volume_MA_exec'] = merged_df['volume'].rolling(window=20).mean()
    merged_df['Volume_ratio_exec'] = merged_df['volume'] / merged_df['Volume_MA_exec']

    # 5.2 멀티 타임프레임 상호작용 피처 (확장)
    print("   🧠 멀티 타임프레임 상호작용 피처 생성...")

    # 기존 상호작용 피처
    merged_df['MA_above_trend'] = (merged_df['SMA_20_exec'] > merged_df['SMA_50_trend']).astype(int)
    merged_df['RSI_divergence'] = merged_df['RSI_14_exec'] - merged_df['RSI_14_trend']

    # 🎯 새로운 멀티 타임프레임 상호작용 피처
    # 일봉 추세와 15분봉 신호 조합
    merged_df['Daily_trend_15m_signal'] = merged_df['Trend_direction_daily'] * merged_df['RSI_14_exec']

    # 주봉 추세 강도와 4시간 추세 조합
    merged_df['Weekly_strength_4h_trend'] = merged_df['Trend_strength_weekly'] * merged_df['RSI_14_trend']

    # 멀티 타임프레임 RSI 평균
    merged_df['RSI_multi_timeframe'] = (
        merged_df['RSI_14_exec'] * 0.4 +      # 15분 40%
        merged_df['RSI_14_trend'] * 0.3 +     # 4시간 30%
        merged_df['RSI_14_daily'] * 0.2 +     # 1일 20%
        merged_df['RSI_14_weekly'] * 0.1      # 1주 10%
    )

    print(f"   ✅ 15분봉 실행 피처 계산 완료")
    print(f"   ✅ 멀티 타임프레임 상호작용 피처 추가 완료")

    # 5. 최종 데이터프레임 정리
    print("🧹 최종 데이터프레임을 정리합니다...")

    # 원본 OHLCV 컬럼 제거 (피처만 남김) - 바이낸스는 소문자 컬럼명 사용
    feature_columns = [col for col in merged_df.columns
                      if col not in ['open', 'high', 'low', 'close', 'volume']]

    final_df = merged_df[feature_columns].dropna()

    # 🧠 상호작용 피처 생성 (모델 지능 강화)
    print("🧠 상호작용 피처를 생성합니다...")
    try:
        from data.interaction_features import InteractionFeatureGenerator

        generator = InteractionFeatureGenerator()
        final_df = generator.generate_all_interaction_features(final_df)
        interaction_count = len(generator.feature_names)

        print(f"   ✅ 상호작용 피처 {interaction_count}개 추가 완료")

    except Exception as e:
        print(f"   ⚠️ 상호작용 피처 생성 실패: {e}")
        interaction_count = 0

    print(f"✅ 피처 엔지니어링 완료!")
    print(f"   최종 데이터 형태: {final_df.shape}")
    print(f"   포함된 피처: {len(final_df.columns)}개")

    # 피처 타입별 분류 (멀티 타임프레임)
    exec_features = [col for col in final_df.columns if col.endswith('_exec')]
    trend_features = [col for col in final_df.columns if col.endswith('_trend')]
    daily_features = [col for col in final_df.columns if col.endswith('_daily')]
    weekly_features = [col for col in final_df.columns if col.endswith('_weekly')]

    # 상호작용 피처들
    interaction_features = [col for col in final_df.columns
                          if not any(col.endswith(suffix) for suffix in ['_exec', '_trend', '_daily', '_weekly'])]

    print(f"   ⚡ 15분봉 실행 피처: {len(exec_features)}개")
    print(f"   🔄 4시간봉 추세 피처: {len(trend_features)}개")
    print(f"   � 1일봉 장기 피처: {len(daily_features)}개")
    print(f"   📊 1주봉 초장기 피처: {len(weekly_features)}개")
    print(f"   🧠 상호작용 피처: {len(interaction_features)}개")

    total_timeframe_features = len(exec_features) + len(trend_features) + len(daily_features) + len(weekly_features)
    print(f"   🎯 총 타임프레임 피처: {total_timeframe_features}개")
    print(f"   🎯 총 상호작용 피처: {len(interaction_features)}개")

    return final_df


if __name__ == '__main__':
    """
    메인 실행 부분: CSV 파일 기반 피처 엔지니어링 테스트
    """
    print("🚀 Project LEVIATHAN - CSV 기반 피처 엔지니어링")
    print("=" * 60)

    try:
        # CSV 기반 피처 생성 실행
        final_features = generate_features_from_csv()

        print(f"\n📋 최종 생성된 피처 데이터 샘플:")
        print(final_features.head())

        print(f"\n📊 피처 목록:")
        for i, col in enumerate(final_features.columns, 1):
            print(f"   {i:2d}. {col}")

        print(f"\n✅ CSV 기반 피처 엔지니어링이 성공적으로 완료되었습니다!")
        print(f"   📈 이제 API 호출 없이 빠른 피처 생성이 가능합니다.")
        print(f"   🎯 다음 단계: LightGBM 모델에 이 피처들을 적용하세요.")

    except FileNotFoundError as e:
        print(f"❌ 파일 오류: {e}")
        print(f"\n📝 해결 방법:")
        print(f"   1. python data/collector.py 실행하여 데이터를 먼저 다운로드하세요")
        print(f"   2. 다운로드 완료 후 다시 이 스크립트를 실행하세요")

    except Exception as e:
        print(f"❌ 오류 발생: {e}")
        import traceback
        traceback.print_exc()
