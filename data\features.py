"""
Project LEVIATHAN - CSV 기반 피처 엔지니어링
로컬 CSV 파일로부터 데이터를 읽어 다중 시간 프레임 피처를 생성합니다.
더 이상 API 호출을 하지 않으므로 빠르고 안정적입니다.
"""

import pandas as pd
import numpy as np
import os
import sys
from pathlib import Path

# 프로젝트 루트를 Python 경로에 추가
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 간단한 설정 (하드코딩)
DATA_PATHS = {
    '15m': 'data/BTCUSDT_15m.csv',
    '4h': 'data/BTCUSDT_4h.csv'
}


def calculate_rsi(prices, period=14):
    """RSI (Relative Strength Index) 계산"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi


def calculate_macd(prices, fast=12, slow=26, signal=9):
    """MACD 계산"""
    ema_fast = prices.ewm(span=fast).mean()
    ema_slow = prices.ewm(span=slow).mean()
    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=signal).mean()
    histogram = macd_line - signal_line
    return macd_line, signal_line, histogram


def calculate_bollinger_bands(prices, period=20, std_dev=2):
    """볼린저 밴드 계산"""
    ma = prices.rolling(window=period).mean()
    std = prices.rolling(window=period).std()
    upper_band = ma + (std * std_dev)
    lower_band = ma - (std * std_dev)
    bb_width = (upper_band - lower_band) / ma
    bb_position = (prices - lower_band) / (upper_band - lower_band)
    return upper_band, ma, lower_band, bb_width, bb_position


def generate_features_from_csv():
    """
    로컬 CSV 파일로부터 데이터를 읽어 다중 시간 프레임 피처를 생성합니다.

    Returns:
        pd.DataFrame: 최종 피처 데이터프레임
    """
    # 1. 설정된 경로에서 CSV 파일 읽기
    exec_path = DATA_PATHS['15m']
    trend_path = DATA_PATHS['4h']

    if not os.path.exists(exec_path) or not os.path.exists(trend_path):
        raise FileNotFoundError(
            "데이터 파일이 존재하지 않습니다. data/collector.py를 먼저 실행하세요.\n"
            f"필요한 파일:\n  - {exec_path}\n  - {trend_path}"
        )

    print("📂 로컬 CSV 파일에서 데이터를 로드합니다...")

    # 바이낸스 데이터는 'timestamp' 인덱스를 사용
    df_exec = pd.read_csv(exec_path, index_col='timestamp', parse_dates=True)
    df_trend = pd.read_csv(trend_path, index_col='timestamp', parse_dates=True)

    print(f"   ✅ 15분봉 데이터: {len(df_exec)}개 레코드 ({df_exec.index[0]} ~ {df_exec.index[-1]})")
    print(f"   ✅ 4시간봉 데이터: {len(df_trend)}개 레코드 ({df_trend.index[0]} ~ {df_trend.index[-1]})")

    # 2. 4시간봉 데이터프레임에서 추세 피처 계산
    print("📊 추세 피처를 계산합니다 (4h)...")

    # 추세 피처들 (바이낸스 데이터는 소문자 컬럼명 사용)
    df_trend['SMA_50_trend'] = df_trend['close'].rolling(window=50).mean()
    df_trend['RSI_14_trend'] = calculate_rsi(df_trend['close'], 14)

    macd, macd_signal, macd_hist = calculate_macd(df_trend['close'])
    df_trend['MACD_trend'] = macd
    df_trend['MACD_signal_trend'] = macd_signal

    bb_upper, bb_middle, bb_lower, bb_width, bb_position = calculate_bollinger_bands(df_trend['close'])
    df_trend['BB_position_trend'] = bb_position
    df_trend['BB_width_trend'] = bb_width

    # 추세 피처만 선택
    trend_features = df_trend[['SMA_50_trend', 'RSI_14_trend', 'MACD_trend',
                              'MACD_signal_trend', 'BB_position_trend', 'BB_width_trend']]

    print(f"   ✅ 추세 피처 {len(trend_features.columns)}개 계산 완료")

    # 3. pd.merge_asof를 사용하여 두 데이터프레임 병합
    print("🔗 실행 데이터에 추세 피처를 병합합니다...")

    # 인덱스를 리셋하여 merge_asof 사용 준비
    df_exec_reset = df_exec.reset_index()
    trend_features_reset = trend_features.reset_index()

    # 시간 순서로 정렬
    df_exec_reset = df_exec_reset.sort_values('timestamp')
    trend_features_reset = trend_features_reset.sort_values('timestamp')

    # merge_asof로 병합 (이것이 다중 시간 프레임 분석의 핵심!)
    merged_df = pd.merge_asof(
        left=df_exec_reset,
        right=trend_features_reset,
        on='timestamp',
        direction='backward'  # 이전 시간의 가장 최신 추세 데이터를 사용
    )

    # 인덱스 복원
    merged_df.set_index('timestamp', inplace=True)

    print(f"   ✅ 병합 완료: {len(merged_df)}개 레코드")

    # 4. 병합된 데이터에서 15분봉 실행 피처 계산
    print("⚡ 실행 피처를 계산합니다 (15m)...")

    # 실행 피처들 (바이낸스 데이터는 소문자 컬럼명 사용)
    merged_df['SMA_20_exec'] = merged_df['close'].rolling(window=20).mean()
    merged_df['SMA_5_exec'] = merged_df['close'].rolling(window=5).mean()
    merged_df['RSI_14_exec'] = calculate_rsi(merged_df['close'], 14)

    macd_exec, macd_signal_exec, macd_hist_exec = calculate_macd(merged_df['close'])
    merged_df['MACD_exec'] = macd_exec
    merged_df['MACD_signal_exec'] = macd_signal_exec
    merged_df['MACD_histogram_exec'] = macd_hist_exec

    bb_upper_exec, bb_middle_exec, bb_lower_exec, bb_width_exec, bb_position_exec = calculate_bollinger_bands(merged_df['close'])
    merged_df['BB_upper_exec'] = bb_upper_exec
    merged_df['BB_middle_exec'] = bb_middle_exec
    merged_df['BB_lower_exec'] = bb_lower_exec
    merged_df['BB_width_exec'] = bb_width_exec
    merged_df['BB_position_exec'] = bb_position_exec

    # 거래량 피처
    merged_df['Volume_MA_exec'] = merged_df['volume'].rolling(window=20).mean()
    merged_df['Volume_ratio_exec'] = merged_df['volume'] / merged_df['Volume_MA_exec']

    # 상호작용 피처 (추세 + 실행)
    merged_df['MA_above_trend'] = (merged_df['SMA_20_exec'] > merged_df['SMA_50_trend']).astype(int)
    merged_df['RSI_divergence'] = merged_df['RSI_14_exec'] - merged_df['RSI_14_trend']

    print(f"   ✅ 실행 피처 계산 완료")

    # 5. 최종 데이터프레임 정리
    print("🧹 최종 데이터프레임을 정리합니다...")

    # 원본 OHLCV 컬럼 제거 (피처만 남김) - 바이낸스는 소문자 컬럼명 사용
    feature_columns = [col for col in merged_df.columns
                      if col not in ['open', 'high', 'low', 'close', 'volume']]

    final_df = merged_df[feature_columns].dropna()

    # 🧠 상호작용 피처 생성 (모델 지능 강화)
    print("🧠 상호작용 피처를 생성합니다...")
    try:
        from data.interaction_features import InteractionFeatureGenerator

        generator = InteractionFeatureGenerator()
        final_df = generator.generate_all_interaction_features(final_df)
        interaction_count = len(generator.feature_names)

        print(f"   ✅ 상호작용 피처 {interaction_count}개 추가 완료")

    except Exception as e:
        print(f"   ⚠️ 상호작용 피처 생성 실패: {e}")
        interaction_count = 0

    print(f"✅ 피처 엔지니어링 완료!")
    print(f"   최종 데이터 형태: {final_df.shape}")
    print(f"   포함된 피처: {len(final_df.columns)}개")

    # 피처 타입별 분류
    trend_features = [col for col in final_df.columns if col.endswith('_trend')]
    exec_features = [col for col in final_df.columns if col.endswith('_exec')]

    # 새로운 상호작용 피처들
    new_interaction_features = ['Trend_Momentum_Alignment', 'Volume_Price_Momentum', 'Volatility_Trend_Filter']
    existing_interaction_features = [col for col in final_df.columns
                                   if not (col.endswith('_trend') or col.endswith('_exec'))
                                   and col not in new_interaction_features]

    total_interaction_features = len(existing_interaction_features) + len([col for col in new_interaction_features if col in final_df.columns])

    print(f"   📊 추세 피처: {len(trend_features)}개")
    print(f"   ⚡ 실행 피처: {len(exec_features)}개")
    print(f"   🔗 기존 상호작용 피처: {len(existing_interaction_features)}개")
    print(f"   🧠 새로운 상호작용 피처: {len([col for col in new_interaction_features if col in final_df.columns])}개")
    print(f"   🎯 총 상호작용 피처: {total_interaction_features}개")

    return final_df


if __name__ == '__main__':
    """
    메인 실행 부분: CSV 파일 기반 피처 엔지니어링 테스트
    """
    print("🚀 Project LEVIATHAN - CSV 기반 피처 엔지니어링")
    print("=" * 60)

    try:
        # CSV 기반 피처 생성 실행
        final_features = generate_features_from_csv()

        print(f"\n📋 최종 생성된 피처 데이터 샘플:")
        print(final_features.head())

        print(f"\n📊 피처 목록:")
        for i, col in enumerate(final_features.columns, 1):
            print(f"   {i:2d}. {col}")

        print(f"\n✅ CSV 기반 피처 엔지니어링이 성공적으로 완료되었습니다!")
        print(f"   📈 이제 API 호출 없이 빠른 피처 생성이 가능합니다.")
        print(f"   🎯 다음 단계: LightGBM 모델에 이 피처들을 적용하세요.")

    except FileNotFoundError as e:
        print(f"❌ 파일 오류: {e}")
        print(f"\n📝 해결 방법:")
        print(f"   1. python data/collector.py 실행하여 데이터를 먼저 다운로드하세요")
        print(f"   2. 다운로드 완료 후 다시 이 스크립트를 실행하세요")

    except Exception as e:
        print(f"❌ 오류 발생: {e}")
        import traceback
        traceback.print_exc()
