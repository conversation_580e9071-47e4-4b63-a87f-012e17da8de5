"""
Project LEVIATHAN - 숏 모델만 훈련
35개 멀티 타임프레임 피처로 숏 모델만 훈련
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, roc_auc_score
import joblib
import warnings
warnings.filterwarnings('ignore')

import sys
from pathlib import Path

# 프로젝트 루트를 Python 경로에 추가
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def main():
    """숏 모델만 훈련"""
    print("🔻 Project LEVIATHAN - 숏 모델 전용 훈련")
    print("=" * 50)
    print("📊 35개 멀티 타임프레임 피처")
    print("🎯 숏 모델만 훈련")
    print("⏰ 48시간 보유기간")
    print("=" * 50)
    
    try:
        # 1. 멀티 타임프레임 피처 로드
        print("\n📂 멀티 타임프레임 피처 로드 중...")
        from data.features import generate_features_from_csv
        features_df = generate_features_from_csv()
        print(f"✅ 피처 로드 완료: {features_df.shape}")
        
        # 2. 숏 타겟 생성
        print(f"\n🎯 숏 타겟 생성 중...")
        from models.short_target_generator import ShortTargetGenerator
        generator = ShortTargetGenerator()
        
        # 가격 데이터 로드
        price_data = pd.read_csv('data/BTCUSDT_15m.csv', index_col='timestamp', parse_dates=True)
        common_index = features_df.index.intersection(price_data.index)
        price_data = price_data.loc[common_index]
        
        # 숏 타겟 생성
        targets = generator.generate_short_target(price_data)
        targets = targets.loc[features_df.index]
        
        print(f"✅ 숏 타겟 생성 완료: {targets.shape}")
        print(f"📊 타겟 분포:")
        print(targets.value_counts().sort_index())
        
        # 3. 데이터 준비
        print(f"\n🤖 숏 모델 훈련 준비...")
        valid_idx = targets.notna() & features_df.notna().all(axis=1)
        X = features_df[valid_idx]
        y = targets[valid_idx]
        
        # LightGBM용 라벨 변환: -1,0,1 → 0,1,2
        y = y + 1
        print(f"📊 변환된 라벨 분포:")
        print(y.value_counts().sort_index())
        
        print(f"📊 유효 데이터: {len(X)}개 샘플")
        print(f"📊 피처 수: {X.shape[1]}개")
        
        # 4. 훈련/검증 분할
        split_idx = int(len(X) * 0.8)
        X_train, X_val = X.iloc[:split_idx], X.iloc[split_idx:]
        y_train, y_val = y.iloc[:split_idx], y.iloc[split_idx:]
        
        print(f"📊 훈련 데이터: {len(X_train)}개")
        print(f"📊 검증 데이터: {len(X_val)}개")
        
        # 5. LightGBM 파라미터
        params = {
            'objective': 'multiclass',
            'num_class': 3,
            'metric': 'multi_logloss',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.8,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'verbose': -1,
            'random_state': 42,
            'class_weight': 'balanced',
            'max_depth': 6,
            'min_data_in_leaf': 100,
            'lambda_l1': 0.1,
            'lambda_l2': 0.1
        }
        
        # 6. 모델 훈련
        print("\n🔄 숏 모델 훈련 시작...")
        train_data = lgb.Dataset(X_train, label=y_train)
        val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
        
        model = lgb.train(
            params,
            train_data,
            valid_sets=[train_data, val_data],
            valid_names=['train', 'val'],
            num_boost_round=1000,
            callbacks=[
                lgb.early_stopping(stopping_rounds=50),
                lgb.log_evaluation(period=100)
            ]
        )
        
        # 7. 성능 평가
        print(f"\n📊 숏 모델 성능 평가...")
        y_pred = model.predict(X_val, num_iteration=model.best_iteration)
        y_pred_class = np.argmax(y_pred, axis=1)
        
        # AUC 계산
        auc_scores = []
        for i in range(3):
            if len(np.unique(y_val)) > 1:
                y_binary = (y_val == i).astype(int)
                if y_binary.sum() > 0:
                    auc = roc_auc_score(y_binary, y_pred[:, i])
                    auc_scores.append(auc)
        
        avg_auc = np.mean(auc_scores) if auc_scores else 0.5
        
        # 8. 피처 중요도
        feature_importance = pd.DataFrame({
            'feature': X.columns,
            'importance': model.feature_importance(importance_type='gain')
        }).sort_values('importance', ascending=False)
        
        print(f"✅ 숏 모델 훈련 완료!")
        print(f"📊 평균 AUC: {avg_auc:.4f}")
        print(f"📊 최적 반복: {model.best_iteration}")
        
        print(f"\n🔝 상위 10개 중요 피처:")
        for i, (_, row) in enumerate(feature_importance.head(10).iterrows(), 1):
            print(f"   {i:2d}. {row['feature']}: {row['importance']:.0f}")
        
        # 9. 모델 저장
        model_path = 'models/short_multiframe_model.pkl'
        joblib.dump(model, model_path)
        print(f"💾 숏 모델 저장: {model_path}")
        
        # 피처 중요도 저장
        importance_path = 'models/short_feature_importance.csv'
        feature_importance.to_csv(importance_path, index=False)
        print(f"📊 피처 중요도 저장: {importance_path}")
        
        print(f"\n🎉 숏 모델 훈련 성공!")
        print(f"📊 35개 피처 멀티 타임프레임 숏 모델 완성")
        print(f"🎯 AUC: {avg_auc:.4f}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 숏 모델 훈련 실패: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 숏 모델 훈련 성공!")
    else:
        print("\n💥 숏 모델 훈련 실패!")
