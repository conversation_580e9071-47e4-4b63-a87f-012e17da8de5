#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 Project LEVIATHAN - 실시간 신호 생성 엔진

기존 백테스트 ML 모델과 설정을 실시간으로 적용하여
거래 신호를 생성하는 핵심 엔진

Author: 강현모
Date: 2024-12-15
Version: 1.0
"""

import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Optional, Tuple
import sys
from pathlib import Path
import time
import threading
import queue

# 프로젝트 루트 추가
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 기존 백테스트 시스템 임포트
from models.dual_model_predictor import DualModelPredictor
from models.ml_dynamic_exit_predictor import MLDynamicExitPredictor
from data.features import generate_features_from_csv
from data.interaction_features import InteractionFeatureGenerator
from config.settings import TradingConfig
from trading.realtime_data_stream import RealTimeDataStream


class RealTimeSignalEngine:
    """
    실시간 신호 생성 엔진
    
    기존 백테스트 시스템의 모든 설정과 모델을 실시간으로 적용:
    - 듀얼 모델 예측기 (롱/숏 통합)
    - ML 동적 익절 시스템
    - 동적 레버리지 및 포지션 크기 계산
    - Multi-Timeframe 분석 (15m + 4h)
    """
    
    def __init__(self, symbol: str = "BTCUSDT"):
        self.symbol = symbol
        
        # 기존 백테스트 설정 로드
        self.trading_config = TradingConfig()
        
        # 🧠 ML 모델 시스템 (백테스트와 동일)
        self.dual_predictor = DualModelPredictor()
        self.ml_exit_predictor = MLDynamicExitPredictor()
        self.feature_generator = InteractionFeatureGenerator()
        
        # 🎯 백테스트와 동일한 거래 설정
        self.leverage_range = (2.0, 10.0)      # 레버리지 2-10배
        self.stop_loss_range = (0.01, 0.05)   # 손절 1-5%
        self.take_profit_range = (0.02, 0.15) # 익절 2-15%
        self.max_position_size_pct = 1.0      # 자본의 최대 100% 사용
        self.fee_rate = 0.001                 # 0.1% 수수료
        self.slippage = 0.001                 # 0.1% 슬리피지
        
        # 🔥 신호 임계값 (백테스트와 동일)
        self.long_threshold = 0.402   # 롱 진입 임계값
        self.short_threshold = 0.165  # 숏 진입 임계값
        self.confidence_threshold = 0.2  # 최소 신뢰도 (높은 거래 빈도)
        
        # 실시간 데이터 스트림
        self.data_stream = None
        self.is_running = False
        
        # 신호 큐 및 콜백
        self.signal_queue = queue.Queue()
        self.signal_callback = None
        
        # 현재 시장 상태
        self.current_market_state = {
            'volatility': 0.0,
            'trend_strength': 0.0,
            'market_type': 'neutral'
        }
        
        # 동적 파라미터 (실시간 업데이트)
        self.current_leverage = 4.0
        self.current_stop_loss = 0.02
        self.current_take_profit = 0.05
        
        print("🎯 실시간 신호 생성 엔진 초기화 완료")
    
    def initialize_models(self) -> bool:
        """ML 모델 초기화 (백테스트와 동일한 모델 로드)"""
        print("🧠 ML 모델 시스템 초기화...")
        
        try:
            # 듀얼 모델 로드
            if not self.dual_predictor.load_models():
                print("❌ 듀얼 모델 로드 실패")
                return False
            
            # ML 동적 익절 모델 로드
            if not self.ml_exit_predictor.load_models():
                print("⚠️ ML 익절 모델 로드 실패 (기본 설정 사용)")
            
            print("✅ ML 모델 시스템 초기화 완료")
            return True
            
        except Exception as e:
            print(f"❌ 모델 초기화 오류: {e}")
            return False
    
    def start_signal_generation(self, callback_func=None):
        """실시간 신호 생성 시작"""
        print("🚀 실시간 신호 생성 시작...")
        
        # 모델 초기화
        if not self.initialize_models():
            print("❌ 모델 초기화 실패")
            return False
        
        # 콜백 함수 설정
        self.signal_callback = callback_func
        
        # 실시간 데이터 스트림 시작
        self.data_stream = RealTimeDataStream(self.symbol, self._on_new_data)
        self.data_stream.start_stream()
        
        self.is_running = True
        print("✅ 실시간 신호 생성 시작됨")
        
        return True
    
    def stop_signal_generation(self):
        """실시간 신호 생성 중지"""
        print("⏹️ 실시간 신호 생성 중지...")
        
        self.is_running = False
        
        if self.data_stream:
            self.data_stream.stop_stream()
        
        print("✅ 실시간 신호 생성 중지됨")
    
    def _on_new_data(self, interval: str, candle_data: Dict):
        """새로운 데이터 수신 시 호출되는 콜백"""
        if not self.is_running:
            return
        
        # 15분봉 데이터에서만 신호 생성
        if interval == "15m":
            try:
                # 신호 생성
                signal = self._generate_signal()
                
                if signal:
                    # 신호 큐에 추가
                    self.signal_queue.put(signal)
                    
                    # 콜백 함수 호출
                    if self.signal_callback:
                        self.signal_callback(signal)
                    
                    # 신호 출력
                    self._print_signal(signal)
                
            except Exception as e:
                print(f"❌ 신호 생성 오류: {e}")
    
    def _generate_signal(self) -> Optional[Dict]:
        """실시간 신호 생성 (백테스트 로직과 동일)"""
        try:
            # 최신 특성 데이터 가져오기
            features_df = self.data_stream.get_features("15m", 100)
            
            if features_df is None or len(features_df) < 50:
                return None
            
            # 최신 데이터 포인트
            latest_features = features_df.iloc[-1:].copy()
            
            # 듀얼 모델 예측 (백테스트와 동일)
            predictions = self.dual_predictor.predict_dual_signals(latest_features)
            
            if len(predictions) == 0:
                return None
            
            latest_prediction = predictions.iloc[-1]
            
            # 신호 생성 로직 (백테스트와 동일)
            signal = self._process_prediction(latest_prediction, latest_features.iloc[-1])
            
            return signal
            
        except Exception as e:
            print(f"❌ 신호 생성 중 오류: {e}")
            return None
    
    def _process_prediction(self, prediction: pd.Series, features: pd.Series) -> Optional[Dict]:
        """예측 결과를 신호로 변환 (백테스트 로직과 동일)"""
        
        long_prob = prediction['long_prob']
        short_prob = prediction['short_prob']
        final_signal = prediction['final_signal']
        confidence = prediction['confidence']
        
        # 신뢰도 필터링
        if confidence < self.confidence_threshold:
            return None
        
        # 동적 파라미터 계산 (백테스트와 동일)
        self._update_dynamic_parameters(features)
        
        # 신호 타입 결정
        signal_type = None
        if final_signal == 1 and long_prob > self.long_threshold:
            signal_type = "BUY"
        elif final_signal == -1 and short_prob > self.short_threshold:
            signal_type = "SELL"
        else:
            return None
        
        # ML 기반 익절 레벨 계산
        take_profit_level = self._calculate_ml_take_profit(features)
        
        # 포지션 크기 계산
        position_size = self._calculate_position_size(confidence)
        
        # 신호 객체 생성
        signal = {
            'timestamp': datetime.now(),
            'symbol': self.symbol,
            'signal_type': signal_type,
            'confidence': confidence,
            'long_prob': long_prob,
            'short_prob': short_prob,
            
            # 거래 파라미터 (백테스트와 동일)
            'leverage': self.current_leverage,
            'position_size': position_size,
            'stop_loss_pct': self.current_stop_loss,
            'take_profit_pct': take_profit_level,
            
            # 시장 상태
            'market_state': self.current_market_state.copy(),
            
            # 추가 정보
            'fee_rate': self.fee_rate,
            'slippage': self.slippage
        }
        
        return signal
    
    def _update_dynamic_parameters(self, features: pd.Series):
        """동적 파라미터 업데이트 (백테스트와 동일한 로직)"""
        try:
            # 변동성 계산
            if 'volatility' in features:
                volatility = features['volatility']
            else:
                volatility = 0.02  # 기본값
            
            # 추세 강도 계산
            if 'trend_strength' in features:
                trend_strength = features['trend_strength']
            else:
                trend_strength = 0.5  # 기본값
            
            # 시장 상태 업데이트
            self.current_market_state = {
                'volatility': volatility,
                'trend_strength': trend_strength,
                'market_type': self._classify_market_type(volatility, trend_strength)
            }
            
            # 동적 레버리지 계산 (백테스트와 동일)
            if volatility < 0.015:  # 저변동성
                self.current_leverage = min(10.0, self.leverage_range[1])
            elif volatility > 0.04:  # 고변동성
                self.current_leverage = max(2.0, self.leverage_range[0])
            else:  # 중간 변동성
                self.current_leverage = 4.0 + (0.04 - volatility) * 100
                self.current_leverage = np.clip(self.current_leverage, *self.leverage_range)
            
            # 동적 손절 계산 (백테스트와 동일)
            base_stop_loss = volatility * 0.05
            self.current_stop_loss = np.clip(base_stop_loss, *self.stop_loss_range)
            
        except Exception as e:
            print(f"⚠️ 동적 파라미터 업데이트 오류: {e}")
    
    def _classify_market_type(self, volatility: float, trend_strength: float) -> str:
        """시장 유형 분류"""
        if volatility > 0.04:
            return "high_volatility"
        elif volatility < 0.015:
            return "low_volatility"
        elif trend_strength > 0.7:
            return "strong_trend"
        elif trend_strength < 0.3:
            return "sideways"
        else:
            return "neutral"
    
    def _calculate_ml_take_profit(self, features: pd.Series) -> float:
        """ML 기반 익절 레벨 계산"""
        try:
            if self.ml_exit_predictor.exit_range_model is not None:
                # ML 모델로 익절 범위 예측
                feature_array = features.values.reshape(1, -1)
                predicted_range = self.ml_exit_predictor.predict_exit_range(feature_array)
                return np.clip(predicted_range[0], *self.take_profit_range)
            else:
                # 기본 익절 레벨
                return self.current_take_profit
        except:
            return self.current_take_profit
    
    def _calculate_position_size(self, confidence: float) -> float:
        """포지션 크기 계산 (백테스트와 동일)"""
        # 신뢰도 기반 포지션 크기 조정
        base_size = self.max_position_size_pct
        confidence_multiplier = min(confidence * 2, 1.0)  # 최대 100%
        
        position_size = base_size * confidence_multiplier
        
        # 레버리지 고려
        max_safe_size = 0.8 / self.current_leverage  # 안전 마진
        position_size = min(position_size, max_safe_size)
        
        return round(position_size, 4)
    
    def _print_signal(self, signal: Dict):
        """신호 정보 출력"""
        timestamp = signal['timestamp'].strftime('%H:%M:%S')
        signal_type = signal['signal_type']
        confidence = signal['confidence']
        leverage = signal['leverage']
        position_size = signal['position_size']
        
        print(f"\n🎯 [{timestamp}] 거래 신호 생성!")
        print(f"   📊 {signal_type} | 신뢰도: {confidence:.3f}")
        print(f"   ⚙️ {leverage:.1f}x 레버리지 | 포지션: {position_size:.1%}")
        print(f"   📈 익절: {signal['take_profit_pct']:.1%} | 손절: {signal['stop_loss_pct']:.1%}")
        print(f"   🌊 시장: {signal['market_state']['market_type']}")
    
    def get_latest_signal(self) -> Optional[Dict]:
        """최신 신호 가져오기"""
        try:
            return self.signal_queue.get_nowait()
        except queue.Empty:
            return None
    
    def get_status(self) -> Dict:
        """엔진 상태 반환"""
        return {
            'is_running': self.is_running,
            'symbol': self.symbol,
            'current_leverage': self.current_leverage,
            'current_stop_loss': self.current_stop_loss,
            'current_take_profit': self.current_take_profit,
            'market_state': self.current_market_state,
            'signal_queue_size': self.signal_queue.qsize(),
            'data_stream_status': self.data_stream.get_status() if self.data_stream else None
        }


def signal_handler(signal: Dict):
    """신호 처리 콜백 함수 예시"""
    print(f"🔔 신호 수신: {signal['signal_type']} @ {signal['confidence']:.3f}")
    
    # 여기에 실제 주문 실행 로직 추가
    # 예: order_executor.execute_order(signal)


if __name__ == "__main__":
    # 실시간 신호 생성 엔진 테스트
    print("🎯 실시간 신호 생성 엔진 테스트")
    print("=" * 60)
    
    # 엔진 초기화
    engine = RealTimeSignalEngine("BTCUSDT")
    
    try:
        # 신호 생성 시작
        if engine.start_signal_generation(signal_handler):
            print("⏰ 60초간 실시간 신호 생성 테스트...")
            
            # 60초간 실행
            time.sleep(60)
            
            # 상태 확인
            status = engine.get_status()
            print(f"\n📊 엔진 상태:")
            for key, value in status.items():
                print(f"   {key}: {value}")
        
    except KeyboardInterrupt:
        print("\n⏹️ 사용자 중단")
    
    finally:
        engine.stop_signal_generation()
        print("✅ 테스트 완료")
