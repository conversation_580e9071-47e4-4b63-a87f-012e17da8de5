"""
🐋 Project LEVIATHAN - 숏 포지션 전문가 모델용 타겟 생성기

이 모듈은 숏 포지션에 특화된 타겟 변수를 생성합니다.
기존 트리플 배리어 메소드의 결과를 숏 관점으로 재해석하여
하락장에서의 성공적인 숏 진입 시점을 학습할 수 있도록 합니다.

Author: 강현모
Date: 2024-12-14
Version: 1.0
"""

import pandas as pd
import numpy as np
from typing import Dict, Tuple
import sys
import os

# 프로젝트 루트 경로 추가
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.target_generator import TargetGenerator

# 리스크 관리 설정 (기존과 동일)
TAKE_PROFIT_PCT = 0.05  # +5% 익절
STOP_LOSS_PCT = 0.02    # -2% 손절
MAX_HOLD_PERIOD = 96    # 96개 봉 (24시간) - 스윙 최적화


class ShortTargetGenerator:
    """
    숏 포지션 전문가 모델용 타겟 생성기
    
    기존 트리플 배리어 메소드의 결과를 숏 관점으로 재해석:
    - 기존 손절(-1) → 숏 성공(1)
    - 기존 익절(1) 또는 시간만료(0) → 진입 안 함(0)
    """
    
    def __init__(self):
        """초기화"""
        self.base_generator = TargetGenerator()
        print("🔻 숏 포지션 전문가 타겟 생성기 초기화 완료")
    
    def generate_short_target(self, price_df: pd.DataFrame = None) -> pd.Series:
        """
        숏 포지션 전용 타겟 생성
        
        Args:
            price_df: 가격 데이터 (None이면 자동 로드)
            
        Returns:
            숏 타겟 시리즈 (1: 숏 성공, 0: 진입 안 함)
        """
        print("🔻 숏 포지션 전용 타겟 생성 시작...")
        
        # 가격 데이터 로드 (필요시)
        if price_df is None:
            price_df = self.base_generator.load_price_data()
            print(f"   📊 가격 데이터 로드: {price_df.shape}")
        
        # 기존 트리플 배리어 타겟 생성
        print("   🎯 기존 트리플 배리어 타겟 생성 중...")
        original_target = self.base_generator.generate_triple_barrier_target(price_df)
        
        # 숏 관점으로 재해석
        print("   🔄 숏 관점으로 타겟 재해석 중...")
        short_target = pd.Series(0, index=original_target.index)
        
        # 변환 로직:
        # 원본 -1 (롱 손절) → 숏 1 (숏 성공) - 가격이 하락했으므로 숏이 성공
        # 원본 1 (롱 익절) → 숏 0 (진입 안 함) - 가격이 상승했으므로 숏 실패
        # 원본 0 (시간만료) → 숏 0 (진입 안 함) - 명확한 방향성 없음
        
        short_target[original_target == -1] = 1  # 숏 성공
        short_target[original_target == 1] = 0   # 진입 안 함
        short_target[original_target == 0] = 0   # 진입 안 함
        
        # 결과 통계
        short_counts = short_target.value_counts().sort_index()
        total_valid = short_counts.sum()
        
        print(f"\n   ✅ 숏 타겟 생성 완료!")
        print(f"   📊 숏 타겟 분포:")
        print(f"      진입 안 함(0): {short_counts.get(0, 0):,}개 ({short_counts.get(0, 0)/total_valid*100:.1f}%)")
        print(f"      숏 성공(1): {short_counts.get(1, 0):,}개 ({short_counts.get(1, 0)/total_valid*100:.1f}%)")
        
        # 원본과 비교 통계
        original_counts = original_target.value_counts().sort_index()
        print(f"\n   📈 원본 vs 숏 타겟 비교:")
        print(f"      원본 손절(-1) → 숏 성공(1): {original_counts.get(-1, 0):,}개")
        print(f"      원본 익절(1) + 시간만료(0) → 진입 안 함(0): {original_counts.get(1, 0) + original_counts.get(0, 0):,}개")
        
        return short_target
    
    def analyze_short_opportunities(self, price_df: pd.DataFrame = None, short_target: pd.Series = None) -> Dict:
        """
        숏 기회 분석

        Args:
            price_df: 가격 데이터
            short_target: 이미 생성된 숏 타겟 (중복 계산 방지)

        Returns:
            분석 결과 딕셔너리
        """
        print("📊 숏 기회 심층 분석 시작...")

        if price_df is None:
            price_df = self.base_generator.load_price_data()

        # 숏 타겟 생성 (이미 있으면 재사용)
        if short_target is None:
            short_target = self.generate_short_target(price_df)
        
        # 시장 상황별 분석
        price_df['returns'] = price_df['close'].pct_change()
        price_df['market_trend'] = price_df['returns'].rolling(20).mean()
        
        # 하락장에서의 숏 성공률
        bear_market = price_df['market_trend'] < -0.001  # 일일 -0.1% 이상 하락 추세
        bull_market = price_df['market_trend'] > 0.001   # 일일 +0.1% 이상 상승 추세
        sideways = (~bear_market) & (~bull_market)       # 횡보장
        
        analysis = {
            'total_opportunities': len(short_target),
            'short_success_count': (short_target == 1).sum(),
            'short_success_rate': (short_target == 1).mean() * 100,
            'bear_market_success_rate': short_target[bear_market].mean() * 100 if bear_market.sum() > 0 else 0,
            'bull_market_success_rate': short_target[bull_market].mean() * 100 if bull_market.sum() > 0 else 0,
            'sideways_success_rate': short_target[sideways].mean() * 100 if sideways.sum() > 0 else 0,
            'bear_market_periods': bear_market.sum(),
            'bull_market_periods': bull_market.sum(),
            'sideways_periods': sideways.sum()
        }
        
        print(f"\n📊 숏 기회 분석 결과:")
        print(f"   전체 기회: {analysis['total_opportunities']:,}개")
        print(f"   숏 성공: {analysis['short_success_count']:,}개 ({analysis['short_success_rate']:.1f}%)")
        print(f"   하락장 성공률: {analysis['bear_market_success_rate']:.1f}% ({analysis['bear_market_periods']:,}개 기간)")
        print(f"   상승장 성공률: {analysis['bull_market_success_rate']:.1f}% ({analysis['bull_market_periods']:,}개 기간)")
        print(f"   횡보장 성공률: {analysis['sideways_success_rate']:.1f}% ({analysis['sideways_periods']:,}개 기간)")
        
        return analysis
    
    def save_short_dataset(self, output_path: str = "data/short_dataset.csv", short_target: pd.Series = None) -> str:
        """
        숏 전용 데이터셋 저장

        Args:
            output_path: 저장 경로
            short_target: 이미 생성된 숏 타겟 (중복 계산 방지)

        Returns:
            저장된 파일 경로
        """
        print(f"💾 숏 전용 데이터셋 저장 중: {output_path}")

        # 피처 데이터 로드
        from data.features import generate_features_from_csv
        features_df = generate_features_from_csv()

        # 숏 타겟 생성 (이미 있으면 재사용)
        if short_target is None:
            short_target = self.generate_short_target()
        
        # 인덱스 정렬
        common_index = features_df.index.intersection(short_target.index)
        features_aligned = features_df.loc[common_index]
        target_aligned = short_target.loc[common_index]
        
        # 데이터셋 결합
        short_dataset = features_aligned.copy()
        short_dataset['short_target'] = target_aligned
        
        # NaN 제거
        short_dataset = short_dataset.dropna()
        
        # 저장
        short_dataset.to_csv(output_path)
        
        print(f"   ✅ 저장 완료: {short_dataset.shape[0]}행 × {short_dataset.shape[1]}열")
        print(f"   📊 타겟 분포: {short_dataset['short_target'].value_counts().to_dict()}")
        
        return output_path


if __name__ == "__main__":
    """
    숏 타겟 생성기 테스트
    """
    print("🔻 Project LEVIATHAN - 숏 포지션 전문가 타겟 생성기 테스트")
    print("=" * 70)
    
    try:
        # 숏 타겟 생성기 초기화
        short_gen = ShortTargetGenerator()
        
        # 숏 타겟 생성 (한 번만)
        short_target = short_gen.generate_short_target()

        # 숏 기회 분석 (타겟 재사용)
        analysis = short_gen.analyze_short_opportunities(short_target=short_target)

        # 데이터셋 저장 (타겟 재사용)
        dataset_path = short_gen.save_short_dataset(short_target=short_target)
        
        print(f"\n🎯 다음 단계: 숏 전문가 모델 훈련")
        print(f"   python models/short_model.py")
        
    except Exception as e:
        print(f"❌ 오류 발생: {e}")
        import traceback
        traceback.print_exc()
