"""
🐋 Project LEVIATHAN - 듀얼 모델 예측기 (롱/숏 통합 시그널)

이 모듈은 롱 전문가 모델과 숏 전문가 모델의 예측을 통합하여
최종 거래 신호를 생성합니다. Stop-and-Reverse 로직을 통해
시장 변화에 빠르게 대응하는 통합 시그널 시스템을 구현합니다.

Author: 강현모
Date: 2024-12-14
Version: 1.0
"""

import pandas as pd
import numpy as np
import joblib
from typing import Tuple, Dict, Optional
import sys
import os

# 프로젝트 루트 경로 추가
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.features import generate_features_from_csv


class DualModelPredictor:
    """
    듀얼 모델 예측기
    
    롱 전문가 모델과 숏 전문가 모델의 신호를 통합하여
    최종 거래 결정을 내리는 통합 시그널 시스템
    """
    
    def __init__(self, long_model_path: str = "models/swing_s_v2_long_model_final_v2.pkl",
                 short_model_path: str = "models/swing_s_v2_long_model_final_v2.pkl"):
        """
        초기화
        
        Args:
            long_model_path: 롱 모델 경로
            short_model_path: 숏 모델 경로
        """
        self.long_model_path = long_model_path
        self.short_model_path = short_model_path
        self.long_model = None
        self.short_model = None
        
        # 신호 임계값 설정 (적응형 최적화 결과)
        self.long_threshold = 0.402   # 롱 진입 임계값 (80th percentile)
        self.short_threshold = 0.165  # 숏 진입 임계값 (80th percentile)
        self.confidence_weight = 0.1  # 확신도 가중치
        
        print("🔄 듀얼 모델 예측기 초기화 완료")
    
    def load_models(self) -> bool:
        """
        롱/숏 모델 로드

        Returns:
            로드 성공 여부
        """
        print("📥 롱/숏 모델 로드 중...")

        try:
            # 롱 모델 로드 (딕셔너리 형태일 수 있음)
            long_model_data = joblib.load(self.long_model_path)
            if isinstance(long_model_data, dict):
                self.long_model = long_model_data['model']
            else:
                self.long_model = long_model_data
            print(f"   ✅ 롱 모델 로드: {self.long_model_path}")

            # 숏 모델 로드
            self.short_model = joblib.load(self.short_model_path)
            print(f"   ✅ 숏 모델 로드: {self.short_model_path}")

            return True

        except FileNotFoundError as e:
            print(f"   ❌ 모델 파일 없음: {e}")
            return False
        except Exception as e:
            print(f"   ❌ 모델 로드 실패: {e}")
            return False
    
    def predict_dual_signals(self, features: pd.DataFrame) -> pd.DataFrame:
        """
        듀얼 모델 신호 예측

        Args:
            features: 피처 데이터프레임

        Returns:
            예측 결과 데이터프레임 (timestamp, long_prob, short_prob, final_signal, confidence)
        """
        if self.long_model is None or self.short_model is None:
            if not self.load_models():
                raise ValueError("모델 로드 실패")

        print(f"🔮 듀얼 모델 예측 시작: {features.shape[0]}개 데이터 포인트")

        # 롱 모델 예측 (기존 이진 분류 모델)
        long_probabilities = self.long_model.predict(features)

        # 숏 모델용 피처 생성 (하락장 특화 피처 추가)
        short_features = self._create_short_features(features)

        # 숏 모델 예측
        short_probabilities = self.short_model.predict(short_features)
        
        # 결과 데이터프레임 생성 (인덱스 유지)
        predictions = pd.DataFrame({
            'long_prob': long_probabilities,
            'short_prob': short_probabilities
        }, index=features.index)
        
        # 최종 신호 및 확신도 계산
        predictions['final_signal'], predictions['confidence'] = zip(
            *predictions.apply(self._calculate_final_signal, axis=1)
        )
        
        # 신호 통계
        signal_counts = predictions['final_signal'].value_counts()
        print(f"   📊 신호 분포: {signal_counts.to_dict()}")
        
        return predictions
    
    def _calculate_final_signal(self, row: pd.Series) -> Tuple[str, float]:
        """
        최종 신호 및 확신도 계산

        Args:
            row: 예측 결과 행 (long_prob, short_prob)

        Returns:
            (final_signal, confidence) 튜플
        """
        long_prob = row['long_prob']
        short_prob = row['short_prob']

        # 신호 강도 계산 (확률을 0-1 범위로 정규화)
        long_strength = long_prob
        short_strength = short_prob

        # 최종 신호 결정 로직
        if long_strength > self.long_threshold and short_strength > self.short_threshold:
            # 두 신호가 모두 강할 때: 더 강한 신호 선택
            if long_strength > short_strength:
                signal = 'BUY'
                confidence = long_strength
            else:
                signal = 'SELL'
                confidence = short_strength
        elif long_strength > self.long_threshold:
            # 롱 신호만 강할 때
            signal = 'BUY'
            confidence = long_strength
        elif short_strength > self.short_threshold:
            # 숏 신호만 강할 때
            signal = 'SELL'
            confidence = short_strength
        else:
            # 신호가 약할 때
            signal = 'HOLD'
            confidence = max(long_strength, short_strength) * self.confidence_weight

        return signal, confidence

    def _create_short_features(self, features_df: pd.DataFrame) -> pd.DataFrame:
        """
        하락장 특화 피처 생성 (숏 모델용)

        Args:
            features_df: 기본 피처 데이터프레임

        Returns:
            하락장 특화 피처가 추가된 데이터프레임
        """
        enhanced_df = features_df.copy()

        # 1. 데드크로스 신호 (단기 MA < 장기 MA)
        if 'SMA_20_exec' in enhanced_df.columns and 'SMA_50_trend' in enhanced_df.columns:
            enhanced_df['Death_Cross'] = (enhanced_df['SMA_20_exec'] < enhanced_df['SMA_50_trend']).astype(int)

        # 2. RSI 과매수 구간 (하락 전조)
        if 'RSI_14_exec' in enhanced_df.columns:
            enhanced_df['RSI_Overbought'] = (enhanced_df['RSI_14_exec'] > 70).astype(int)
            enhanced_df['RSI_Bearish_Divergence'] = (enhanced_df['RSI_14_exec'] < enhanced_df['RSI_14_exec'].shift(1)).astype(int)

        # 3. MACD 하락 신호
        if 'MACD_exec' in enhanced_df.columns and 'MACD_signal_exec' in enhanced_df.columns:
            enhanced_df['MACD_Bearish'] = (enhanced_df['MACD_exec'] < enhanced_df['MACD_signal_exec']).astype(int)
            enhanced_df['MACD_Declining'] = (enhanced_df['MACD_exec'] < enhanced_df['MACD_exec'].shift(1)).astype(int)

        # 4. 볼린저 밴드 상단 터치 (하락 전조)
        if 'BB_position_exec' in enhanced_df.columns:
            enhanced_df['BB_Upper_Touch'] = (enhanced_df['BB_position_exec'] > 0.8).astype(int)

        # 5. 거래량 급증 + 가격 하락 (공포 매도)
        if 'Volume_ratio_exec' in enhanced_df.columns:
            enhanced_df['Volume_Spike'] = (enhanced_df['Volume_ratio_exec'] > 1.5).astype(int)

        # 6. 추세 약화 신호
        if 'SMA_50_trend' in enhanced_df.columns:
            enhanced_df['Trend_Weakening'] = (enhanced_df['SMA_50_trend'] < enhanced_df['SMA_50_trend'].shift(5)).astype(int)

        # 7. 복합 하락 신호 (여러 지표 조합)
        bear_signals = []
        for col in ['Death_Cross', 'RSI_Overbought', 'MACD_Bearish', 'BB_Upper_Touch']:
            if col in enhanced_df.columns:
                bear_signals.append(enhanced_df[col])

        if bear_signals:
            enhanced_df['Bear_Signal_Count'] = sum(bear_signals)
            enhanced_df['Strong_Bear_Signal'] = (enhanced_df['Bear_Signal_Count'] >= 2).astype(int)

        return enhanced_df

    def generate_trading_signals(self, start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """
        거래 신호 생성
        
        Args:
            start_date: 시작 날짜
            end_date: 종료 날짜
            
        Returns:
            거래 신호 데이터프레임
        """
        print("🎯 통합 거래 신호 생성 중...")
        
        # 피처 데이터 로드
        features_df = generate_features_from_csv()
        
        # 기간 필터링
        if start_date or end_date:
            if start_date:
                features_df = features_df[features_df.index >= start_date]
            if end_date:
                features_df = features_df[features_df.index <= end_date]
        
        print(f"   📊 분석 기간: {features_df.index[0]} ~ {features_df.index[-1]}")
        print(f"   📊 데이터 포인트: {len(features_df):,}개")
        
        # 듀얼 모델 예측
        predictions = self.predict_dual_signals(features_df)
        
        # Stop-and-Reverse 로직 적용
        enhanced_signals = self._apply_stop_and_reverse(predictions)
        
        return enhanced_signals
    
    def _apply_stop_and_reverse(self, predictions: pd.DataFrame) -> pd.DataFrame:
        """
        Stop-and-Reverse 로직 적용
        
        Args:
            predictions: 기본 예측 결과
            
        Returns:
            SAR 로직이 적용된 신호
        """
        print("   🔄 Stop-and-Reverse 로직 적용 중...")
        
        enhanced = predictions.copy()
        enhanced['position'] = 'NONE'  # 현재 포지션 상태
        enhanced['action'] = 'HOLD'    # 실제 거래 액션

        # 인덱스가 datetime인지 확인
        if not isinstance(enhanced.index, pd.DatetimeIndex):
            print(f"   ⚠️ 경고: 인덱스가 DatetimeIndex가 아닙니다: {type(enhanced.index)}")
        else:
            print(f"   ✅ 인덱스 확인: DatetimeIndex ({len(enhanced)}개)")
        
        current_position = 'NONE'
        
        for i, row in enhanced.iterrows():
            signal = row['final_signal']
            confidence = row['confidence']
            
            # Stop-and-Reverse 로직
            if current_position == 'NONE':
                # 포지션이 없을 때: 신호에 따라 진입
                if signal == 'BUY' and confidence > self.confidence_weight:
                    enhanced.loc[i, 'action'] = 'BUY'
                    enhanced.loc[i, 'position'] = 'LONG'
                    current_position = 'LONG'
                elif signal == 'SELL' and confidence > self.confidence_weight:
                    enhanced.loc[i, 'action'] = 'SELL'
                    enhanced.loc[i, 'position'] = 'SHORT'
                    current_position = 'SHORT'
                else:
                    enhanced.loc[i, 'position'] = 'NONE'
                    
            elif current_position == 'LONG':
                # 롱 포지션 보유 중
                if signal == 'SELL' and confidence > self.confidence_weight:
                    # 숏 신호 → 롱 청산 후 숏 진입
                    enhanced.loc[i, 'action'] = 'SELL'
                    enhanced.loc[i, 'position'] = 'SHORT'
                    current_position = 'SHORT'
                else:
                    enhanced.loc[i, 'position'] = 'LONG'

            elif current_position == 'SHORT':
                # 숏 포지션 보유 중
                if signal == 'BUY' and confidence > self.confidence_weight:
                    # 롱 신호 → 숏 청산 후 롱 진입
                    enhanced.loc[i, 'action'] = 'BUY'
                    enhanced.loc[i, 'position'] = 'LONG'
                    current_position = 'LONG'
                else:
                    enhanced.loc[i, 'position'] = 'SHORT'
        
        # 액션 통계
        action_counts = enhanced['action'].value_counts()
        position_counts = enhanced['position'].value_counts()
        
        print(f"   📊 거래 액션 분포: {action_counts.to_dict()}")
        print(f"   📊 포지션 분포: {position_counts.to_dict()}")
        
        return enhanced
    
    def analyze_signal_quality(self, signals: pd.DataFrame) -> Dict:
        """
        신호 품질 분석
        
        Args:
            signals: 거래 신호 데이터프레임
            
        Returns:
            분석 결과 딕셔너리
        """
        print("📊 신호 품질 분석 중...")
        
        # 거래 빈도 분석
        total_signals = len(signals)
        buy_signals = (signals['action'] == 'BUY').sum()
        sell_signals = (signals['action'] == 'SELL').sum()
        hold_signals = (signals['action'] == 'HOLD').sum()
        
        # 확신도 분석
        avg_confidence = signals['confidence'].mean()
        high_confidence_signals = (signals['confidence'] > 0.3).sum()
        
        # 포지션 변경 빈도
        position_changes = (signals['position'] != signals['position'].shift(1)).sum()
        
        analysis = {
            'total_signals': total_signals,
            'buy_signals': buy_signals,
            'sell_signals': sell_signals,
            'hold_signals': hold_signals,
            'trading_frequency': (buy_signals + sell_signals) / total_signals * 100,
            'avg_confidence': avg_confidence,
            'high_confidence_signals': high_confidence_signals,
            'position_changes': position_changes,
            'avg_position_duration': total_signals / max(position_changes, 1)
        }
        
        print(f"   📊 신호 품질 분석 결과:")
        print(f"      총 신호: {analysis['total_signals']:,}개")
        print(f"      매수 신호: {analysis['buy_signals']:,}개")
        print(f"      매도 신호: {analysis['sell_signals']:,}개")
        print(f"      거래 빈도: {analysis['trading_frequency']:.1f}%")
        print(f"      평균 확신도: {analysis['avg_confidence']:.3f}")
        print(f"      고확신 신호: {analysis['high_confidence_signals']:,}개")
        print(f"      포지션 변경: {analysis['position_changes']:,}회")
        print(f"      평균 포지션 지속: {analysis['avg_position_duration']:.1f}개 봉")
        
        return analysis
    
    def save_signals(self, signals: pd.DataFrame, output_path: str = "data/dual_model_signals.csv") -> str:
        """
        신호 저장
        
        Args:
            signals: 신호 데이터프레임
            output_path: 저장 경로
            
        Returns:
            저장된 파일 경로
        """
        signals.to_csv(output_path)
        print(f"   💾 신호 저장 완료: {output_path}")
        return output_path


if __name__ == "__main__":
    """
    듀얼 모델 예측기 테스트
    """
    print("🔄 Project LEVIATHAN - 듀얼 모델 예측기 테스트")
    print("=" * 70)
    
    try:
        # 듀얼 모델 예측기 초기화
        dual_predictor = DualModelPredictor()
        
        # 거래 신호 생성 (2024년 테스트)
        signals = dual_predictor.generate_trading_signals(
            start_date='2024-01-01',
            end_date='2024-12-31'
        )
        
        # 신호 품질 분석
        analysis = dual_predictor.analyze_signal_quality(signals)
        
        # 신호 저장
        output_path = dual_predictor.save_signals(signals)
        
        print(f"\n🎯 다음 단계: 듀얼 모델 백테스팅")
        print(f"   python backtesting/dual_model_backtest.py")
        
    except Exception as e:
        print(f"❌ 오류 발생: {e}")
        import traceback
        traceback.print_exc()
