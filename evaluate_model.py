"""
Project LEVIATHAN - 전문가용 모델 평가 및 신호 생성 시스템
트리플 배리어 모델의 성능을 정밀 평가하고 확률 임계값 기반 신호를 생성합니다.
"""

import pandas as pd
import numpy as np
import joblib
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix, f1_score
from sklearn.metrics import precision_recall_curve, roc_curve, auc
import sys
from pathlib import Path
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

# 프로젝트 루트를 Python 경로에 추가
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from data.features import generate_features_from_csv
from models.target_generator import TargetGenerator
from config import PROBABILITY_THRESHOLD


class ModelEvaluator:
    """
    트리플 배리어 모델 전문가용 평가 시스템
    """
    
    def __init__(self, model_path: str = 'models/lgb_model_v2.pkl'):
        """
        모델 평가기 초기화
        
        Args:
            model_path: 평가할 모델 파일 경로
        """
        self.model_path = model_path
        self.model_data = None
        self.model = None
        self.class_names = ['손절(-1)', '시간만료(0)', '익절(1)']
        
        # 모델 로드
        self.load_model()
        
        print(f"📊 모델 평가기 초기화 완료")
        print(f"   🤖 모델: {model_path}")
        print(f"   🎯 클래스: {self.class_names}")
        print(f"   📈 확률 임계값: {PROBABILITY_THRESHOLD}")
        
    def load_model(self) -> None:
        """
        저장된 모델 로드
        """
        try:
            self.model_data = joblib.load(self.model_path)
            self.model = self.model_data['model']
            print(f"✅ 모델 로드 성공: {self.model_data.get('model_version', 'unknown')}")
        except Exception as e:
            print(f"❌ 모델 로드 실패: {e}")
            raise
    
    def prepare_evaluation_data(self) -> Tuple[pd.DataFrame, pd.Series]:
        """
        평가용 데이터 준비
        
        Returns:
            (features, target) 튜플
        """
        print("📊 평가용 데이터 준비 중...")
        
        # 피처 데이터 로드
        features_df = generate_features_from_csv()
        
        # 타겟 생성기 초기화
        target_gen = TargetGenerator()
        price_data = target_gen.load_price_data()
        
        # 인덱스 정렬
        common_index = features_df.index.intersection(price_data.index)
        features_aligned = features_df.loc[common_index]
        price_aligned = price_data.loc[common_index]
        
        # 트리플 배리어 타겟 생성
        triple_barrier_target = target_gen.generate_triple_barrier_target(price_aligned)
        target_aligned = triple_barrier_target.loc[common_index]
        
        # 결측값 제거
        valid_mask = target_aligned.notna() & features_aligned.notna().all(axis=1)
        final_features = features_aligned[valid_mask]
        final_target = target_aligned[valid_mask]
        
        # 타겟 라벨 변환 (모델 훈련 시와 동일)
        label_mapping = {-1: 0, 0: 1, 1: 2}
        final_target_mapped = final_target.map(label_mapping)
        
        print(f"   ✅ 평가 데이터: {final_features.shape}, 타겟: {len(final_target_mapped)}")
        
        return final_features, final_target_mapped
    
    def comprehensive_evaluation(self) -> Dict:
        """
        종합적인 모델 성능 평가
        
        Returns:
            평가 결과 딕셔너리
        """
        print("🔍 종합적인 모델 성능 평가 시작...")
        
        # 데이터 준비
        X, y = self.prepare_evaluation_data()
        
        # 최근 20% 데이터를 테스트 세트로 사용
        split_idx = int(len(X) * 0.8)
        X_test, y_test = X.iloc[split_idx:], y.iloc[split_idx:]
        
        print(f"   📊 테스트 데이터: {X_test.shape}")
        
        # 예측 수행
        y_pred_proba = self.model.predict(X_test)
        y_pred_class = np.argmax(y_pred_proba, axis=1)
        
        # 기본 성능 지표
        f1_macro = f1_score(y_test, y_pred_class, average='macro')
        f1_weighted = f1_score(y_test, y_pred_class, average='weighted')
        
        # 분류 보고서
        class_report = classification_report(
            y_test, y_pred_class, 
            target_names=self.class_names,
            output_dict=True
        )
        
        # 혼동 행렬
        cm = confusion_matrix(y_test, y_pred_class)
        
        # 클래스별 확률 분포 분석
        prob_stats = self.analyze_probability_distribution(y_pred_proba, y_test)
        
        results = {
            'f1_macro': f1_macro,
            'f1_weighted': f1_weighted,
            'classification_report': class_report,
            'confusion_matrix': cm,
            'probability_stats': prob_stats,
            'test_size': len(y_test),
            'predictions': y_pred_class,
            'probabilities': y_pred_proba,
            'true_labels': y_test
        }
        
        # 결과 출력
        self.print_evaluation_results(results)
        
        return results
    
    def analyze_probability_distribution(self, y_pred_proba: np.ndarray, 
                                       y_true: pd.Series) -> Dict:
        """
        클래스별 예측 확률 분포 분석
        
        Args:
            y_pred_proba: 예측 확률 배열
            y_true: 실제 라벨
            
        Returns:
            확률 분포 통계
        """
        prob_stats = {}
        
        for class_idx in range(3):
            class_probs = y_pred_proba[:, class_idx]
            prob_stats[f'class_{class_idx}'] = {
                'mean': np.mean(class_probs),
                'std': np.std(class_probs),
                'min': np.min(class_probs),
                'max': np.max(class_probs),
                'q25': np.percentile(class_probs, 25),
                'q50': np.percentile(class_probs, 50),
                'q75': np.percentile(class_probs, 75)
            }
        
        return prob_stats
    
    def generate_threshold_based_signals(self, X: pd.DataFrame, 
                                       threshold: float = None) -> pd.DataFrame:
        """
        확률 임계값 기반 거래 신호 생성
        
        Args:
            X: 피처 데이터
            threshold: 확률 임계값 (None이면 config 값 사용)
            
        Returns:
            신호 데이터프레임
        """
        if threshold is None:
            threshold = PROBABILITY_THRESHOLD
            
        print(f"🚦 확률 임계값 {threshold:.1%} 기반 신호 생성 중...")
        
        # 예측 확률 계산
        y_pred_proba = self.model.predict(X)
        
        signals = []
        confidences = []
        
        for probas in y_pred_proba:
            # probas 배열 순서: [P(손절), P(시간만료), P(익절)]
            sell_prob = probas[0]    # 손절 확률
            hold_prob = probas[1]    # 시간만료 확률  
            buy_prob = probas[2]     # 익절 확률
            
            # 최고 확률과 해당 신호 결정
            max_prob = max(sell_prob, hold_prob, buy_prob)
            
            if buy_prob >= threshold and buy_prob == max_prob:
                signal = 'BUY'
                confidence = buy_prob
            elif sell_prob >= threshold and sell_prob == max_prob:
                signal = 'SELL'
                confidence = sell_prob
            else:
                signal = 'HOLD'
                confidence = hold_prob
            
            signals.append(signal)
            confidences.append(confidence)
        
        # 결과 데이터프레임 생성
        results_df = pd.DataFrame({
            'timestamp': X.index,
            'signal': signals,
            'confidence': confidences,
            'sell_prob': y_pred_proba[:, 0],
            'hold_prob': y_pred_proba[:, 1],
            'buy_prob': y_pred_proba[:, 2]
        })
        
        # 신호 분포 분석
        signal_counts = results_df['signal'].value_counts()
        total_signals = len(results_df)
        
        print(f"   📊 신호 분포:")
        for signal, count in signal_counts.items():
            percentage = count / total_signals * 100
            print(f"      {signal}: {count:,}개 ({percentage:.1f}%)")
        
        # 평균 신뢰도
        avg_confidence = results_df['confidence'].mean()
        print(f"   📈 평균 신뢰도: {avg_confidence:.3f}")
        
        return results_df
    
    def print_evaluation_results(self, results: Dict) -> None:
        """
        평가 결과 출력
        
        Args:
            results: 평가 결과 딕셔너리
        """
        print(f"\n" + "="*60)
        print(f"📊 트리플 배리어 모델 성능 평가 결과")
        print(f"="*60)
        
        print(f"🎯 전체 성능 지표:")
        print(f"   F1 스코어 (매크로): {results['f1_macro']:.4f}")
        print(f"   F1 스코어 (가중): {results['f1_weighted']:.4f}")
        print(f"   테스트 샘플 수: {results['test_size']:,}개")
        
        print(f"\n📋 클래스별 성능:")
        for class_name in self.class_names:
            class_metrics = results['classification_report'][class_name]
            print(f"   {class_name}:")
            print(f"      정밀도: {class_metrics['precision']:.3f}")
            print(f"      재현율: {class_metrics['recall']:.3f}")
            print(f"      F1 스코어: {class_metrics['f1-score']:.3f}")
            print(f"      샘플 수: {class_metrics['support']:,}개")
        
        print(f"\n🔍 혼동 행렬:")
        cm = results['confusion_matrix']
        print(f"        예측→   손절   시간만료   익절")
        for i, actual_class in enumerate(self.class_names):
            print(f"   {actual_class:>8}: {cm[i]}")
        
        print(f"\n📈 확률 분포 통계:")
        prob_stats = results['probability_stats']
        for class_idx, class_name in enumerate(self.class_names):
            stats = prob_stats[f'class_{class_idx}']
            print(f"   {class_name}:")
            print(f"      평균: {stats['mean']:.3f}, 표준편차: {stats['std']:.3f}")
            print(f"      범위: {stats['min']:.3f} ~ {stats['max']:.3f}")
    
    def save_evaluation_plots(self, results: Dict) -> None:
        """
        평가 결과 시각화 저장
        
        Args:
            results: 평가 결과 딕셔너리
        """
        print(f"📊 평가 결과 시각화 저장 중...")
        
        # 한글 폰트 설정
        plt.rcParams['font.family'] = 'DejaVu Sans'
        plt.rcParams['axes.unicode_minus'] = False
        
        # 혼동 행렬 히트맵
        plt.figure(figsize=(10, 8))
        sns.heatmap(
            results['confusion_matrix'], 
            annot=True, 
            fmt='d',
            xticklabels=self.class_names,
            yticklabels=self.class_names,
            cmap='Blues'
        )
        plt.title('Triple Barrier Model - Confusion Matrix')
        plt.ylabel('Actual')
        plt.xlabel('Predicted')
        plt.tight_layout()
        plt.savefig('evaluation_confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 확률 분포 히스토그램
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        probabilities = results['probabilities']
        
        for i, class_name in enumerate(self.class_names):
            axes[i].hist(probabilities[:, i], bins=50, alpha=0.7, color=f'C{i}')
            axes[i].set_title(f'{class_name} Probability Distribution')
            axes[i].set_xlabel('Probability')
            axes[i].set_ylabel('Frequency')
            axes[i].axvline(PROBABILITY_THRESHOLD, color='red', linestyle='--', 
                          label=f'Threshold ({PROBABILITY_THRESHOLD})')
            axes[i].legend()
        
        plt.tight_layout()
        plt.savefig('evaluation_probability_distributions.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"   ✅ 시각화 저장 완료:")
        print(f"      - evaluation_confusion_matrix.png")
        print(f"      - evaluation_probability_distributions.png")


if __name__ == "__main__":
    """
    모델 평가 및 신호 생성 테스트
    """
    print("📊 Project LEVIATHAN - 전문가용 모델 평가 시스템")
    print("=" * 60)
    
    try:
        # 모델 평가기 초기화
        evaluator = ModelEvaluator()
        
        # 종합적인 성능 평가
        results = evaluator.comprehensive_evaluation()
        
        # 시각화 저장
        evaluator.save_evaluation_plots(results)
        
        # 확률 임계값 기반 신호 생성 테스트
        print(f"\n🚦 확률 임계값 기반 신호 생성 테스트:")
        X, _ = evaluator.prepare_evaluation_data()
        
        # 최근 100개 시점에 대해 신호 생성
        recent_X = X.tail(100)
        signals_df = evaluator.generate_threshold_based_signals(recent_X)
        
        print(f"\n📊 최근 100개 시점 신호 요약:")
        print(signals_df.tail(10)[['signal', 'confidence', 'buy_prob', 'sell_prob']])
        
        print(f"\n🎉 전문가용 모델 평가 완료!")
        
    except Exception as e:
        print(f"❌ 오류 발생: {e}")
        import traceback
        traceback.print_exc()
