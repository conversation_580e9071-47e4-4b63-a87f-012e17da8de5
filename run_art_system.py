"""
Project LEVIATHAN - ART 시스템 통합 실행기
ML 동적 시스템이 통합된 ART (Adaptive Risk Trading) 시스템 실행
"""

import sys
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 프로젝트 루트를 Python 경로에 추가
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from backtesting.art_backtest import ARTBacktester


def main():
    """ART 시스템 메인 실행 함수"""
    print("🎯 Project LEVIATHAN - ART 시스템 시작")
    print("=" * 60)
    print("🧠 ML 기반 시장 분석 엔진")
    print("⚖️ 동적 리스크 관리 시스템")
    print("🔄 적응형 거래 전략")
    print("📊 실시간 성과 추적")
    print("=" * 60)
    
    # 사용자 설정
    print("\n📋 사용자 설정:")
    print("   📊 동적 레버리지: 2-10배 자동 조정")
    print("   📉 손절: 5% (고정)")
    print("   📈 익절: 5-15% (동적)")
    print("   💰 포지션: 30-100% (시장별)")
    print("   🎯 ML 동적 시스템: 활성화")
    
    try:
        # ART 백테스터 초기화
        print("\n🚀 ART 백테스터 초기화...")
        backtest = ARTBacktester(initial_capital=10000)
        
        # 백테스팅 실행
        print("\n⚡ ART 백테스팅 실행...")
        results = backtest.run_backtest("2024-01-01", "2024-06-30")
        
        # 결과 출력
        backtest.print_results(results)
        
        # 성공 메시지
        print("\n✅ ART 시스템 실행 완료!")
        print("🎯 모든 ML 동적 시스템이 정상 작동했습니다.")
        
    except Exception as e:
        print(f"\n❌ 오류 발생: {str(e)}")
        print("🔧 시스템을 점검해주세요.")
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 ART 시스템 테스트 성공!")
    else:
        print("\n💥 ART 시스템 테스트 실패!")
