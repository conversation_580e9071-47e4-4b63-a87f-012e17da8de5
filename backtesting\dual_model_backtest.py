"""
🐋 Project LEVIATHAN - 듀얼 모델 백테스팅 엔진

롱/숏 전문가 모델의 통합 신호를 백테스팅하는 전용 엔진입니다.
Stop-and-Reverse 로직과 포지션 전환을 정확히 시뮬레이션하여
듀얼 모델 시스템의 실제 성능을 검증합니다.

Author: 강현모
Date: 2024-12-14
Version: 1.0
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple
import sys
import os
from dataclasses import dataclass
from datetime import datetime

# 프로젝트 루트 경로 추가
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.dual_model_predictor import DualModelPredictor
from models.ml_dynamic_exit_predictor import MLDynamicExitPredictor


@dataclass
class DualTrade:
    """듀얼 모델 거래 정보"""
    entry_time: str
    exit_time: str
    entry_price: float
    exit_price: float
    position_type: str  # 'LONG' or 'SHORT'
    position_size: float
    pnl: float
    pnl_pct: float
    exit_reason: str
    long_confidence: float
    short_confidence: float


class DualModelBacktester:
    """
    듀얼 모델 백테스팅 엔진
    
    롱/숏 전문가 모델의 통합 신호를 기반으로
    Stop-and-Reverse 전략을 백테스팅합니다.
    """
    
    def __init__(self, initial_capital: float = 10000, custom_predictor: 'DualModelPredictor' = None):
        """
        초기화

        Args:
            initial_capital: 초기 자본금
            custom_predictor: 사용자 정의 예측기 (재훈련된 모델용)
        """
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.current_position = None
        self.trades = []

        # 🔥 새로운 기능: 사용자 정의 예측기 지원
        self.custom_predictor = custom_predictor

        # 🧠 ML 기반 동적 거래 설정 (확장된 범위)
        self.leverage = 4.0         # 기본 4배 (2-10배 동적 조정)
        self.stop_loss_pct = 0.02   # 기본 2% (1-5% 동적 조정)
        self.take_profit_pct = 0.05 # 기본 5% (2-15% ML 예측)
        self.fee_rate = 0.001       # 0.1% 수수료 (현실화)
        self.slippage = 0.001       # 0.1% 슬리피지 (현실화)

        # 🧠 ML 기반 동적 시스템 (완전 비활성화 - 오류 해결)
        self.ml_adaptive_tp = False  # ML 동적 익절 비활성화
        self.ml_adaptive_params = True  # ML 동적 파라미터 기본 활성화
        self.ml_exit_predictor = None   # ML 익절 예측기 비활성화

        # ML 동적 파라미터 범위
        self.leverage_range = (2.0, 10.0)      # 레버리지 2-10배
        self.stop_loss_range = (0.01, 0.05)   # 손절 1-5%
        self.take_profit_range = (0.02, 0.15) # 익절 2-15%

        # 🔧 개선: 공격적 자본 사용
        self.max_position_size_pct = 1.0  # 자본의 최대 100% 사용 (공격적)
        self.min_liquidity_threshold = 1000000  # 최소 유동성 $1M
        self.max_slippage_pct = 0.01   # 최대 슬리피지 1.0% (현실화)
        self.daily_trade_limit = 10  # 일일 최대 거래 횟수
        self.consecutive_loss_limit = 3  # 연속 손실 제한
        self.max_single_position_usd = 100000  # 단일 포지션 최대 $100K

        # 추가 상태 변수
        self.daily_trade_count = {}
        self.consecutive_losses = 0
        self.last_trade_date = None

        predictor_info = "사용자 정의 모델" if custom_predictor else "기본 모델"
        print(f"🔄 듀얼 모델 백테스터 초기화 (초기자본: ${initial_capital:,.0f}, 예측기: {predictor_info})")
        print(f"   🚀 공격적 설정: 포지션 한도 {self.max_position_size_pct*100:.0f}%, 일일 거래 한도 {self.daily_trade_limit}회")
        print(f"   🧠 ML 동적 시스템: 익절 {'활성화' if self.ml_adaptive_tp else '비활성화'}, 파라미터 {'활성화' if self.ml_adaptive_params else '비활성화'}")
        print(f"   📊 동적 범위: 레버리지 {self.leverage_range[0]}-{self.leverage_range[1]}배, 손절 {self.stop_loss_range[0]*100:.0f}-{self.stop_loss_range[1]*100:.0f}%, 익절 {self.take_profit_range[0]*100:.0f}-{self.take_profit_range[1]*100:.0f}%")

        # 🧠 ML 익절 예측기 초기화 (개선된 버전)
        try:
            self.ml_exit_predictor = MLDynamicExitPredictor()
            if self.ml_exit_predictor.load_models():
                self.ml_adaptive_tp = True  # 모델이 있으면 활성화
                print(f"   ✅ ML 익절 예측기 로드 완료 - 동적 익절 활성화")
                print(f"   🎯 목표: 예측된 최적 익절 범위까지 수익 극대화")
            else:
                print(f"   ⚠️ ML 익절 모델 없음 - 기본 익절 사용")
                self.ml_adaptive_tp = False
                self.ml_exit_predictor = None
        except Exception as e:
            print(f"   ⚠️ ML 익절 예측기 초기화 실패: {e} - 기본 익절 사용")
            self.ml_adaptive_tp = False
            self.ml_exit_predictor = None
    
    def run_backtest(self, start_date: str = '2024-01-01', end_date: str = '2024-12-31') -> Dict:
        """
        듀얼 모델 백테스팅 실행
        
        Args:
            start_date: 시작 날짜
            end_date: 종료 날짜
            
        Returns:
            백테스팅 결과
        """
        print(f"🚀 듀얼 모델 백테스팅 시작: {start_date} ~ {end_date}")

        # 🔥 개선된 듀얼 모델 신호 생성 (재훈련된 모델 지원)
        if self.custom_predictor:
            print(f"   🧠 사용자 정의 모델 사용 (재훈련된 모델)")
            dual_predictor = self.custom_predictor
        else:
            print(f"   🧠 기본 모델 사용")
            dual_predictor = DualModelPredictor()

        signals = dual_predictor.generate_trading_signals(start_date, end_date)

        # 🧠 ML 기반 동적 파라미터 적용 (백테스트 시작 전)
        self.apply_ml_dynamic_parameters()

        # 가격 데이터 로드
        price_data = self._load_price_data()

        # 신호와 가격 데이터 정렬
        aligned_data = self._align_signals_and_prices(signals, price_data)

        print(f"   📊 백테스팅 데이터: {len(aligned_data)}개 포인트")
        
        # 메인 백테스팅 루프
        for i, (timestamp, row) in enumerate(aligned_data.iterrows()):
            self._process_signal(timestamp, row, aligned_data, i)
        
        # 마지막 포지션 청산
        if self.current_position:
            self._close_final_position(aligned_data.iloc[-1])
        
        # 결과 분석
        results = self._analyze_results()
        
        # 결과 출력
        self._print_results(results)
        
        return results

    def apply_ml_dynamic_parameters(self, market_analysis: dict = None):
        """
        🧠 ML 기반 동적 파라미터 적용

        Args:
            market_analysis: 시장 분석 결과 (슬라이딩 윈도우에서 전달)
        """
        if not self.ml_adaptive_params:
            return

        print(f"🧠 ML 기반 동적 파라미터 적용 중...")

        if market_analysis:
            # 슬라이딩 윈도우에서 전달된 분석 결과 사용
            settings = market_analysis.get('recommended_settings', {})
            self.leverage = settings.get('leverage', self.leverage)
            self.stop_loss_pct = settings.get('stop_loss_pct', self.stop_loss_pct)
            self.take_profit_pct = settings.get('take_profit_pct', self.take_profit_pct)
            self.ml_adaptive_tp = settings.get('ml_adaptive_tp', self.ml_adaptive_tp)

            market_type = market_analysis.get('market_type', 'unknown')
            print(f"   📊 시장 유형: {market_type}")
        else:
            # 기본 ML 파라미터 계산 (간단한 버전)
            volatility = 0.6  # 기본 변동성

            # 동적 레버리지 계산
            base_leverage = 6.0 / max(volatility, 0.3)
            self.leverage = max(self.leverage_range[0], min(self.leverage_range[1], base_leverage))

            # 동적 손절 계산
            base_stop_loss = volatility * 0.05
            self.stop_loss_pct = max(self.stop_loss_range[0], min(self.stop_loss_range[1], base_stop_loss))

            print(f"   📊 기본 ML 파라미터 적용")

        print(f"   ⚙️ 적용된 설정: {self.leverage:.1f}x 레버리지, {self.stop_loss_pct*100:.1f}% 손절, {'ML 동적 익절' if self.ml_adaptive_tp else f'{self.take_profit_pct*100:.1f}% 익절'}")

    def _load_price_data(self) -> pd.DataFrame:
        """가격 데이터 로드"""
        try:
            price_data = pd.read_csv('data/BTCUSDT_15m.csv')
            price_data['timestamp'] = pd.to_datetime(price_data['timestamp'])
            price_data.set_index('timestamp', inplace=True)
            return price_data
        except FileNotFoundError:
            print("❌ 가격 데이터 파일을 찾을 수 없습니다.")
            raise
    
    def _align_signals_and_prices(self, signals: pd.DataFrame, prices: pd.DataFrame) -> pd.DataFrame:
        """
        신호와 가격 데이터 정렬 (Lookahead Bias 완전 제거)

        핵심 수정: 신호 시점의 다음 캔들 가격을 사용하여 현실적인 거래 시뮬레이션
        """
        # 인덱스 타입 통일 (datetime으로 변환)
        if not isinstance(signals.index, pd.DatetimeIndex):
            signals.index = pd.to_datetime(signals.index)
        if not isinstance(prices.index, pd.DatetimeIndex):
            prices.index = pd.to_datetime(prices.index)

        # 🔧 핵심 수정: 신호 시점에서 다음 캔들 가격 매핑
        aligned_data = []

        for signal_time in signals.index:
            # 현재 신호 데이터
            signal_row = signals.loc[signal_time]

            # 🔧 다음 캔들 시간 계산 (15분 후)
            next_candle_time = signal_time + pd.Timedelta(minutes=15)

            # 다음 캔들 가격 데이터 찾기
            if next_candle_time in prices.index:
                next_price = prices.loc[next_candle_time]

                # 신고 데이터 + 다음 캔들 가격 결합
                combined_row = signal_row.copy()
                combined_row['execution_time'] = next_candle_time
                combined_row['open'] = next_price.get('open', next_price.get('Open', next_price.iloc[0] if hasattr(next_price, 'iloc') else next_price))
                combined_row['high'] = next_price.get('high', next_price.get('High', combined_row['open']))      # 손절/익절 체크용
                combined_row['low'] = next_price.get('low', next_price.get('Low', combined_row['open']))        # 손절/익절 체크용
                combined_row['close'] = next_price.get('close', next_price.get('Close', combined_row['open']))    # 참고용

                aligned_data.append(combined_row)
            else:
                # 다음 캔들이 없는 경우 (마지막 신호) - 현재 캔들 사용
                if signal_time in prices.index:
                    current_price = prices.loc[signal_time]
                    combined_row = signal_row.copy()
                    combined_row['execution_time'] = signal_time
                    combined_row['open'] = current_price.get('open', current_price.get('Open', current_price.iloc[0] if hasattr(current_price, 'iloc') else current_price))
                    combined_row['high'] = current_price.get('high', current_price.get('High', combined_row['open']))
                    combined_row['low'] = current_price.get('low', current_price.get('Low', combined_row['open']))
                    combined_row['close'] = current_price.get('close', current_price.get('Close', combined_row['open']))
                    aligned_data.append(combined_row)

        if not aligned_data:
            print(f"   ❌ 정렬 가능한 데이터가 없습니다!")
            return pd.DataFrame()

        # DataFrame으로 변환
        aligned = pd.DataFrame(aligned_data, index=signals.index[:len(aligned_data)])

        print(f"   📊 정렬된 데이터: {len(aligned)}개 (Lookahead Bias 제거)")
        print(f"   🔧 모든 거래가 다음 캔들 시가에서 실행됩니다")

        return aligned
    
    def _process_signal(self, timestamp: str, row: pd.Series, data: pd.DataFrame, index: int) -> None:
        """
        신호 처리 (Lookahead Bias 완전 제거)

        🔧 수정: 이미 정렬된 데이터에서 실행 가격 직접 사용
        """
        action = row['action']
        position = row['position']
        confidence = row['confidence']

        # 🔧 수정: 이미 다음 캔들 가격으로 정렬된 데이터 사용
        execution_price = row['open']  # 이미 다음 캔들 시가로 설정됨
        
        # 포지션 변경 감지
        if self.current_position is None and action in ['BUY', 'SELL']:
            # 신규 포지션 진입
            self._open_position(timestamp, execution_price, action, confidence, row)
            
        elif self.current_position and action in ['BUY', 'SELL']:
            # 기존 포지션과 다른 신호 → Stop-and-Reverse
            if (self.current_position['type'] == 'LONG' and action == 'SELL') or \
               (self.current_position['type'] == 'SHORT' and action == 'BUY'):
                # 기존 포지션 청산
                self._close_position(timestamp, execution_price, 'SIGNAL_CHANGE', row)
                # 새 포지션 진입
                self._open_position(timestamp, execution_price, action, confidence, row)
        
        # 강제 청산 체크 (포지션이 있을 때) - 최우선 체크
        if self.current_position:
            # 1. 강제 청산 체크 (가장 중요)
            if self._check_liquidation(execution_price):
                self._liquidate_position(timestamp, execution_price, row)
                return  # 청산되면 더 이상 체크할 필요 없음

            # 2. 손절/익절 체크 (강제 청산이 없을 때만)
            if 'high' in row and 'low' in row:
                exit_reason = self._check_exit_conditions(row, data, index)
                if exit_reason:
                    self._close_position(timestamp, execution_price, exit_reason, row)
    
    def _open_position(self, timestamp: str, price: float, action: str, confidence: float, row: pd.Series) -> None:
        """🔧 개선: 현실적 제약이 적용된 포지션 진입"""

        # 1. 현실적 제약 체크
        if not self._check_trading_constraints(timestamp, price, confidence):
            return  # 제약 조건 위반 시 거래 취소

        position_type = 'LONG' if action == 'BUY' else 'SHORT'

        # 2. 🚀 공격적 포지션 크기 계산
        base_size = self.current_capital * self.max_position_size_pct  # 최대 100%

        # 3. 확신도 조정 (공격적으로)
        if self.leverage <= 4.0:
            confidence_multiplier = min(confidence * 1.8, 1.0)  # 더 공격적
        else:
            confidence_multiplier = min(confidence * 1.5, 0.9)  # 높은 레버리지에서도 공격적

        # 4. 실제 투입 자본 계산
        actual_capital_used = base_size * confidence_multiplier

        # 5. 🔧 동적 슬리피지 계산 (포지션 크기에 따라)
        dynamic_slippage = self._calculate_dynamic_slippage(actual_capital_used * self.leverage, price)

        # 6. 레버리지 적용된 포지션 가치
        position_size = actual_capital_used * self.leverage

        # 7. 현실적 제약 적용
        position_size = min(position_size, self.max_single_position_usd)  # 최대 포지션 크기 제한

        # 8. 🔧 현실적 비용 계산 (동적 슬리피지 포함)
        total_cost = position_size * (self.fee_rate + dynamic_slippage)

        # 9. 자본 부족 체크
        if self.current_capital < total_cost:
            print(f"   ❌ 자본 부족으로 거래 취소: 필요 ${total_cost:,.0f} > 보유 ${self.current_capital:,.0f}")
            return

        self.current_capital -= total_cost

        # 🧠 ML 기반 동적 익절 타겟 계산 (개선된 버전)
        ml_exit_info = None
        if self.ml_adaptive_tp and self.ml_exit_predictor:
            try:
                # 현재 피처 추출 (개선된 방식)
                exclude_columns = ['final_signal', 'action', 'confidence', 'long_prob', 'short_prob',
                                 'execution_time', 'open', 'high', 'low', 'close', 'volume']

                # 숫자형 피처만 선택
                all_columns = row.index.tolist()
                feature_columns = [col for col in all_columns
                                 if col not in exclude_columns and pd.api.types.is_numeric_dtype(row[col])]

                if len(feature_columns) >= 5:  # 최소 5개 피처 필요
                    current_features = row[feature_columns]

                    # 결측값 확인 및 처리
                    if current_features.isna().any():
                        current_features = current_features.fillna(0)

                    # ML 익절 예측
                    ml_exit_info = self.ml_exit_predictor.predict_optimal_exit(
                        features=current_features,
                        current_price=price,
                        position_type=position_type,
                        confidence=confidence
                    )

                    if ml_exit_info:
                        print(f"   🧠 ML 익절 예측: {ml_exit_info['optimal_exit_range']*100:.1f}% "
                              f"(확률: {ml_exit_info['exit_probability']*100:.0f}%, {ml_exit_info['recommendation']})")
                else:
                    print(f"   ⚠️ 피처 부족 ({len(feature_columns)}개) - 기본 익절 사용")
                    ml_exit_info = None

            except Exception as e:
                print(f"   ⚠️ ML 익절 예측 실패: {e} - 기본 익절 사용")
                ml_exit_info = None

        self.current_position = {
            'type': position_type,
            'entry_time': timestamp,
            'entry_price': price,
            'position_size': position_size,
            'actual_capital': actual_capital_used,
            'normalized_leverage': self.leverage,
            'confidence': confidence,
            'dynamic_slippage': dynamic_slippage,
            'long_prob': row['long_prob'],
            'short_prob': row['short_prob'],
            'ml_exit_info': ml_exit_info  # ML 익절 정보 저장
        }

        # 10. 거래 횟수 업데이트
        self._update_daily_trade_count(timestamp)

        print(f"   🔧 {position_type} 진입: {timestamp} @ ${price:,.2f} "
              f"(포지션: ${position_size:,.0f}, 투입: ${actual_capital_used:,.0f}, "
              f"슬리피지: {dynamic_slippage*100:.3f}%)")

    def _check_trading_constraints(self, timestamp: str, price: float, confidence: float) -> bool:
        """현실적 거래 제약 조건 체크"""

        # 1. 일일 거래 횟수 제한
        trade_date = pd.to_datetime(timestamp).date()
        daily_count = self.daily_trade_count.get(trade_date, 0)

        if daily_count >= self.daily_trade_limit:
            print(f"   ❌ 일일 거래 한도 초과: {daily_count}/{self.daily_trade_limit}")
            return False

        # 2. 연속 손실 제한
        if self.consecutive_losses >= self.consecutive_loss_limit:
            print(f"   ❌ 연속 손실 한도 초과: {self.consecutive_losses}회")
            return False

        # 3. 최소 확신도 체크 (합리적 수준으로 조정)
        if confidence < 0.50:  # 50% 미만 확신도는 거래 안 함 (합리적 수준)
            print(f"   ❌ 확신도 부족: {confidence:.2f} < 0.50")
            return False

        # 4. 자본 여유도 체크
        if self.current_capital < self.initial_capital * 0.1:  # 초기 자본의 10% 미만
            print(f"   ❌ 자본 부족: ${self.current_capital:,.0f} < ${self.initial_capital*0.1:,.0f}")
            return False

        return True

    def _calculate_dynamic_slippage(self, position_value: float, price: float) -> float:
        """포지션 크기에 따른 동적 슬리피지 계산"""

        # 기본 슬리피지
        base_slippage = self.slippage

        # 포지션 크기에 따른 추가 슬리피지
        if position_value > 50000:  # $50K 이상
            size_impact = (position_value - 50000) / 1000000 * 0.001  # 0.1% per $1M
            additional_slippage = min(size_impact, self.max_slippage_pct - base_slippage)
        else:
            additional_slippage = 0

        total_slippage = base_slippage + additional_slippage

        return min(total_slippage, self.max_slippage_pct)

    def _update_daily_trade_count(self, timestamp: str):
        """일일 거래 횟수 업데이트"""
        trade_date = pd.to_datetime(timestamp).date()
        self.daily_trade_count[trade_date] = self.daily_trade_count.get(trade_date, 0) + 1
        self.last_trade_date = trade_date
    
    def _close_position(self, timestamp: str, price: float, exit_reason: str, row: pd.Series) -> None:
        """포지션 청산"""
        if not self.current_position:
            return
        
        position = self.current_position
        
        # 🔧 수정된 손익 계산 (레버리지 중복 적용 방지)
        if position['type'] == 'LONG':
            price_change_pct = (price - position['entry_price']) / position['entry_price']
        else:  # SHORT
            price_change_pct = (position['entry_price'] - price) / position['entry_price']

        # ✅ 레버리지는 이미 포지션 크기에 반영되어 있으므로 중복 적용하지 않음
        pnl = position['position_size'] * price_change_pct
        # 🔧 정규화된 레버리지 적용된 수익률 (표시용)
        normalized_leverage = position.get('normalized_leverage', self.leverage)
        leveraged_pnl_pct = price_change_pct * normalized_leverage
        
        # 🔧 수정된 수수료 및 슬리피지 차감 (포지션 크기 기준 - 레버리지 반영)
        total_cost = position['position_size'] * (self.fee_rate + self.slippage)
        net_pnl = pnl - total_cost
        
        # 자본 업데이트
        self.current_capital += net_pnl
        
        # 거래 기록
        trade = DualTrade(
            entry_time=position['entry_time'],
            exit_time=timestamp,
            entry_price=position['entry_price'],
            exit_price=price,
            position_type=position['type'],
            position_size=position['position_size'],
            pnl=net_pnl,
            pnl_pct=leveraged_pnl_pct * 100,
            exit_reason=exit_reason,
            long_confidence=position['long_prob'],
            short_confidence=position['short_prob']
        )
        
        self.trades.append(trade)
        
        print(f"   🔧 {position['type']} 청산: {timestamp} @ ${price:,.2f} "
              f"(손익: ${net_pnl:+,.0f}, {leveraged_pnl_pct*100:+.1f}%)")
        
        self.current_position = None

    def _check_liquidation(self, current_price: float) -> bool:
        """
        강제 청산 체크

        Args:
            current_price: 현재 가격

        Returns:
            True if liquidated, False otherwise
        """
        if not self.current_position:
            return False

        entry_price = self.current_position['entry_price']
        position_type = self.current_position['type']
        normalized_leverage = self.current_position.get('normalized_leverage', self.leverage)

        # 유지 증거금 비율 (일반적으로 0.5%)
        maintenance_margin = 0.005

        if position_type == 'LONG':
            # 롱 포지션 청산 가격
            liquidation_price = entry_price * (1 - (1/normalized_leverage) + maintenance_margin)
            return current_price <= liquidation_price
        else:
            # 숏 포지션 청산 가격
            liquidation_price = entry_price * (1 + (1/normalized_leverage) - maintenance_margin)
            return current_price >= liquidation_price

    def _liquidate_position(self, timestamp: str, price: float, row: pd.Series) -> None:
        """
        강제 청산 실행 (전액 손실)
        """
        if not self.current_position:
            return

        position = self.current_position

        # 강제 청산 시 전액 손실
        net_pnl = -position['actual_capital']  # 투입 자본 전액 손실

        # 자본 업데이트 (전액 손실)
        self.current_capital += net_pnl

        # 거래 기록
        trade = DualTrade(
            entry_time=position['entry_time'],
            exit_time=timestamp,
            entry_price=position['entry_price'],
            exit_price=price,
            position_type=position['type'],
            position_size=position['position_size'],
            pnl=net_pnl,
            pnl_pct=-100.0,  # 100% 손실
            exit_reason='LIQUIDATION',
            long_confidence=position['long_prob'],
            short_confidence=position['short_prob']
        )

        self.trades.append(trade)

        print(f"   💥 {position['type']} 강제청산: {timestamp} @ ${price:,.2f} "
              f"(손실: ${net_pnl:+,.0f}, -100.0%)")

        self.current_position = None

    def _check_exit_conditions(self, row: pd.Series, data: pd.DataFrame, index: int) -> str:
        """
        🧠 ML 기반 동적 청산 조건 체크

        ML이 예측한 최적 익절 범위와 확률을 고려한 지능형 청산 시스템
        """
        if not self.current_position:
            return None

        entry_price = self.current_position['entry_price']
        position_type = self.current_position['type']
        ml_exit_info = self.current_position.get('ml_exit_info')

        # 현재 캔들에서 손절/익절이 가능했는지 체크
        high_price = row['high']
        low_price = row['low']
        current_price = row['close']

        if position_type == 'LONG':
            # 롱 포지션 청산 조건
            stop_loss_price = entry_price * (1 - self.stop_loss_pct)

            # 🧠 ML 기반 동적 익절 가격 계산 (개선된 버전)
            if self.ml_adaptive_tp and ml_exit_info:
                # ML이 예측한 최적 익절 범위 사용
                ml_take_profit_range = ml_exit_info['optimal_exit_range']
                take_profit_price = entry_price * (1 + ml_take_profit_range)

                # 익절 확률 기반 지능형 로직
                exit_probability = ml_exit_info['exit_probability']
                current_return = (current_price - entry_price) / entry_price

                # 🎯 수익 극대화 로직
                if exit_probability > 0.8 and current_return >= ml_take_profit_range * 0.7:
                    # 높은 확률 + 목표의 70% 달성 → 조기 익절
                    if high_price >= entry_price * (1 + ml_take_profit_range * 0.7):
                        return 'ML_HIGH_PROB_EXIT'
                elif current_return >= ml_take_profit_range * 1.3:
                    # 목표를 30% 초과 달성 → 확장 익절
                    if high_price >= entry_price * (1 + ml_take_profit_range * 1.3):
                        return 'ML_EXTENDED_PROFIT'
                elif current_return >= ml_take_profit_range:
                    # 목표 달성 → 기본 익절
                    if high_price >= take_profit_price:
                        return 'ML_TARGET_PROFIT'
            else:
                # 기본 고정 익절
                take_profit_price = entry_price * (1 + self.take_profit_pct)

            # 손절 우선 체크
            if low_price <= stop_loss_price:
                return 'STOP_LOSS'
            elif high_price >= take_profit_price:
                return 'TAKE_PROFIT'

        else:
            # 숏 포지션 청산 조건
            stop_loss_price = entry_price * (1 + self.stop_loss_pct)

            # 🧠 ML 기반 동적 익절 가격 계산 (숏 포지션)
            if self.ml_adaptive_tp and ml_exit_info:
                ml_take_profit_range = ml_exit_info['optimal_exit_range']
                take_profit_price = entry_price * (1 - ml_take_profit_range)

                exit_probability = ml_exit_info['exit_probability']
                current_return = (entry_price - current_price) / entry_price

                # 🎯 수익 극대화 로직 (숏)
                if exit_probability > 0.8 and current_return >= ml_take_profit_range * 0.7:
                    if low_price <= entry_price * (1 - ml_take_profit_range * 0.7):
                        return 'ML_HIGH_PROB_EXIT'
                elif current_return >= ml_take_profit_range * 1.3:
                    if low_price <= entry_price * (1 - ml_take_profit_range * 1.3):
                        return 'ML_EXTENDED_PROFIT'
                elif current_return >= ml_take_profit_range:
                    if low_price <= take_profit_price:
                        return 'ML_TARGET_PROFIT'
            else:
                # 기본 고정 익절
                take_profit_price = entry_price * (1 - self.take_profit_pct)

            # 손절 우선 체크
            if high_price >= stop_loss_price:
                return 'STOP_LOSS'
            elif low_price <= take_profit_price:
                return 'TAKE_PROFIT'

        return None

    def _calculate_corrected_sharpe_ratio(self, risk_free_rate: float = 0.03) -> float:
        """
        🔧 현실적인 샤프 비율 계산 (거래별 수익률 기준)

        Args:
            risk_free_rate: 무위험 수익률 (연율, 기본 3%)

        Returns:
            현실적으로 계산된 샤프 비율
        """
        if not self.trades or len(self.trades) < 5:
            return 0.0

        # 1. 거래별 수익률 수집 (%)
        trade_returns = [trade.pnl_pct / 100 for trade in self.trades]  # 소수점으로 변환

        if len(trade_returns) < 5:
            return 0.0

        # 2. 거래 기간 계산 (연단위)
        start_time = pd.to_datetime(self.trades[0].entry_time)
        end_time = pd.to_datetime(self.trades[-1].exit_time)
        period_years = (end_time - start_time).days / 365.25

        if period_years <= 0:
            return 0.0

        # 3. 연환산 수익률 계산
        total_return = sum(trade_returns)
        annual_return = total_return / period_years

        # 4. 거래별 수익률의 변동성 (연환산)
        returns_std = np.std(trade_returns)
        if returns_std == 0 or np.isnan(returns_std):
            return 0.0

        # 거래 빈도 고려한 연환산 변동성
        trades_per_year = len(self.trades) / period_years
        annual_std = returns_std * np.sqrt(trades_per_year)

        # 5. 초과 수익률
        excess_return = annual_return - risk_free_rate

        # 6. 샤프 비율 계산
        sharpe_ratio = excess_return / annual_std

        # 7. 현실적 범위로 제한
        if sharpe_ratio > 3.0:
            print(f"   ⚠️ 높은 샤프 비율 감지: {sharpe_ratio:.2f} → 3.0으로 제한")
            sharpe_ratio = 3.0
        elif sharpe_ratio < -3.0:
            sharpe_ratio = -3.0

        return sharpe_ratio

    def _close_final_position(self, final_row: pd.Series) -> None:
        """
        마지막 포지션 청산 (Lookahead Bias 제거)

        🔧 수정: 마지막 캔들에서는 시가로 청산 (더 현실적)
        """
        if self.current_position:
            # 마지막 캔들에서는 시가로 청산 (종가는 미래 정보)
            self._close_position(
                final_row.name,
                final_row['open'],  # 🔧 수정: close → open
                'END_OF_PERIOD',
                final_row
            )
    
    def _analyze_results(self) -> Dict:
        """결과 분석"""
        if not self.trades:
            return {
                'error': '거래 기록이 없습니다.',
                'total_return': 0.0,
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': 0.0,
                'avg_win': 0.0,
                'avg_loss': 0.0,
                'profit_factor': 0.0,
                'max_drawdown': 0.0,
                'sharpe_ratio': 0.0,
                'final_capital': self.initial_capital,
                'equity_curve': [self.initial_capital]
            }
        
        # 기본 통계
        total_trades = len(self.trades)
        winning_trades = len([t for t in self.trades if t.pnl > 0])
        losing_trades = len([t for t in self.trades if t.pnl < 0])
        
        # 수익률 계산
        total_return = (self.current_capital - self.initial_capital) / self.initial_capital * 100
        
        # 거래별 수익률
        returns = [t.pnl_pct for t in self.trades]
        
        # 최대 낙폭 계산
        equity_curve = [self.initial_capital]
        for trade in self.trades:
            equity_curve.append(equity_curve[-1] + trade.pnl)
        
        peak = equity_curve[0]
        max_drawdown = 0
        for equity in equity_curve:
            if equity > peak:
                peak = equity
            drawdown = (peak - equity) / peak
            max_drawdown = max(max_drawdown, drawdown)
        
        # 🔧 수정된 샤프 비율 계산 (올바른 방식)
        sharpe_ratio = self._calculate_corrected_sharpe_ratio()
        
        results = {
            'total_return': total_return,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': winning_trades / total_trades * 100 if total_trades > 0 else 0,
            'avg_win': np.mean([t.pnl_pct for t in self.trades if t.pnl > 0]) if winning_trades > 0 else 0,
            'avg_loss': np.mean([t.pnl_pct for t in self.trades if t.pnl < 0]) if losing_trades > 0 else 0,
            'profit_factor': abs(sum([t.pnl for t in self.trades if t.pnl > 0]) / 
                               sum([t.pnl for t in self.trades if t.pnl < 0])) if losing_trades > 0 else float('inf'),
            'max_drawdown': max_drawdown * 100,
            'sharpe_ratio': sharpe_ratio,
            'final_capital': self.current_capital,
            'equity_curve': equity_curve
        }
        
        return results
    
    def _print_results(self, results: Dict) -> None:
        """결과 출력"""
        print(f"\n🏆 듀얼 모델 백테스팅 결과")
        print(f"=" * 50)
        print(f"📈 총 수익률: {results['total_return']:+.2f}%")
        print(f"💰 최종 자본: ${results['final_capital']:,.0f}")
        print(f"📊 총 거래: {results['total_trades']}회")
        print(f"🎯 승률: {results['win_rate']:.1f}%")
        print(f"📈 평균 수익: {results['avg_win']:+.2f}%")
        print(f"📉 평균 손실: {results['avg_loss']:+.2f}%")
        print(f"💎 손익비: {abs(results['avg_win']/results['avg_loss']):.2f}:1" if results['avg_loss'] != 0 else "∞:1")
        print(f"📉 최대 낙폭: {results['max_drawdown']:.2f}%")
        print(f"⚡ 샤프 비율: {results['sharpe_ratio']:.2f}")
    
    def plot_results(self, results: Dict) -> None:
        """결과 시각화"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 자본 곡선
        axes[0, 0].plot(results['equity_curve'])
        axes[0, 0].set_title('자본 곡선')
        axes[0, 0].set_ylabel('자본 ($)')
        
        # 거래별 수익률
        returns = [t.pnl_pct for t in self.trades]
        axes[0, 1].hist(returns, bins=20, alpha=0.7)
        axes[0, 1].set_title('거래별 수익률 분포')
        axes[0, 1].set_xlabel('수익률 (%)')
        
        # 포지션 타입별 성과
        long_trades = [t.pnl_pct for t in self.trades if t.position_type == 'LONG']
        short_trades = [t.pnl_pct for t in self.trades if t.position_type == 'SHORT']
        
        axes[1, 0].boxplot([long_trades, short_trades], labels=['LONG', 'SHORT'])
        axes[1, 0].set_title('포지션 타입별 성과')
        axes[1, 0].set_ylabel('수익률 (%)')
        
        # 월별 수익률
        monthly_returns = {}
        for trade in self.trades:
            month = pd.to_datetime(trade.exit_time).strftime('%Y-%m')  # YYYY-MM
            if month not in monthly_returns:
                monthly_returns[month] = []
            monthly_returns[month].append(trade.pnl_pct)
        
        months = list(monthly_returns.keys())
        monthly_avg = [np.mean(monthly_returns[m]) for m in months]
        
        axes[1, 1].bar(range(len(months)), monthly_avg)
        axes[1, 1].set_title('월별 평균 수익률')
        axes[1, 1].set_xticks(range(len(months)))
        axes[1, 1].set_xticklabels(months, rotation=45)
        
        plt.tight_layout()
        plt.savefig('dual_model_backtest_results.png', dpi=300, bbox_inches='tight')
        plt.show()


if __name__ == "__main__":
    """
    듀얼 모델 백테스팅 실행
    """
    print("🔄 Project LEVIATHAN - 듀얼 모델 백테스팅")
    print("=" * 70)
    
    try:
        # 백테스터 초기화
        backtester = DualModelBacktester(initial_capital=10000)
        
        # 백테스팅 실행
        results = backtester.run_backtest(
            start_date='2024-01-01',
            end_date='2024-12-31'
        )
        
        # 결과 시각화
        backtester.plot_results(results)
        
        print(f"\n🎯 다음 단계: Walk-Forward 검증")
        print(f"   python run_dual_model_walk_forward.py")
        
    except Exception as e:
        print(f"❌ 오류 발생: {e}")
        import traceback
        traceback.print_exc()
