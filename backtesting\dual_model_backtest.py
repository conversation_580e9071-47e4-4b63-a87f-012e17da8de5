"""
🐋 Project LEVIATHAN - 듀얼 모델 백테스팅 엔진

롱/숏 전문가 모델의 통합 신호를 백테스팅하는 전용 엔진입니다.
Stop-and-Reverse 로직과 포지션 전환을 정확히 시뮬레이션하여
듀얼 모델 시스템의 실제 성능을 검증합니다.

Author: 강현모
Date: 2024-12-14
Version: 1.0
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple
import sys
import os
from dataclasses import dataclass
from datetime import datetime

# 프로젝트 루트 경로 추가
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.dual_model_predictor import DualModelPredictor


@dataclass
class DualTrade:
    """듀얼 모델 거래 정보"""
    entry_time: str
    exit_time: str
    entry_price: float
    exit_price: float
    position_type: str  # 'LONG' or 'SHORT'
    position_size: float
    pnl: float
    pnl_pct: float
    exit_reason: str
    long_confidence: float
    short_confidence: float


class DualModelBacktester:
    """
    듀얼 모델 백테스팅 엔진
    
    롱/숏 전문가 모델의 통합 신호를 기반으로
    Stop-and-Reverse 전략을 백테스팅합니다.
    """
    
    def __init__(self, initial_capital: float = 10000, enable_art: bool = True):
        """
        초기화

        Args:
            initial_capital: 초기 자본금
            enable_art: ART (ML 동적) 시스템 활성화 여부
        """
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.current_position = None
        self.trades = []

        # ART 시스템 설정
        self.enable_art = enable_art

        if enable_art:
            # 🎯 ART 동적 설정 (사용자 요구사항)
            self.base_leverage = 4.0
            self.leverage_range = (2.0, 10.0)      # 2-10배 동적
            self.stop_loss_pct = 0.05              # 5% 고정 손절
            self.take_profit_range = (0.05, 0.15)  # 5-15% 동적 익절
            self.position_range = (0.3, 1.0)       # 30-100% 동적 포지션
            print(f"🎯 ART 시스템 활성화: 동적 레버리지 {self.leverage_range[0]}-{self.leverage_range[1]}배")
        else:
            # 기존 고정 설정
            self.leverage = 4.0
            self.stop_loss_pct = 0.02   # 2% 손절
            self.take_profit_pct = 0.05  # 5% 익절

        self.fee_rate = 0.0004      # 0.04% 수수료
        self.slippage = 0.0002      # 0.02% 슬리피지

        system_type = "ART (ML 동적)" if enable_art else "기본"
        print(f"🔄 듀얼 모델 백테스터 초기화 ({system_type}, 초기자본: ${initial_capital:,.0f})")

    def calculate_art_settings(self, row: pd.Series) -> Dict:
        """
        ART 동적 설정 계산 (사용자 요구사항 기반)

        Args:
            row: 현재 데이터 행 (신호 + 가격 정보)

        Returns:
            동적 설정 딕셔너리
        """
        if not self.enable_art:
            return {
                'leverage': self.leverage,
                'position_size_pct': 0.1,
                'stop_loss_pct': self.stop_loss_pct,
                'take_profit_pct': self.take_profit_pct
            }

        # 시장 변동성 추정 (가격 변화 기반)
        confidence = row.get('confidence', 0.5)

        # 🎯 사용자 요구사항: 동적 레버리지 (2-10배)
        if confidence > 0.8:  # 높은 확신도
            leverage = min(self.base_leverage * 2, self.leverage_range[1])  # 최대 10배
        elif confidence > 0.6:  # 중간 확신도
            leverage = self.base_leverage  # 4배
        else:  # 낮은 확신도
            leverage = max(self.base_leverage * 0.5, self.leverage_range[0])  # 최소 2배

        # 🎯 사용자 요구사항: 동적 포지션 크기 (30-100%)
        if confidence > 0.8:
            position_size_pct = self.position_range[1]  # 100%
        elif confidence > 0.6:
            position_size_pct = 0.7  # 70%
        elif confidence > 0.4:
            position_size_pct = 0.5  # 50%
        else:
            position_size_pct = self.position_range[0]  # 30%

        # 🎯 사용자 요구사항: 손절 5% 고정
        stop_loss_pct = self.stop_loss_pct  # 5%

        # 🎯 사용자 요구사항: 동적 익절 (5-15%)
        if confidence > 0.8:
            take_profit_pct = self.take_profit_range[1]  # 15%
        elif confidence > 0.6:
            take_profit_pct = 0.10  # 10%
        else:
            take_profit_pct = self.take_profit_range[0]  # 5%

        return {
            'leverage': leverage,
            'position_size_pct': position_size_pct,
            'stop_loss_pct': stop_loss_pct,
            'take_profit_pct': take_profit_pct,
            'confidence': confidence
        }

    def run_backtest(self, start_date: str = '2024-01-01', end_date: str = '2024-12-31') -> Dict:
        """
        듀얼 모델 백테스팅 실행
        
        Args:
            start_date: 시작 날짜
            end_date: 종료 날짜
            
        Returns:
            백테스팅 결과
        """
        print(f"🚀 듀얼 모델 백테스팅 시작: {start_date} ~ {end_date}")
        
        # 듀얼 모델 신호 생성
        dual_predictor = DualModelPredictor()
        signals = dual_predictor.generate_trading_signals(start_date, end_date)
        
        # 가격 데이터 로드
        price_data = self._load_price_data()
        
        # 신호와 가격 데이터 정렬
        aligned_data = self._align_signals_and_prices(signals, price_data)
        
        print(f"   📊 백테스팅 데이터: {len(aligned_data)}개 포인트")
        
        # 메인 백테스팅 루프
        for i, (timestamp, row) in enumerate(aligned_data.iterrows()):
            self._process_signal(timestamp, row, aligned_data, i)
        
        # 마지막 포지션 청산
        if self.current_position:
            self._close_final_position(aligned_data.iloc[-1])
        
        # 결과 분석
        results = self._analyze_results()
        
        # 결과 출력
        self._print_results(results)
        
        return results
    
    def _load_price_data(self) -> pd.DataFrame:
        """가격 데이터 로드"""
        try:
            price_data = pd.read_csv('data/BTCUSDT_15m.csv')
            price_data['timestamp'] = pd.to_datetime(price_data['timestamp'])
            price_data.set_index('timestamp', inplace=True)
            return price_data
        except FileNotFoundError:
            print("❌ 가격 데이터 파일을 찾을 수 없습니다.")
            raise
    
    def _align_signals_and_prices(self, signals: pd.DataFrame, prices: pd.DataFrame) -> pd.DataFrame:
        """
        신호와 가격 데이터 정렬 (Lookahead Bias 완전 제거)

        핵심 수정: 신호 시점의 다음 캔들 가격을 사용하여 현실적인 거래 시뮬레이션
        """
        # 인덱스 타입 통일 (datetime으로 변환)
        if not isinstance(signals.index, pd.DatetimeIndex):
            signals.index = pd.to_datetime(signals.index)
        if not isinstance(prices.index, pd.DatetimeIndex):
            prices.index = pd.to_datetime(prices.index)

        # 🔧 핵심 수정: 신호 시점에서 다음 캔들 가격 매핑
        aligned_data = []

        for signal_time in signals.index:
            # 현재 신호 데이터
            signal_row = signals.loc[signal_time]

            # 🔧 다음 캔들 시간 계산 (15분 후)
            next_candle_time = signal_time + pd.Timedelta(minutes=15)

            # 다음 캔들 가격 데이터 찾기
            if next_candle_time in prices.index:
                next_price = prices.loc[next_candle_time]

                # 신호 데이터 + 다음 캔들 가격 결합
                combined_row = signal_row.copy()
                combined_row['execution_time'] = next_candle_time
                combined_row['open'] = next_price['open']      # 실제 거래 가격
                combined_row['high'] = next_price['high']      # 손절/익절 체크용
                combined_row['low'] = next_price['low']        # 손절/익절 체크용
                combined_row['close'] = next_price['close']    # 참고용

                aligned_data.append(combined_row)
            else:
                # 다음 캔들이 없는 경우 (마지막 신호) - 현재 캔들 사용
                if signal_time in prices.index:
                    current_price = prices.loc[signal_time]
                    combined_row = signal_row.copy()
                    combined_row['execution_time'] = signal_time
                    combined_row['open'] = current_price['open']
                    combined_row['high'] = current_price['high']
                    combined_row['low'] = current_price['low']
                    combined_row['close'] = current_price['close']
                    aligned_data.append(combined_row)

        if not aligned_data:
            print(f"   ❌ 정렬 가능한 데이터가 없습니다!")
            return pd.DataFrame()

        # DataFrame으로 변환
        aligned = pd.DataFrame(aligned_data, index=signals.index[:len(aligned_data)])

        print(f"   📊 정렬된 데이터: {len(aligned)}개 (Lookahead Bias 제거)")
        print(f"   🔧 모든 거래가 다음 캔들 시가에서 실행됩니다")

        return aligned
    
    def _process_signal(self, timestamp: str, row: pd.Series, data: pd.DataFrame, index: int) -> None:
        """
        신호 처리 (Lookahead Bias 완전 제거)

        🔧 수정: 이미 정렬된 데이터에서 실행 가격 직접 사용
        """
        action = row['action']
        position = row['position']
        confidence = row['confidence']

        # 🔧 수정: 이미 다음 캔들 가격으로 정렬된 데이터 사용
        execution_price = row['open']  # 이미 다음 캔들 시가로 설정됨
        
        # 포지션 변경 감지
        if self.current_position is None and action in ['BUY', 'SELL']:
            # 신규 포지션 진입
            self._open_position(timestamp, execution_price, action, confidence, row)
            
        elif self.current_position and action in ['BUY', 'SELL']:
            # 기존 포지션과 다른 신호 → Stop-and-Reverse
            if (self.current_position['type'] == 'LONG' and action == 'SELL') or \
               (self.current_position['type'] == 'SHORT' and action == 'BUY'):
                # 기존 포지션 청산
                self._close_position(timestamp, execution_price, 'SIGNAL_CHANGE', row)
                # 새 포지션 진입
                self._open_position(timestamp, execution_price, action, confidence, row)
        
        # 손절/익절 체크 (포지션이 있을 때)
        if self.current_position:
            # 현재 row에 high/low가 있는지 확인
            if 'high' in row and 'low' in row:
                exit_reason = self._check_exit_conditions(row, data, index)
                if exit_reason:
                    self._close_position(timestamp, execution_price, exit_reason, row)
    
    def _open_position(self, timestamp: str, price: float, action: str, confidence: float, row: pd.Series) -> None:
        """포지션 진입 (ART 동적 설정 적용)"""
        position_type = 'LONG' if action == 'BUY' else 'SHORT'

        # 🎯 ART 동적 설정 계산
        art_settings = self.calculate_art_settings(row)

        # 🎯 동적 포지션 크기 계산 (30-100% 범위)
        base_size = self.current_capital * art_settings['position_size_pct']
        position_size = base_size * art_settings['leverage']

        # 수수료 및 슬리피지 적용
        total_cost = position_size * (self.fee_rate + self.slippage)
        self.current_capital -= total_cost

        self.current_position = {
            'type': position_type,
            'entry_time': timestamp,
            'entry_price': price,
            'position_size': position_size,
            'confidence': confidence,
            'long_prob': row['long_prob'],
            'short_prob': row['short_prob'],
            'art_settings': art_settings  # ART 설정 저장
        }

        if self.enable_art:
            print(f"   🎯 {position_type} 진입 (ART): {timestamp} @ ${price:,.2f}")
            print(f"      레버리지: {art_settings['leverage']:.1f}배, 포지션: {art_settings['position_size_pct']*100:.0f}%, 크기: ${position_size:,.0f}")
        else:
            print(f"   📈 {position_type} 진입: {timestamp} @ ${price:,.2f} (크기: ${position_size:,.0f})")
    
    def _close_position(self, timestamp: str, price: float, exit_reason: str, row: pd.Series) -> None:
        """포지션 청산"""
        if not self.current_position:
            return
        
        position = self.current_position
        
        # 손익 계산
        if position['type'] == 'LONG':
            pnl_pct = (price - position['entry_price']) / position['entry_price']
        else:  # SHORT
            pnl_pct = (position['entry_price'] - price) / position['entry_price']
        
        # 레버리지 적용
        leveraged_pnl_pct = pnl_pct * self.leverage
        pnl = position['position_size'] * leveraged_pnl_pct
        
        # 수수료 및 슬리피지 차감
        total_cost = position['position_size'] * (self.fee_rate + self.slippage)
        net_pnl = pnl - total_cost
        
        # 자본 업데이트
        self.current_capital += net_pnl
        
        # 거래 기록
        trade = DualTrade(
            entry_time=position['entry_time'],
            exit_time=timestamp,
            entry_price=position['entry_price'],
            exit_price=price,
            position_type=position['type'],
            position_size=position['position_size'],
            pnl=net_pnl,
            pnl_pct=leveraged_pnl_pct * 100,
            exit_reason=exit_reason,
            long_confidence=position['long_prob'],
            short_confidence=position['short_prob']
        )
        
        self.trades.append(trade)
        
        print(f"   📉 {position['type']} 청산: {timestamp} @ ${price:,.2f} "
              f"(손익: ${net_pnl:+,.0f}, {leveraged_pnl_pct*100:+.1f}%)")
        
        self.current_position = None
    
    def _check_exit_conditions(self, row: pd.Series, data: pd.DataFrame, index: int) -> str:
        """
        청산 조건 체크 (ART 동적 설정 적용)
        """
        if not self.current_position:
            return None

        entry_price = self.current_position['entry_price']
        position_type = self.current_position['type']

        # 🎯 ART 동적 설정 사용 (저장된 설정 또는 새로 계산)
        if self.enable_art and 'art_settings' in self.current_position:
            art_settings = self.current_position['art_settings']
            stop_loss_pct = art_settings['stop_loss_pct']
            take_profit_pct = art_settings['take_profit_pct']
        else:
            # 기존 고정 설정
            stop_loss_pct = self.stop_loss_pct
            take_profit_pct = self.take_profit_pct if hasattr(self, 'take_profit_pct') else 0.05

        # 현재 캔들에서 손절/익절이 가능했는지 체크
        high_price = row['high']
        low_price = row['low']

        if position_type == 'LONG':
            # 롱 포지션: 저가로 손절, 고가로 익절 체크
            stop_loss_price = entry_price * (1 - stop_loss_pct)
            take_profit_price = entry_price * (1 + take_profit_pct)

            if low_price <= stop_loss_price:
                return 'STOP_LOSS'
            elif high_price >= take_profit_price:
                return 'TAKE_PROFIT'
        else:
            # 숏 포지션: 고가로 손절, 저가로 익절 체크
            stop_loss_price = entry_price * (1 + stop_loss_pct)
            take_profit_price = entry_price * (1 - take_profit_pct)

            if high_price >= stop_loss_price:
                return 'STOP_LOSS'
            elif low_price <= take_profit_price:
                return 'TAKE_PROFIT'

        return None
    
    def _close_final_position(self, final_row: pd.Series) -> None:
        """
        마지막 포지션 청산 (Lookahead Bias 제거)

        🔧 수정: 마지막 캔들에서는 시가로 청산 (더 현실적)
        """
        if self.current_position:
            # 마지막 캔들에서는 시가로 청산 (종가는 미래 정보)
            self._close_position(
                final_row.name,
                final_row['open'],  # 🔧 수정: close → open
                'END_OF_PERIOD',
                final_row
            )
    
    def _analyze_results(self) -> Dict:
        """결과 분석"""
        if not self.trades:
            return {
                'error': '거래 기록이 없습니다.',
                'total_return': 0.0,
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': 0.0,
                'avg_win': 0.0,
                'avg_loss': 0.0,
                'profit_factor': 0.0,
                'max_drawdown': 0.0,
                'sharpe_ratio': 0.0,
                'final_capital': self.initial_capital,
                'equity_curve': [self.initial_capital]
            }
        
        # 기본 통계
        total_trades = len(self.trades)
        winning_trades = len([t for t in self.trades if t.pnl > 0])
        losing_trades = len([t for t in self.trades if t.pnl < 0])
        
        # 수익률 계산
        total_return = (self.current_capital - self.initial_capital) / self.initial_capital * 100
        
        # 거래별 수익률
        returns = [t.pnl_pct for t in self.trades]
        
        # 최대 낙폭 계산
        equity_curve = [self.initial_capital]
        for trade in self.trades:
            equity_curve.append(equity_curve[-1] + trade.pnl)
        
        peak = equity_curve[0]
        max_drawdown = 0
        for equity in equity_curve:
            if equity > peak:
                peak = equity
            drawdown = (peak - equity) / peak
            max_drawdown = max(max_drawdown, drawdown)
        
        # 샤프 비율 (일일 기준 근사)
        if len(returns) > 1:
            sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(365) if np.std(returns) > 0 else 0
        else:
            sharpe_ratio = 0
        
        results = {
            'total_return': total_return,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': winning_trades / total_trades * 100 if total_trades > 0 else 0,
            'avg_win': np.mean([t.pnl_pct for t in self.trades if t.pnl > 0]) if winning_trades > 0 else 0,
            'avg_loss': np.mean([t.pnl_pct for t in self.trades if t.pnl < 0]) if losing_trades > 0 else 0,
            'profit_factor': abs(sum([t.pnl for t in self.trades if t.pnl > 0]) / 
                               sum([t.pnl for t in self.trades if t.pnl < 0])) if losing_trades > 0 else float('inf'),
            'max_drawdown': max_drawdown * 100,
            'sharpe_ratio': sharpe_ratio,
            'final_capital': self.current_capital,
            'equity_curve': equity_curve
        }
        
        return results
    
    def _print_results(self, results: Dict) -> None:
        """결과 출력"""
        print(f"\n🏆 듀얼 모델 백테스팅 결과")
        print(f"=" * 50)
        print(f"📈 총 수익률: {results['total_return']:+.2f}%")
        print(f"💰 최종 자본: ${results['final_capital']:,.0f}")
        print(f"📊 총 거래: {results['total_trades']}회")
        print(f"🎯 승률: {results['win_rate']:.1f}%")
        print(f"📈 평균 수익: {results['avg_win']:+.2f}%")
        print(f"📉 평균 손실: {results['avg_loss']:+.2f}%")
        print(f"💎 손익비: {abs(results['avg_win']/results['avg_loss']):.2f}:1" if results['avg_loss'] != 0 else "∞:1")
        print(f"📉 최대 낙폭: {results['max_drawdown']:.2f}%")
        print(f"⚡ 샤프 비율: {results['sharpe_ratio']:.2f}")
    
    def plot_results(self, results: Dict) -> None:
        """결과 시각화"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 자본 곡선
        axes[0, 0].plot(results['equity_curve'])
        axes[0, 0].set_title('자본 곡선')
        axes[0, 0].set_ylabel('자본 ($)')
        
        # 거래별 수익률
        returns = [t.pnl_pct for t in self.trades]
        axes[0, 1].hist(returns, bins=20, alpha=0.7)
        axes[0, 1].set_title('거래별 수익률 분포')
        axes[0, 1].set_xlabel('수익률 (%)')
        
        # 포지션 타입별 성과
        long_trades = [t.pnl_pct for t in self.trades if t.position_type == 'LONG']
        short_trades = [t.pnl_pct for t in self.trades if t.position_type == 'SHORT']
        
        axes[1, 0].boxplot([long_trades, short_trades], labels=['LONG', 'SHORT'])
        axes[1, 0].set_title('포지션 타입별 성과')
        axes[1, 0].set_ylabel('수익률 (%)')
        
        # 월별 수익률
        monthly_returns = {}
        for trade in self.trades:
            month = trade.exit_time[:7]  # YYYY-MM
            if month not in monthly_returns:
                monthly_returns[month] = []
            monthly_returns[month].append(trade.pnl_pct)
        
        months = list(monthly_returns.keys())
        monthly_avg = [np.mean(monthly_returns[m]) for m in months]
        
        axes[1, 1].bar(range(len(months)), monthly_avg)
        axes[1, 1].set_title('월별 평균 수익률')
        axes[1, 1].set_xticks(range(len(months)))
        axes[1, 1].set_xticklabels(months, rotation=45)
        
        plt.tight_layout()
        plt.savefig('dual_model_backtest_results.png', dpi=300, bbox_inches='tight')
        plt.show()


if __name__ == "__main__":
    """
    듀얼 모델 백테스팅 실행
    """
    print("🔄 Project LEVIATHAN - 듀얼 모델 백테스팅")
    print("=" * 70)
    
    try:
        # 백테스터 초기화
        backtester = DualModelBacktester(initial_capital=10000)
        
        # 백테스팅 실행
        results = backtester.run_backtest(
            start_date='2024-01-01',
            end_date='2024-12-31'
        )
        
        # 결과 시각화
        backtester.plot_results(results)
        
        print(f"\n🎯 다음 단계: Walk-Forward 검증")
        print(f"   python run_dual_model_walk_forward.py")
        
    except Exception as e:
        print(f"❌ 오류 발생: {e}")
        import traceback
        traceback.print_exc()
