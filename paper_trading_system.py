#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 Project LEVIATHAN - 통합 페이퍼 트레이딩 시스템

실시간 데이터 → 신호 생성 → 자동 주문 실행
완전한 자동 거래 시스템

Author: 강현모
Date: 2024-12-15
Version: 1.0
"""

import time
import threading
from datetime import datetime, timedelta
from typing import Dict, Optional
import sys
from pathlib import Path

# 프로젝트 루트 추가
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from trading.order_executor import OrderExecutor
from trading.realtime_data_stream import RealTimeDataStream


class PaperTradingSystem:
    """
    통합 페이퍼 트레이딩 시스템
    
    구성 요소:
    1. 실시간 데이터 스트리밍
    2. 신호 생성 (기존 백테스트 모델 사용)
    3. 자동 주문 실행
    4. 성과 모니터링
    """
    
    def __init__(self, api_key: str, secret_key: str, symbol: str = "BTCUSDT"):
        self.symbol = symbol
        
        # 🎯 자동 주문 실행기
        self.order_executor = OrderExecutor(api_key, secret_key, testnet=True)
        
        # 📡 실시간 데이터 스트림
        self.data_stream = RealTimeDataStream(symbol, self._on_new_data)
        
        # 시스템 상태
        self.is_running = False
        self.start_time = None
        self.signal_count = 0
        self.executed_orders = 0
        
        # 성과 추적
        self.initial_balance = 0.0
        self.current_balance = 0.0
        self.total_pnl = 0.0
        
        # 거래 설정 (백테스트와 동일)
        self.trading_config = {
            'leverage_range': (2.0, 10.0),
            'stop_loss_range': (0.01, 0.05),
            'take_profit_range': (0.02, 0.15),
            'long_threshold': 0.402,
            'short_threshold': 0.165,
            'confidence_threshold': 0.2,
            'position_size': 0.1  # 10% 포지션 크기
        }
        
        print("🚀 통합 페이퍼 트레이딩 시스템 초기화 완료")
    
    def start_trading(self):
        """페이퍼 트레이딩 시작"""
        print("\n🎯 페이퍼 트레이딩 시작!")
        print("=" * 60)
        
        # 초기 계정 정보 확인
        if not self._initialize_account():
            print("❌ 계정 초기화 실패")
            return False
        
        # 실시간 데이터 스트림 시작
        self.data_stream.start_stream()
        
        # 시스템 상태 설정
        self.is_running = True
        self.start_time = datetime.now()
        
        print(f"✅ 페이퍼 트레이딩 시작됨")
        print(f"📊 초기 잔고: ${self.initial_balance:,.2f}")
        print(f"⚙️ 거래 설정:")
        print(f"   신뢰도 임계값: {self.trading_config['confidence_threshold']:.1%}")
        print(f"   포지션 크기: {self.trading_config['position_size']:.1%}")
        print(f"   레버리지 범위: {self.trading_config['leverage_range'][0]}-{self.trading_config['leverage_range'][1]}x")
        
        return True
    
    def stop_trading(self):
        """페이퍼 트레이딩 중지"""
        print("\n⏹️ 페이퍼 트레이딩 중지...")
        
        self.is_running = False
        
        # 데이터 스트림 중지
        self.data_stream.stop_stream()
        
        # 모든 포지션 청산 (선택사항)
        # self.order_executor.emergency_stop()
        
        # 최종 성과 리포트
        self._generate_final_report()
        
        print("✅ 페이퍼 트레이딩 중지됨")
    
    def _initialize_account(self) -> bool:
        """계정 초기화"""
        print("📊 계정 정보 초기화...")
        
        try:
            account_info = self.order_executor.get_account_info()
            
            if account_info:
                self.initial_balance = float(account_info['totalWalletBalance'])
                self.current_balance = self.initial_balance
                
                print(f"   ✅ 계정 연결 성공")
                print(f"   💰 초기 잔고: ${self.initial_balance:,.2f} USDT")
                
                return True
            else:
                print("   ❌ 계정 정보 조회 실패")
                return False
                
        except Exception as e:
            print(f"   ❌ 계정 초기화 오류: {e}")
            return False
    
    def _on_new_data(self, interval: str, candle_data: Dict):
        """새로운 데이터 수신 시 호출"""
        if not self.is_running:
            return
        
        # 15분봉에서만 신호 생성
        if interval == "15m":
            try:
                # 간단한 신호 생성 (실제로는 ML 모델 사용)
                signal = self._generate_simple_signal(candle_data)
                
                if signal:
                    self.signal_count += 1
                    
                    # 신호 출력
                    self._print_signal(signal)
                    
                    # 주문 실행
                    if self._should_execute_signal(signal):
                        success = self.order_executor.execute_signal(signal)
                        
                        if success:
                            self.executed_orders += 1
                            print(f"✅ 주문 실행 성공 (총 {self.executed_orders}개)")
                        else:
                            print(f"❌ 주문 실행 실패")
                
            except Exception as e:
                print(f"❌ 신호 처리 오류: {e}")
    
    def _generate_simple_signal(self, candle_data: Dict) -> Optional[Dict]:
        """간단한 신호 생성 (ML 모델 대신 임시 사용)"""
        
        # 현재 가격
        current_price = candle_data['close']
        
        # 간단한 신호 로직 (실제로는 ML 모델 사용)
        import random
        
        # 랜덤 신호 생성 (데모용)
        signal_type = random.choice(['BUY', 'SELL', 'HOLD', 'HOLD', 'HOLD'])  # 대부분 HOLD
        confidence = random.uniform(0.1, 0.8)
        
        if signal_type == 'HOLD' or confidence < self.trading_config['confidence_threshold']:
            return None
        
        # 동적 파라미터 계산
        leverage = random.uniform(*self.trading_config['leverage_range'])
        stop_loss_pct = random.uniform(*self.trading_config['stop_loss_range'])
        take_profit_pct = random.uniform(*self.trading_config['take_profit_range'])
        
        signal = {
            'timestamp': datetime.now(),
            'symbol': self.symbol,
            'signal_type': signal_type,
            'confidence': confidence,
            'leverage': leverage,
            'position_size': self.trading_config['position_size'],
            'stop_loss_pct': stop_loss_pct,
            'take_profit_pct': take_profit_pct,
            'current_price': current_price
        }
        
        return signal
    
    def _should_execute_signal(self, signal: Dict) -> bool:
        """신호 실행 여부 결정"""
        
        # 신뢰도 확인
        if signal['confidence'] < self.trading_config['confidence_threshold']:
            return False
        
        # 시스템 상태 확인
        if self.order_executor.is_emergency_stop:
            return False
        
        # 최대 포지션 수 확인
        if len(self.order_executor.active_positions) >= self.order_executor.max_open_positions:
            return False
        
        return True
    
    def _print_signal(self, signal: Dict):
        """신호 정보 출력"""
        timestamp = signal['timestamp'].strftime('%H:%M:%S')
        signal_type = signal['signal_type']
        confidence = signal['confidence']
        price = signal['current_price']
        
        print(f"\n🎯 [{timestamp}] 신호 생성: {signal_type}")
        print(f"   💰 가격: ${price:,.2f}")
        print(f"   📊 신뢰도: {confidence:.3f}")
        print(f"   ⚙️ 레버리지: {signal['leverage']:.1f}x")
    
    def _update_performance(self):
        """성과 업데이트"""
        try:
            account_info = self.order_executor.get_account_info()
            
            if account_info:
                self.current_balance = float(account_info['totalWalletBalance'])
                self.total_pnl = self.current_balance - self.initial_balance
                
        except Exception as e:
            print(f"⚠️ 성과 업데이트 오류: {e}")
    
    def print_status(self):
        """시스템 상태 출력"""
        if not self.is_running:
            print("⏹️ 시스템 중지 상태")
            return
        
        # 성과 업데이트
        self._update_performance()
        
        # 운영 시간 계산
        runtime = datetime.now() - self.start_time
        hours = int(runtime.total_seconds() // 3600)
        minutes = int((runtime.total_seconds() % 3600) // 60)
        
        print(f"\n📊 페이퍼 트레이딩 상태:")
        print(f"   ⏰ 운영 시간: {hours}시간 {minutes}분")
        print(f"   🎯 생성된 신호: {self.signal_count}개")
        print(f"   📝 실행된 주문: {self.executed_orders}개")
        print(f"   💰 현재 잔고: ${self.current_balance:,.2f}")
        print(f"   📈 총 PnL: ${self.total_pnl:+,.2f} ({self.total_pnl/self.initial_balance:+.1%})")
        print(f"   📍 활성 포지션: {len(self.order_executor.active_positions)}개")
        
        # 데이터 스트림 상태
        stream_status = self.data_stream.get_status()
        print(f"   📡 데이터 스트림: {'✅ 정상' if stream_status['is_running'] else '❌ 중단'}")
        print(f"   📊 15분봉 버퍼: {stream_status['15m_buffer_size']}개")
    
    def _generate_final_report(self):
        """최종 성과 리포트 생성"""
        self._update_performance()
        
        runtime = datetime.now() - self.start_time if self.start_time else timedelta(0)
        hours = runtime.total_seconds() / 3600
        
        print(f"\n📋 최종 페이퍼 트레이딩 리포트")
        print("=" * 50)
        print(f"🕐 운영 기간: {runtime}")
        print(f"💰 초기 잔고: ${self.initial_balance:,.2f}")
        print(f"💵 최종 잔고: ${self.current_balance:,.2f}")
        print(f"📈 총 수익률: {self.total_pnl/self.initial_balance:+.2%}")
        print(f"📊 총 PnL: ${self.total_pnl:+,.2f}")
        print(f"🎯 생성된 신호: {self.signal_count}개")
        print(f"📝 실행된 주문: {self.executed_orders}개")
        print(f"⚡ 신호 실행률: {self.executed_orders/max(self.signal_count, 1)*100:.1f}%")
        
        if hours > 0:
            print(f"📈 시간당 신호: {self.signal_count/hours:.1f}개")
            print(f"📝 시간당 주문: {self.executed_orders/hours:.1f}개")


def main():
    print("🚀 Project LEVIATHAN - 통합 페이퍼 트레이딩 시스템")
    print("=" * 70)
    print("🎯 목표: 실시간 자동 거래 시스템 테스트")
    print()
    
    # API 키 설정
    API_KEY = "30f3132331bef6575c30cd4ec053b90d6e4c4e005cc827066e424c992ff86ccc"
    SECRET_KEY = "ed604353c26ccc02d6180bf32f5f402eeaa0ebc6963ac02ead2c32c654dbba46"
    
    # 페이퍼 트레이딩 시스템 초기화
    trading_system = PaperTradingSystem(API_KEY, SECRET_KEY, "BTCUSDT")
    
    try:
        # 페이퍼 트레이딩 시작
        if trading_system.start_trading():
            print(f"\n⏰ 60초간 페이퍼 트레이딩 테스트...")
            print(f"💡 실시간 신호 생성 및 자동 주문 실행")
            print(f"🔄 Ctrl+C로 중단 가능")
            
            # 60초간 실행하면서 10초마다 상태 출력
            for i in range(6):
                time.sleep(10)
                print(f"\n⏰ {(i+1)*10}초 경과...")
                trading_system.print_status()
            
            print(f"\n✅ 60초 테스트 완료!")
        
    except KeyboardInterrupt:
        print(f"\n⏹️ 사용자가 중단했습니다")
    
    except Exception as e:
        print(f"\n❌ 시스템 오류: {e}")
    
    finally:
        trading_system.stop_trading()
        print(f"\n🎉 페이퍼 트레이딩 시스템 테스트 완료!")


if __name__ == "__main__":
    main()
