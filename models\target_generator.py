"""
Project LEVIATHAN - 타겟 변수 생성 모듈
다양한 타겟 변수 생성 전략을 제공합니다.
"""

import pandas as pd
import numpy as np
import sys
from pathlib import Path
from typing import Dict, Tuple, Optional

# 프로젝트 루트를 Python 경로에 추가
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 설정값 하드코딩 (임시)
DATA_PATHS = {
    '15m': 'data/BTCUSDT_15m.csv',
    '4h': 'data/BTCUSDT_4h.csv'
}
STOP_LOSS_PCT = 0.02  # -2%
TAKE_PROFIT_PCT = 0.05  # +5%
MAX_HOLD_PERIOD = 96   # 96개 봉 (24시간) - 스윙 최적화


class TargetGenerator:
    """
    다양한 타겟 변수 생성 전략을 제공하는 클래스
    """
    
    def __init__(self, target_config: Optional[Dict] = None):
        """
        타겟 생성기 초기화
        
        Args:
            target_config: 타겟 생성 설정
        """
        self.target_config = target_config or {
            'forward_periods': [4, 8],  # 1시간, 2시간 후 (15분봉 기준) - 단순화
            'return_thresholds': {
                'strong_buy': 0.015,   # 1.5% 이상 상승
                'buy': 0.005,          # 0.5% 이상 상승
                'hold': 0.005,         # -0.5% ~ +0.5%
                'sell': -0.005,        # -0.5% 이하 하락
                'strong_sell': -0.015  # -1.5% 이하 하락
            },
            'volatility_adjustment': False,  # 변동성 기반 임계값 조정 비활성화 (속도 향상)
            'outlier_clip': 0.05  # 상하위 5% 이상치 제거
        }
        
    def load_price_data(self) -> pd.DataFrame:
        """
        원본 가격 데이터 로드
        
        Returns:
            15분봉 OHLCV 데이터프레임
        """
        price_data = pd.read_csv(DATA_PATHS['15m'], index_col='timestamp', parse_dates=True)
        return price_data
    
    def calculate_forward_returns(self, prices: pd.Series, periods: int) -> pd.Series:
        """
        미래 수익률 계산
        
        Args:
            prices: 가격 시리즈
            periods: 미래 기간 (15분봉 개수)
            
        Returns:
            미래 수익률 시리즈
        """
        future_prices = prices.shift(-periods)
        returns = (future_prices - prices) / prices
        return returns
    
    def calculate_volatility_adjusted_thresholds(self, returns: pd.Series, 
                                               base_thresholds: Dict[str, float],
                                               window: int = 96) -> pd.DataFrame:
        """
        변동성 기반 동적 임계값 계산 (96 = 24시간)
        
        Args:
            returns: 수익률 시리즈
            base_thresholds: 기본 임계값
            window: 변동성 계산 윈도우
            
        Returns:
            동적 임계값 데이터프레임
        """
        # 롤링 변동성 계산
        rolling_vol = returns.rolling(window=window).std()
        vol_multiplier = rolling_vol / rolling_vol.median()
        
        # 변동성 기반 임계값 조정
        adjusted_thresholds = pd.DataFrame(index=returns.index)
        for label, threshold in base_thresholds.items():
            adjusted_thresholds[label] = threshold * vol_multiplier
            
        return adjusted_thresholds
    
    def create_classification_target(self, returns: pd.Series, 
                                   thresholds: Optional[Dict] = None) -> pd.Series:
        """
        분류용 타겟 변수 생성 (5클래스)
        
        Args:
            returns: 수익률 시리즈
            thresholds: 분류 임계값
            
        Returns:
            분류 라벨 시리즈 (0: strong_sell, 1: sell, 2: hold, 3: buy, 4: strong_buy)
        """
        if thresholds is None:
            thresholds = self.target_config['return_thresholds']
            
        # 변동성 조정 적용
        if self.target_config['volatility_adjustment']:
            adj_thresholds = self.calculate_volatility_adjusted_thresholds(returns, thresholds)
            
            # 동적 임계값 기반 분류
            labels = pd.Series(2, index=returns.index)  # 기본값: hold
            
            for i in returns.index:
                if pd.isna(returns[i]) or i not in adj_thresholds.index:
                    labels[i] = 2  # hold
                    continue
                    
                ret = returns[i]
                thresh = adj_thresholds.loc[i]
                
                if ret >= thresh['strong_buy']:
                    labels[i] = 4  # strong_buy
                elif ret >= thresh['buy']:
                    labels[i] = 3  # buy
                elif ret <= thresh['strong_sell']:
                    labels[i] = 0  # strong_sell
                elif ret <= thresh['sell']:
                    labels[i] = 1  # sell
                else:
                    labels[i] = 2  # hold
        else:
            # 고정 임계값 기반 분류
            labels = pd.Series(2, index=returns.index)  # 기본값: hold
            
            labels[returns >= thresholds['strong_buy']] = 4  # strong_buy
            labels[(returns >= thresholds['buy']) & (returns < thresholds['strong_buy'])] = 3  # buy
            labels[returns <= thresholds['strong_sell']] = 0  # strong_sell
            labels[(returns <= thresholds['sell']) & (returns > thresholds['strong_sell'])] = 1  # sell
            
        return labels
    
    def create_regression_target(self, returns: pd.Series) -> pd.Series:
        """
        회귀용 타겟 변수 생성 (연속값)
        
        Args:
            returns: 수익률 시리즈
            
        Returns:
            클리핑된 수익률 시리즈
        """
        # 이상치 제거 (상하위 5%)
        if self.target_config['outlier_clip'] > 0:
            lower_bound = returns.quantile(self.target_config['outlier_clip'])
            upper_bound = returns.quantile(1 - self.target_config['outlier_clip'])
            clipped_returns = returns.clip(lower_bound, upper_bound)
        else:
            clipped_returns = returns.copy()
            
        return clipped_returns
    
    def generate_all_targets(self) -> Dict[str, pd.DataFrame]:
        """
        모든 타겟 변수 생성
        
        Returns:
            타겟 변수들이 포함된 딕셔너리
        """
        print("🎯 타겟 변수 생성을 시작합니다...")
        
        # 가격 데이터 로드
        price_data = self.load_price_data()
        close_prices = price_data['close']
        
        print(f"   📊 가격 데이터: {len(close_prices)}개 레코드")
        
        targets = {}
        
        # 각 예측 기간별로 타겟 생성
        for period in self.target_config['forward_periods']:
            period_name = f"{period * 15}min"  # 15분봉 기준 시간 변환
            
            print(f"   🔄 {period_name} 후 타겟 변수 생성 중...")
            
            # 미래 수익률 계산
            forward_returns = self.calculate_forward_returns(close_prices, period)
            
            # 분류 타겟
            classification_target = self.create_classification_target(forward_returns)
            
            # 회귀 타겟
            regression_target = self.create_regression_target(forward_returns)
            
            # 결과 저장
            targets[f'classification_{period_name}'] = classification_target
            targets[f'regression_{period_name}'] = regression_target
            targets[f'raw_returns_{period_name}'] = forward_returns
            
            # 통계 출력
            valid_count = classification_target.notna().sum()
            class_dist = classification_target.value_counts().sort_index()
            
            print(f"     ✅ 유효 샘플: {valid_count}개")
            print(f"     📊 클래스 분포: {dict(class_dist)}")
            
        print(f"✅ 타겟 변수 생성 완료! 총 {len(targets)}개 타겟")
        
        return targets

    def generate_triple_barrier_target(self, price_df: pd.DataFrame) -> pd.Series:
        """
        트리플 배리어 메소드를 사용하여 타겟 라벨을 생성합니다.

        Args:
            price_df: OHLCV 가격 데이터프레임

        Returns:
            타겟 라벨 시리즈 (1: 익절, -1: 손절, 0: 시간만료)
        """
        print("🎯 트리플 배리어 메소드로 타겟 생성 중...")

        df = price_df.copy()
        target = pd.Series(0, index=df.index)  # 기본값: 시간만료(0)

        # 리스크 파라미터
        tp_pct = TAKE_PROFIT_PCT  # +5% 익절
        sl_pct = STOP_LOSS_PCT    # -2% 손절
        max_period = MAX_HOLD_PERIOD  # 16개 봉 (4시간)

        print(f"   📈 익절 임계값: +{tp_pct*100:.1f}%")
        print(f"   📉 손절 임계값: -{sl_pct*100:.1f}%")
        print(f"   ⏰ 최대 보유 기간: {max_period}개 봉 ({max_period*15}분)")

        # 각 시점에 대해 트리플 배리어 적용
        total_points = len(df) - max_period
        processed = 0

        for i in range(total_points):
            entry_price = df['close'].iloc[i]
            tp_price = entry_price * (1 + tp_pct)  # 익절 가격
            sl_price = entry_price * (1 - sl_pct)  # 손절 가격

            # 미래 가격 확인 (1봉부터 max_period까지)
            for j in range(1, max_period + 1):
                if i + j >= len(df):
                    break

                future_high = df['high'].iloc[i + j]
                future_low = df['low'].iloc[i + j]

                # 익절 조건 확인 (상단 장벽)
                if future_high >= tp_price:
                    target.iloc[i] = 1  # 익절
                    break

                # 손절 조건 확인 (하단 장벽)
                if future_low <= sl_price:
                    target.iloc[i] = -1  # 손절
                    break

            # 진행률 출력
            processed += 1
            if processed % 10000 == 0:
                progress = processed / total_points * 100
                print(f"   🔄 진행률: {progress:.1f}% ({processed:,}/{total_points:,})")

        # 결과 통계
        target_counts = target.value_counts().sort_index()
        total_valid = target_counts.sum()

        print(f"\n   ✅ 트리플 배리어 타겟 생성 완료!")
        print(f"   📊 타겟 분포:")
        print(f"      손절(-1): {target_counts.get(-1, 0):,}개 ({target_counts.get(-1, 0)/total_valid*100:.1f}%)")
        print(f"      시간만료(0): {target_counts.get(0, 0):,}개 ({target_counts.get(0, 0)/total_valid*100:.1f}%)")
        print(f"      익절(1): {target_counts.get(1, 0):,}개 ({target_counts.get(1, 0)/total_valid*100:.1f}%)")

        return target

    def get_target_info(self) -> Dict:
        """
        타겟 변수 정보 반환
        
        Returns:
            타겟 변수 설정 정보
        """
        return {
            'config': self.target_config,
            'classification_labels': {
                0: 'strong_sell',
                1: 'sell', 
                2: 'hold',
                3: 'buy',
                4: 'strong_buy'
            },
            'forward_periods_minutes': [p * 15 for p in self.target_config['forward_periods']]
        }


if __name__ == "__main__":
    """
    타겟 생성기 테스트
    """
    print("🎯 Project LEVIATHAN - 타겟 변수 생성기 테스트")
    print("=" * 60)
    
    try:
        # 타겟 생성기 초기화
        target_gen = TargetGenerator()
        
        # 설정 정보 출력
        info = target_gen.get_target_info()
        print("📋 타겟 생성 설정:")
        print(f"   예측 기간: {info['forward_periods_minutes']} 분")
        print(f"   분류 라벨: {info['classification_labels']}")
        print(f"   수익률 임계값: {info['config']['return_thresholds']}")
        
        # 기존 타겟 변수 생성
        targets = target_gen.generate_all_targets()

        # 결과 요약
        print(f"\n📊 생성된 기존 타겟 변수:")
        for name, target in targets.items():
            if isinstance(target, pd.Series):
                print(f"   {name}: {len(target)}개 샘플")

        # 트리플 배리어 메소드 테스트
        print(f"\n" + "="*60)
        print(f"🎯 트리플 배리어 메소드 테스트")
        print(f"="*60)

        # 가격 데이터 로드
        price_data = target_gen.load_price_data()

        # 트리플 배리어 타겟 생성
        triple_barrier_target = target_gen.generate_triple_barrier_target(price_data)

        print(f"\n🎯 다음 단계: 트리플 배리어 타겟으로 LightGBM 모델 재훈련")
        
    except Exception as e:
        print(f"❌ 오류 발생: {e}")
        import traceback
        traceback.print_exc()
