"""
Project LEVIATHAN - 통합 설정 관리
전략별 설정, API 설정, 인프라 설정을 모두 관리하는 통합 모듈
"""

from typing import Dict, Any, Union
import importlib
from pathlib import Path

# 전략 설정 파일들 import (v2만 유지)
try:
    from .swing_s_v2 import swing_s_v2_config, SwingStrategyV2Config
    from .api_config import api_config
except ImportError:
    # 직접 실행 시 절대 import 사용
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent))

    from swing_s_v2 import swing_s_v2_config, SwingStrategyV2Config
    from api_config import api_config


class ConfigManager:
    """통합 설정 관리자"""

    def __init__(self):
        self.strategy_configs = {
            'swing_s_v2': swing_s_v2_config
        }

        self.strategy_classes = {
            'swing_s_v2': SwingStrategyV2Config
        }

    def get_strategy_config(self, strategy_name: str) -> SwingStrategyV2Config:
        """전략 설정 반환"""
        if strategy_name not in self.strategy_configs:
            raise ValueError(f"Unknown strategy: {strategy_name}. Available: {list(self.strategy_configs.keys())}")
        return self.strategy_configs[strategy_name]

    def get_api_config(self):
        """API 설정 반환"""
        return api_config

    def get_legacy_settings(self):
        """레거시 설정 반환 (기존 코드 호환성)"""
        return settings

    def list_strategies(self) -> list:
        """사용 가능한 전략 목록 반환"""
        return list(self.strategy_configs.keys())

    def get_strategy_info(self, strategy_name: str) -> Dict[str, Any]:
        """전략 정보 반환"""
        config = self.get_strategy_config(strategy_name)
        return {
            'name': config.strategy_name,
            'version': config.version,
            'description': config.description,
            'created_date': config.created_date,
            'timeframes': getattr(config, 'execution_timeframe', 'N/A'),
            'max_hold_hours': getattr(config, 'time_barrier_hours', 'N/A'),
            'leverage': getattr(config, 'leverage', 'N/A'),
            'stop_loss_pct': getattr(config, 'stop_loss_pct', 'N/A'),
            'take_profit_pct': getattr(config, 'take_profit_pct', 'N/A')
        }

    def print_all_strategies(self):
        """모든 전략 정보 출력"""
        print("🐋 Project LEVIATHAN - 사용 가능한 전략들")
        print("=" * 80)

        for strategy_name in self.list_strategies():
            info = self.get_strategy_info(strategy_name)
            print(f"\n📋 {info['name']} {info['version']}")
            print(f"   📝 설명: {info['description']}")
            print(f"   📊 시간프레임: {info['timeframes']}")
            print(f"   ⏰ 최대보유: {info['max_hold_hours']}시간")
            print(f"   ⚖️ 레버리지: {info['leverage']}x")
            print(f"   📉 손절: -{info['stop_loss_pct']*100:.1f}%")
            print(f"   📈 익절: +{info['take_profit_pct']*100:.1f}%")

    def validate_strategy_config(self, strategy_name: str) -> bool:
        """전략 설정 유효성 검증"""
        try:
            config = self.get_strategy_config(strategy_name)

            # 필수 속성 확인
            required_attrs = [
                'strategy_name', 'version', 'description',
                'stop_loss_pct', 'take_profit_pct', 'leverage',
                'max_hold_period', 'probability_threshold'
            ]

            for attr in required_attrs:
                if not hasattr(config, attr):
                    print(f"❌ Missing required attribute: {attr}")
                    return False

            # 값 범위 검증
            if config.stop_loss_pct <= 0 or config.stop_loss_pct >= 1:
                print(f"❌ Invalid stop_loss_pct: {config.stop_loss_pct}")
                return False

            if config.take_profit_pct <= 0 or config.take_profit_pct >= 1:
                print(f"❌ Invalid take_profit_pct: {config.take_profit_pct}")
                return False

            if config.leverage <= 0 or config.leverage > 20:
                print(f"❌ Invalid leverage: {config.leverage}")
                return False

            print(f"✅ Configuration '{strategy_name}' is valid")
            return True

        except Exception as e:
            print(f"❌ Configuration validation failed: {e}")
            return False


# 전역 설정 관리자 인스턴스
config_manager = ConfigManager()


# 편의 함수들 (기존 코드 호환성)
def get_config(strategy_name: str):
    """편의 함수: 전략 설정 반환"""
    return config_manager.get_strategy_config(strategy_name)


def list_strategies():
    """편의 함수: 전략 목록 반환"""
    return config_manager.list_strategies()


def print_strategies():
    """편의 함수: 전략 정보 출력"""
    config_manager.print_all_strategies()


def validate_strategy(strategy_name: str):
    """편의 함수: 전략 설정 검증"""
    return config_manager.validate_strategy_config(strategy_name)


def get_api_config():
    """편의 함수: API 설정 반환"""
    return config_manager.get_api_config()


def get_legacy_settings():
    """편의 함수: 레거시 설정 반환"""
    return config_manager.get_legacy_settings()


# 사용 예시
if __name__ == "__main__":
    # 모든 전략 정보 출력
    print_strategies()

    print("\n" + "="*80)
    print("🔍 설정 검증 결과")
    print("="*80)

    # 각 전략 설정 검증
    for strategy in list_strategies():
        validate_strategy(strategy)
        print()

    # API 설정 확인
    print("🔑 API 설정 상태:")
    api_cfg = get_api_config()
    if api_cfg:
        api_cfg.print_config_status()
