"""
Project LEVIATHAN - 이진 분류 모델 (손절 vs 익절)
시간만료 데이터를 제거하고 오직 거래 기회에서의 승부만 예측하는 전문가 모델
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import classification_report, confusion_matrix, f1_score, roc_auc_score
import optuna
import joblib
import sys
from pathlib import Path
from typing import Dict, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 프로젝트 루트를 Python 경로에 추가
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 모듈 import 수정
try:
    from data.features import generate_features_from_csv
    from models.target_generator import TargetGenerator
except ImportError as e:
    print(f"❌ Import 오류: {e}")
    print("📝 해결 방법: 프로젝트 루트에서 실행하거나 PYTHONPATH를 설정하세요.")
    sys.exit(1)


class BinaryTradingModel:
    """
    이진 분류 모델: 거래 기회에서 손절 vs 익절 예측
    "언제 거래하면 안 되는지"가 아닌 "거래할 때 이길 것인가?"에 집중
    """
    
    def __init__(self):
        """
        이진 분류 모델 초기화
        """
        self.model = None
        self.feature_importance = None
        self.best_params = None
        
        # 이진 분류 전용 하이퍼파라미터
        self.default_params = {
            'objective': 'binary',
            'metric': 'binary_logloss',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.9,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'is_unbalance': True,  # 손절 vs 익절 불균형 처리
            'verbose': -1,
            'random_state': 42
        }
        
        print(f"🎯 이진 분류 모델 초기화 (손절 vs 익절)")
        print(f"   🔍 핵심 질문: '거래 기회가 왔을 때, 이길 것인가 질 것인가?'")
        print(f"   ⚡ 전략: 시간만료 데이터 제거 → 순수 승부 예측")
        
    def prepare_binary_data(self) -> Tuple[pd.DataFrame, pd.Series]:
        """
        이진 분류용 데이터 준비 (시간만료 제거)
        
        Returns:
            (features, target) 튜플
        """
        print("🎯 이진 분류용 데이터 준비 중...")
        
        # 피처 데이터 로드
        features_df = generate_features_from_csv()
        print(f"   ✅ 피처 데이터: {features_df.shape}")
        
        # 타겟 생성기 초기화
        target_gen = TargetGenerator()
        price_data = target_gen.load_price_data()
        
        # 인덱스 정렬
        common_index = features_df.index.intersection(price_data.index)
        features_aligned = features_df.loc[common_index]
        price_aligned = price_data.loc[common_index]
        
        # 트리플 배리어 타겟 생성
        triple_barrier_target = target_gen.generate_triple_barrier_target(price_aligned)
        target_aligned = triple_barrier_target.loc[common_index]
        
        # 결측값 제거
        valid_mask = target_aligned.notna() & features_aligned.notna().all(axis=1)
        features_clean = features_aligned[valid_mask]
        target_clean = target_aligned[valid_mask]
        
        print(f"   📊 전체 데이터: {len(target_clean)}개")
        
        # 🔥 핵심: 시간만료(0) 데이터 제거 - 오직 손절(-1)과 익절(1)만 남김
        trading_mask = (target_clean == -1) | (target_clean == 1)
        
        final_features = features_clean[trading_mask]
        final_target = target_clean[trading_mask]
        
        # 타겟을 이진 분류용으로 변환: -1(손절) → 0, 1(익절) → 1
        binary_target = (final_target == 1).astype(int)
        
        print(f"   🎯 거래 기회 데이터: {len(binary_target)}개")
        print(f"   📊 이진 분류 분포:")
        
        target_counts = binary_target.value_counts().sort_index()
        total_trades = len(binary_target)
        
        loss_count = target_counts.get(0, 0)  # 손절 (0)
        win_count = target_counts.get(1, 0)   # 익절 (1)
        
        print(f"      손절 (0): {loss_count:,}개 ({loss_count/total_trades*100:.1f}%)")
        print(f"      익절 (1): {win_count:,}개 ({win_count/total_trades*100:.1f}%)")
        
        # 승률 계산
        win_rate = win_count / total_trades
        print(f"   🏆 실제 승률: {win_rate*100:.1f}% (목표: 이보다 높은 예측 정확도)")
        
        # 불균형 비율
        imbalance_ratio = loss_count / win_count if win_count > 0 else float('inf')
        print(f"   ⚖️ 불균형 비율: 손절 대비 익절 1:{imbalance_ratio:.1f}")
        
        return final_features, binary_target
    
    def train_binary_model(self, optimize: bool = True, n_trials: int = 30) -> None:
        """
        이진 분류 모델 훈련 (미래 데이터 참조 오류 수정)

        Args:
            optimize: 하이퍼파라미터 최적화 여부
            n_trials: 최적화 시도 횟수
        """
        print(f"🚀 수정된 이진 분류 모델 훈련 시작...")
        print(f"🔧 수정 사항: 올바른 시계열 분할 (미래 데이터 참조 오류 해결)")

        # ✅ 수정: 시간 순서를 유지하며 데이터 준비
        features_df = generate_features_from_csv()
        target_gen = TargetGenerator()
        price_data = target_gen.load_price_data()

        # 인덱스 정렬
        common_index = features_df.index.intersection(price_data.index)
        features_aligned = features_df.loc[common_index]
        price_aligned = price_data.loc[common_index]

        # 트리플 배리어 타겟 생성
        triple_barrier_target = target_gen.generate_triple_barrier_target(price_aligned)
        target_aligned = triple_barrier_target.loc[common_index]

        # 결측값 제거
        valid_mask = target_aligned.notna() & features_aligned.notna().all(axis=1)
        features_clean = features_aligned[valid_mask]
        target_clean = target_aligned[valid_mask]

        print(f"   📊 전체 데이터: {len(target_clean)}개 ({features_clean.index.min()} ~ {features_clean.index.max()})")

        # ✅ 수정: 최신 24개월 훈련 전략
        # 최신 24개월로 훈련하여 최신 시장 패턴 학습
        train_start_date = '2023-06-01'
        train_end_date = '2025-06-01'

        # 훈련 데이터 (최신 24개월)
        train_mask = (features_clean.index >= train_start_date) & (features_clean.index < train_end_date)
        train_features = features_clean[train_mask]
        train_target = target_clean[train_mask]

        # 검증 데이터는 별도 테스트에서 사용 (과거 어려운 기간)
        # 여기서는 훈련 데이터의 20%를 임시 검증용으로 사용
        split_idx = int(len(train_features) * 0.8)
        test_features = train_features.iloc[split_idx:]
        test_target = train_target.iloc[split_idx:]
        train_features = train_features.iloc[:split_idx]
        train_target = train_target.iloc[:split_idx]

        print(f"   📅 훈련 기간: {train_features.index.min()} ~ {train_features.index.max()} (최신 24개월)")
        print(f"   📅 임시 검증: {test_features.index.min()} ~ {test_features.index.max()} (훈련 데이터 20%)")
        print(f"   🎯 실제 테스트: 과거 어려운 기간들로 별도 진행")

        # ✅ 수정: 각 세트에서 개별적으로 시간만료 제거
        # 훈련 데이터에서 시간만료 제거
        train_trading_mask = (train_target == -1) | (train_target == 1)
        X_train = train_features[train_trading_mask]
        y_train = (train_target[train_trading_mask] == 1).astype(int)

        # 검증 데이터에서 시간만료 제거
        test_trading_mask = (test_target == -1) | (test_target == 1)
        X_val = test_features[test_trading_mask]
        y_val = (test_target[test_trading_mask] == 1).astype(int)

        print(f"   📊 훈련 데이터: {X_train.shape} (거래 기회만)")
        print(f"   📊 검증 데이터: {X_val.shape} (거래 기회만)")

        # 훈련/검증 데이터 분포 확인
        train_win_rate = y_train.mean()
        val_win_rate = y_val.mean()
        print(f"   🏆 훈련 데이터 승률: {train_win_rate*100:.1f}%")
        print(f"   🏆 검증 데이터 승률: {val_win_rate*100:.1f}%")

        # 하이퍼파라미터 최적화 (수정된 데이터로)
        if optimize:
            params = self.optimize_hyperparameters_fixed(X_train, y_train, n_trials)
        else:
            params = self.default_params.copy()
        
        # LightGBM 데이터셋 생성
        train_data = lgb.Dataset(X_train, label=y_train)
        val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
        
        # 모델 훈련
        self.model = lgb.train(
            params,
            train_data,
            valid_sets=[val_data],
            num_boost_round=500,
            callbacks=[lgb.early_stopping(50), lgb.log_evaluation(50)]
        )
        
        # 피처 중요도 저장
        self.feature_importance = pd.DataFrame({
            'feature': X_train.columns,
            'importance': self.model.feature_importance()
        }).sort_values('importance', ascending=False)
        
        # 검증 성능 평가
        y_pred_proba = self.model.predict(X_val)
        y_pred_class = (y_pred_proba >= 0.5).astype(int)
        
        # 성능 지표 계산
        f1 = f1_score(y_val, y_pred_class)
        auc = roc_auc_score(y_val, y_pred_proba)
        accuracy = (y_pred_class == y_val).mean()
        
        print(f"   ✅ 검증 정확도: {accuracy:.4f}")
        print(f"   ✅ F1 스코어: {f1:.4f}")
        print(f"   ✅ AUC 스코어: {auc:.4f}")
        
        # 분류 보고서
        class_names = ['손절(0)', '익절(1)']
        print(f"\n   📊 분류 보고서:")
        print(classification_report(y_val, y_pred_class, target_names=class_names))
        
        # 혼동 행렬
        cm = confusion_matrix(y_val, y_pred_class)
        print(f"\n   🔍 혼동 행렬:")
        print(f"        예측→   손절   익절")
        for i, actual_class in enumerate(class_names):
            print(f"   {actual_class:>8}: {cm[i]}")
        
        # 예측 확률 분석
        print(f"\n   📈 예측 확률 분석:")
        print(f"      익절 확률 평균: {y_pred_proba.mean():.3f}")
        print(f"      익절 확률 표준편차: {y_pred_proba.std():.3f}")
        print(f"      익절 확률 범위: {y_pred_proba.min():.3f} ~ {y_pred_proba.max():.3f}")
        
        # 피처 중요도 상위 10개
        print(f"\n   🔝 상위 10개 중요 피처:")
        for i, row in self.feature_importance.head(10).iterrows():
            print(f"      {row['feature']}: {row['importance']}")
            
        print(f"\n🎉 수정된 이진 분류 모델 훈련 완료!")
        print(f"✅ 미래 데이터 참조 오류 해결: 과거 데이터로만 미래 예측")

    def optimize_hyperparameters_fixed(self, X: pd.DataFrame, y: pd.Series,
                                      n_trials: int = 30) -> Dict:
        """
        수정된 하이퍼파라미터 최적화 (시계열 순서 유지)

        Args:
            X: 피처 데이터 (이미 시계열 순서로 정렬됨)
            y: 타겟 데이터
            n_trials: 최적화 시도 횟수

        Returns:
            최적 하이퍼파라미터
        """
        print(f"🔧 수정된 하이퍼파라미터 최적화 시작... ({n_trials}회 시도)")
        print(f"✅ 시계열 순서 유지: 과거 데이터로만 미래 예측")

        def objective(trial):
            # 하이퍼파라미터 탐색 공간 정의
            params = {
                'objective': 'binary',
                'metric': 'binary_logloss',
                'boosting_type': 'gbdt',
                'num_leaves': trial.suggest_int('num_leaves', 10, 100),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                'feature_fraction': trial.suggest_float('feature_fraction', 0.4, 1.0),
                'bagging_fraction': trial.suggest_float('bagging_fraction', 0.4, 1.0),
                'bagging_freq': trial.suggest_int('bagging_freq', 1, 7),
                'min_child_samples': trial.suggest_int('min_child_samples', 5, 100),
                'is_unbalance': True,
                'verbose': -1,
                'random_state': 42
            }

            # ✅ 수정: 시계열 순서 유지하는 교차 검증
            # 단순히 80:20으로 분할 (이미 시계열 순서가 맞음)
            split_idx = int(len(X) * 0.8)
            X_train_opt = X.iloc[:split_idx]
            X_val_opt = X.iloc[split_idx:]
            y_train_opt = y.iloc[:split_idx]
            y_val_opt = y.iloc[split_idx:]

            # 모델 훈련
            train_data = lgb.Dataset(X_train_opt, label=y_train_opt)
            val_data = lgb.Dataset(X_val_opt, label=y_val_opt, reference=train_data)

            model = lgb.train(
                params,
                train_data,
                valid_sets=[val_data],
                num_boost_round=100,
                callbacks=[lgb.early_stopping(10), lgb.log_evaluation(0)]
            )

            # 예측 및 평가 (AUC 스코어 사용)
            y_pred_proba = model.predict(X_val_opt)
            score = roc_auc_score(y_val_opt, y_pred_proba)

            return score
        
        # Optuna 최적화 실행
        study = optuna.create_study(direction='maximize')
        study.optimize(objective, n_trials=n_trials, show_progress_bar=True)
        
        self.best_params = study.best_params.copy()
        self.best_params.update({
            'objective': 'binary',
            'metric': 'binary_logloss',
            'boosting_type': 'gbdt',
            'is_unbalance': True,
            'verbose': -1,
            'random_state': 42
        })
            
        print(f"   ✅ 최적화 완료! 최고 AUC 스코어: {study.best_value:.4f}")
        print(f"   🎯 최적 파라미터: {self.best_params}")
        
        return self.best_params
    
    def predict_win_probability(self, X: pd.DataFrame) -> np.ndarray:
        """
        익절 확률 예측
        
        Args:
            X: 피처 데이터
            
        Returns:
            익절 확률 배열
        """
        if self.model is None:
            raise ValueError("모델이 훈련되지 않았습니다. train_binary_model() 메서드를 먼저 호출하세요.")
            
        return self.model.predict(X)
    
    def save_model(self, filepath: str = 'models/binary_trading_model.pkl') -> None:
        """
        모델 저장
        
        Args:
            filepath: 저장할 파일 경로
        """
        model_data = {
            'model': self.model,
            'feature_importance': self.feature_importance,
            'best_params': self.best_params,
            'model_version': 'binary_v1.0_win_loss_only',
            'model_type': 'binary_classification',
            'target_mapping': {0: '손절', 1: '익절'},
            'description': '시간만료 제거, 순수 거래 승부 예측 모델'
        }
        
        joblib.dump(model_data, filepath)
        print(f"✅ 이진 분류 모델이 '{filepath}'에 저장되었습니다.")


if __name__ == "__main__":
    """
    이진 분류 모델 테스트
    """
    print("🎯 Project LEVIATHAN - 이진 분류 모델 (손절 vs 익절)")
    print("=" * 60)
    print("🔍 전략: 시간만료 데이터 제거 → 순수 거래 승부 예측")
    print("🎯 목표: '거래 기회가 왔을 때, 이길 것인가?' 정확도 향상")
    print("=" * 60)
    
    try:
        # 모델 초기화
        binary_model = BinaryTradingModel()
        
        # 이진 분류 모델 훈련 (빠른 테스트를 위해 최적화 비활성화)
        binary_model.train_binary_model(optimize=False)
        
        # 모델 저장
        binary_model.save_model()
        
        print(f"\n🎉 이진 분류 모델 테스트 완료!")
        print(f"📊 기대 효과: 거래 기회에서의 승부 예측 정확도 향상")
        
    except Exception as e:
        print(f"❌ 오류 발생: {e}")
        import traceback
        traceback.print_exc()
