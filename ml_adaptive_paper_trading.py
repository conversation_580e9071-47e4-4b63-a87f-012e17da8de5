#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 ML 적응형 페이퍼 트레이딩 시스템

백테스트와 슬라이딩 포워드에서 사용한 모든 ML 적응형 시스템을 실시간으로 적용
- 시장 상황 자동 분석 (상승장/하락장/횡보장/고위험/저변동성)
- ML 기반 동적 레버리지 (2-10x)
- ML 기반 동적 손절 (1-5%)
- ML 기반 동적 익절 (2-15%)
- 실시간 시장 적응형 전략 변경
"""

import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple
import sys
from pathlib import Path

# 프로젝트 루트 추가
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from trading.fixed_realtime_stream import FixedRealTimeDataStream
from trading.order_executor import OrderExecutor
from models.dual_model_predictor import DualModelPredictor
from models.ml_dynamic_exit_predictor import MLDynamicExitPredictor


class MLAdaptiveTradingSystem:
    """
    🧠 ML 적응형 페이퍼 트레이딩 시스템

    백테스트와 슬라이딩 포워드에서 검증된 모든 ML 시스템을 실시간 적용:
    1. 실시간 시장 분석 및 분류
    2. ML 기반 동적 파라미터 계산
    3. 시장별 적응형 전략 자동 변경
    4. ML 동적 익절 시스템
    """

    def __init__(self, api_key: str, secret_key: str, symbol: str = "BTCUSDT"):
        self.symbol = symbol

        # 🎯 자동 주문 실행기
        self.order_executor = OrderExecutor(api_key, secret_key, testnet=True)

        # 📡 실시간 데이터 스트림
        self.data_stream = FixedRealTimeDataStream(symbol, self._on_new_data)

        # 🧠 ML 모델 시스템들
        self.dual_predictor = DualModelPredictor()
        self.ml_exit_predictor = MLDynamicExitPredictor()
        self.models_loaded = False

        # 📊 특성 및 가격 데이터
        self.features_df = None
        self.price_data = None
        self.load_data()

        # 🧠 ML 적응형 설정 (슬라이딩 포워드와 동일)
        self.ml_adaptive_settings = {
            "max_leverage": 10.0,      # 최대 10배 레버리지
            "min_leverage": 2.0,       # 최소 2배 레버리지
            "default_leverage": 4.0,   # 기본 4배 레버리지
            "max_single_loss": 0.05,   # 단일 거래 최대 손실 5%
            "min_single_loss": 0.01,   # 단일 거래 최소 손실 1%
            "max_drawdown": 0.20,      # 최대 낙폭 20%
            "position_size_max": 0.15, # 자본의 최대 15%

            # 동적 익절 범위 (ML이 결정)
            "min_take_profit": 0.02,   # 최소 익절 2%
            "max_take_profit": 0.15,   # 최대 익절 15%
            "adaptive_take_profit": True,  # ML 기반 동적 익절 활성화

            # 시장 분류 기준
            "high_volatility_threshold": 0.8,    # 연변동성 80% 이상
            "low_volatility_threshold": 0.4,     # 연변동성 40% 이하
            "bear_market_threshold": -0.3,       # 24개월간 -30% 이상 하락
            "bull_market_threshold": 0.5         # 24개월간 +50% 이상 상승
        }

        # 시스템 상태
        self.is_running = False
        self.start_time = None
        self.signal_count = 0
        self.executed_orders = 0

        # 현재 시장 상황
        self.current_market_condition = None
        self.current_trading_strategy = None
        self.last_market_analysis = None

        # 성과 추적
        self.initial_balance = 0.0
        self.current_balance = 0.0
        self.total_pnl = 0.0
        self.daily_trade_count = {}
        self.consecutive_losses = 0

        print("🧠 ML 적응형 페이퍼 트레이딩 시스템 초기화 완료")

    def load_data(self):
        """특성 및 가격 데이터 로드"""
        print("📊 데이터 로드...")

        try:
            # 1. 특성 데이터 로드
            features_file = "data/short_dataset.csv"
            if Path(features_file).exists():
                df = pd.read_csv(features_file)

                # 타임스탬프 처리
                if 'timestamp' in df.columns:
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    df.set_index('timestamp', inplace=True)
                elif 'Unnamed: 0' in df.columns:
                    df['timestamp'] = pd.to_datetime(df['Unnamed: 0'])
                    df.set_index('timestamp', inplace=True)
                    df.drop('Unnamed: 0', axis=1, inplace=True, errors='ignore')

                # 숫자형 컬럼만 선택
                numeric_cols = df.select_dtypes(include=[np.number]).columns
                self.features_df = df[numeric_cols].copy()

                print(f"   ✅ 특성 데이터: {self.features_df.shape}")

            # 2. 가격 데이터 로드 (시장 분석용)
            price_files = ["data/BTCUSDT_15m.csv", "data/BTCUSDT_4h.csv"]
            for price_file in price_files:
                if Path(price_file).exists():
                    price_df = pd.read_csv(price_file)
                    if 'timestamp' in price_df.columns:
                        price_df['timestamp'] = pd.to_datetime(price_df['timestamp'])
                        price_df.set_index('timestamp', inplace=True)
                        self.price_data = price_df
                        print(f"   ✅ 가격 데이터: {self.price_data.shape}")
                        break

        except Exception as e:
            print(f"   ❌ 데이터 로드 오류: {e}")

    def initialize_models(self):
        """ML 모델들 초기화"""
        print("🧠 ML 모델들 초기화...")

        try:
            # 1. 듀얼 모델 로드
            if self.dual_predictor.load_models():
                print("   ✅ 듀얼 모델 로드 성공")
            else:
                print("   ❌ 듀얼 모델 로드 실패")
                return False

            # 2. ML 동적 익절 모델 로드
            if self.ml_exit_predictor.load_models():
                print("   ✅ ML 동적 익절 모델 로드 성공")
            else:
                print("   ⚠️ ML 동적 익절 모델 로드 실패 (기본 익절 사용)")

            self.models_loaded = True
            return True

        except Exception as e:
            print(f"   ❌ 모델 초기화 오류: {e}")
            return False

    def start_trading(self):
        """ML 적응형 페이퍼 트레이딩 시작"""
        print("\n🧠 ML 적응형 페이퍼 트레이딩 시작!")
        print("=" * 80)

        # 1. 계정 정보 확인
        account_info = self.order_executor.get_account_info()
        if not account_info:
            print("❌ 계정 정보 조회 실패")
            return False

        self.initial_balance = float(account_info['totalWalletBalance'])
        self.current_balance = self.initial_balance
        print(f"💰 초기 잔고: ${self.initial_balance:,.2f} USDT")

        # 2. ML 모델들 초기화
        if not self.initialize_models():
            print("❌ ML 모델 초기화 실패")
            return False

        # 3. 초기 시장 분석 및 전략 설정
        self._analyze_current_market()

        # 4. 실시간 데이터 스트림 시작
        self.data_stream.start_stream()

        # 5. 시스템 상태 설정
        self.is_running = True
        self.start_time = datetime.now()

        print(f"✅ ML 적응형 시스템 시작됨")
        self._print_current_strategy()

        return True

    def stop_trading(self):
        """ML 적응형 페이퍼 트레이딩 중지"""
        print("\n⏹️ ML 적응형 페이퍼 트레이딩 중지...")

        self.is_running = False
        self.data_stream.stop_stream()
        self._generate_final_report()

        print("✅ ML 적응형 시스템 중지됨")

    def _analyze_current_market(self):
        """현재 시장 상황 분석 (슬라이딩 포워드와 동일한 로직)"""
        print("\n🔍 현재 시장 상황 분석...")

        if self.price_data is None or len(self.price_data) < 100:
            print("   ⚠️ 가격 데이터 부족 - 기본 전략 사용")
            self.current_market_condition = {'type': 'normal_market', 'description': '일반 시장 (데이터 부족)'}
            self.current_trading_strategy = self._get_default_strategy()
            return

        try:
            # 최근 24개월 데이터 사용 (슬라이딩 포워드와 동일)
            recent_data = self.price_data.tail(24 * 30 * 24)  # 대략 24개월 (시간봉 기준)

            if len(recent_data) < 100:
                recent_data = self.price_data.tail(1000)  # 최소 1000개 데이터

            # 1. 변동성 분석 (연환산)
            daily_returns = recent_data['close'].pct_change().dropna()
            volatility = daily_returns.std() * np.sqrt(365)

            # 2. 전체 추세 분석 (총 수익률)
            total_return = (recent_data['close'].iloc[-1] / recent_data['close'].iloc[0]) - 1

            # 3. 최대 낙폭 분석
            rolling_max = recent_data['close'].expanding().max()
            drawdowns = (recent_data['close'] - rolling_max) / rolling_max
            max_drawdown = drawdowns.min()

            # 4. 시장 분류 (슬라이딩 포워드와 동일한 로직)
            self.current_market_condition = self._classify_market_objectively(volatility, total_return, max_drawdown)

            # 5. ML 기반 동적 전략 결정
            self.current_trading_strategy = self._get_ml_adaptive_settings_for_market(self.current_market_condition)

            # 분석 결과 저장
            self.last_market_analysis = {
                'timestamp': datetime.now(),
                'volatility': volatility,
                'total_return': total_return,
                'max_drawdown': max_drawdown,
                'market_condition': self.current_market_condition,
                'trading_strategy': self.current_trading_strategy
            }

            print(f"   📊 변동성: {volatility*100:.1f}% (연환산)")
            print(f"   📈 총 수익률: {total_return*100:+.1f}%")
            print(f"   📉 최대 낙폭: {max_drawdown*100:.1f}%")
            print(f"   🎯 시장 분류: {self.current_market_condition['description']}")

        except Exception as e:
            print(f"   ❌ 시장 분석 오류: {e}")
            self.current_market_condition = {'type': 'normal_market', 'description': '일반 시장 (분석 오류)'}
            self.current_trading_strategy = self._get_default_strategy()

    def _classify_market_objectively(self, volatility: float, total_return: float, max_drawdown: float):
        """객관적 지표 기반 시장 분류 (슬라이딩 포워드와 동일)"""

        settings = self.ml_adaptive_settings

        # 고위험 시장 (높은 변동성 + 큰 낙폭)
        if volatility > settings['high_volatility_threshold'] and max_drawdown < -0.4:
            return {
                'type': 'high_risk',
                'description': '고위험 시장 (높은 변동성 + 큰 낙폭)',
                'volatility': volatility,
                'total_return': total_return,
                'max_drawdown': max_drawdown
            }

        # 하락 시장 (큰 손실)
        elif total_return < settings['bear_market_threshold']:
            return {
                'type': 'bear_market',
                'description': f'하락 시장 ({total_return*100:.1f}% 하락)',
                'volatility': volatility,
                'total_return': total_return,
                'max_drawdown': max_drawdown
            }

        # 상승 시장 (큰 수익)
        elif total_return > settings['bull_market_threshold']:
            return {
                'type': 'bull_market',
                'description': f'상승 시장 ({total_return*100:.1f}% 상승)',
                'volatility': volatility,
                'total_return': total_return,
                'max_drawdown': max_drawdown
            }

        # 저변동성 시장
        elif volatility < settings['low_volatility_threshold']:
            return {
                'type': 'low_volatility',
                'description': f'저변동성 시장 ({volatility*100:.1f}% 변동성)',
                'volatility': volatility,
                'total_return': total_return,
                'max_drawdown': max_drawdown
            }

        # 일반 시장
        else:
            return {
                'type': 'normal_market',
                'description': '일반 시장 (중간 변동성 + 중간 수익률)',
                'volatility': volatility,
                'total_return': total_return,
                'max_drawdown': max_drawdown
            }

    def _get_ml_adaptive_settings_for_market(self, market_condition):
        """🧠 ML 기반 동적 시장별 설정 (슬라이딩 포워드와 동일)"""

        settings = self.ml_adaptive_settings
        market_type = market_condition['type']
        volatility = market_condition.get('volatility', 0.6)
        total_return = market_condition.get('total_return', 0.0)

        # ML 기반 동적 레버리지 계산
        base_leverage = self._calculate_ml_leverage(market_type, volatility, total_return)

        # ML 기반 동적 손절 계산
        dynamic_stop_loss = self._calculate_ml_stop_loss(market_type, volatility)

        # ML 기반 동적 익절 기본값
        base_take_profit = self._calculate_base_take_profit(market_type, volatility)

        if market_type == 'high_risk':
            # 고위험: 매우 보수적 접근 (30% 포지션)
            return {
                'leverage': max(settings['min_leverage'], base_leverage * 0.7),  # 2-7x
                'stop_loss_pct': min(settings['max_single_loss'], dynamic_stop_loss * 1.2),  # 최대 5%
                'take_profit_pct': base_take_profit,
                'ml_adaptive_tp': True,  # 동적 익절 활성화
                'confidence_threshold': 0.6,  # 높은 신뢰도 요구
                'position_size': 0.3,  # 30% 포지션 (매우 보수적)
                'strategy_name': '고위험 보수적 전략'
            }

        elif market_type == 'bear_market':
            # 하락장: 보수적 접근 (50% 포지션)
            return {
                'leverage': min(settings['max_leverage'] * 0.6, base_leverage),  # 2-6x
                'stop_loss_pct': dynamic_stop_loss,  # 1-4%
                'take_profit_pct': base_take_profit,
                'ml_adaptive_tp': True,
                'confidence_threshold': 0.5,
                'position_size': 0.5,  # 50% 포지션 (보수적)
                'strategy_name': '하락장 적응 전략'
            }

        elif market_type == 'bull_market':
            # 상승장: 최대 공격적 접근 (100% 포지션)
            return {
                'leverage': min(settings['max_leverage'], base_leverage * 1.2),  # 최대 10x
                'stop_loss_pct': max(settings['min_single_loss'], dynamic_stop_loss * 0.8),  # 최소 1%
                'take_profit_pct': base_take_profit,
                'ml_adaptive_tp': True,
                'confidence_threshold': 0.4,  # 낮은 신뢰도도 허용
                'position_size': 1.0,  # 100% 포지션 (최대 공격적)
                'strategy_name': '상승장 공격적 전략'
            }

        elif market_type == 'low_volatility':
            # 저변동성: 최대 공격적 접근 (100% 포지션)
            return {
                'leverage': settings['max_leverage'],  # 10x
                'stop_loss_pct': dynamic_stop_loss * 1.5,  # 넓은 손절
                'take_profit_pct': base_take_profit,
                'ml_adaptive_tp': True,
                'confidence_threshold': 0.3,  # 매우 낮은 신뢰도도 허용
                'position_size': 1.0,  # 100% 포지션 (최대 공격적)
                'strategy_name': '저변동성 최대공격 전략'
            }

        else:  # normal_market
            # 일반 시장: 균형적 접근 (70% 포지션)
            return {
                'leverage': settings['default_leverage'],  # 4x
                'stop_loss_pct': (settings['min_single_loss'] + settings['max_single_loss']) / 2,  # 3%
                'take_profit_pct': base_take_profit,
                'ml_adaptive_tp': True,
                'confidence_threshold': 0.5,
                'position_size': 0.7,  # 70% 포지션 (균형적)
                'strategy_name': '일반시장 균형 전략'
            }

    def _calculate_ml_leverage(self, market_type: str, volatility: float, total_return: float) -> float:
        """ML 기반 동적 레버리지 계산"""

        settings = self.ml_adaptive_settings
        base_leverage = settings['default_leverage']

        # 변동성 기반 조정
        if volatility < 0.3:  # 저변동성
            volatility_multiplier = 1.5
        elif volatility > 0.8:  # 고변동성
            volatility_multiplier = 0.6
        else:  # 중간 변동성
            volatility_multiplier = 1.0

        # 수익률 기반 조정
        if total_return > 0.3:  # 강한 상승
            return_multiplier = 1.2
        elif total_return < -0.2:  # 강한 하락
            return_multiplier = 0.8
        else:  # 중간
            return_multiplier = 1.0

        # 최종 레버리지 계산
        calculated_leverage = base_leverage * volatility_multiplier * return_multiplier

        # 범위 제한
        return max(settings['min_leverage'], min(settings['max_leverage'], calculated_leverage))

    def _calculate_ml_stop_loss(self, market_type: str, volatility: float) -> float:
        """ML 기반 동적 손절 계산"""

        settings = self.ml_adaptive_settings

        # 변동성 기반 손절 계산
        base_stop_loss = 0.02  # 기본 2%

        if volatility < 0.3:  # 저변동성
            dynamic_stop_loss = base_stop_loss * 0.7  # 좁은 손절
        elif volatility > 0.8:  # 고변동성
            dynamic_stop_loss = base_stop_loss * 1.8  # 넓은 손절
        else:  # 중간 변동성
            dynamic_stop_loss = base_stop_loss

        # 범위 제한
        return max(settings['min_single_loss'], min(settings['max_single_loss'], dynamic_stop_loss))

    def _calculate_base_take_profit(self, market_type: str, volatility: float) -> float:
        """ML 기반 동적 익절 기본값 계산"""

        settings = self.ml_adaptive_settings

        # 시장 타입별 기본 익절
        if market_type == 'bull_market':
            base_tp = 0.08  # 8%
        elif market_type == 'bear_market':
            base_tp = 0.04  # 4%
        elif market_type == 'low_volatility':
            base_tp = 0.06  # 6%
        elif market_type == 'high_risk':
            base_tp = 0.03  # 3%
        else:  # normal_market
            base_tp = 0.05  # 5%

        # 변동성 기반 조정
        if volatility > 0.8:
            base_tp *= 1.5  # 고변동성에서는 더 큰 익절
        elif volatility < 0.3:
            base_tp *= 0.8  # 저변동성에서는 작은 익절

        # 범위 제한
        return max(settings['min_take_profit'], min(settings['max_take_profit'], base_tp))

    def _get_default_strategy(self):
        """기본 전략 반환"""
        return {
            'leverage': 4.0,
            'stop_loss_pct': 0.02,
            'take_profit_pct': 0.05,
            'ml_adaptive_tp': False,
            'confidence_threshold': 0.5,
            'position_size': 0.7,  # 70% 포지션 (기본값)
            'strategy_name': '기본 전략'
        }

    def _print_current_strategy(self):
        """현재 전략 정보 출력"""
        if self.current_trading_strategy and self.current_market_condition:
            print(f"\n🧠 현재 ML 적응형 전략:")
            print(f"   📊 시장 상황: {self.current_market_condition['description']}")
            print(f"   🎯 전략명: {self.current_trading_strategy['strategy_name']}")
            print(f"   ⚙️ 레버리지: {self.current_trading_strategy['leverage']:.1f}x")
            print(f"   📉 손절: {self.current_trading_strategy['stop_loss_pct']:.1%}")
            print(f"   📈 익절: {self.current_trading_strategy['take_profit_pct']:.1%}")
            print(f"   🎲 신뢰도 임계값: {self.current_trading_strategy['confidence_threshold']:.1%}")
            print(f"   📍 포지션 크기: {self.current_trading_strategy['position_size']:.1%}")
            print(f"   🧠 ML 동적 익절: {'✅ 활성화' if self.current_trading_strategy['ml_adaptive_tp'] else '❌ 비활성화'}")

    def _on_new_data(self, interval: str, candle_data: Dict):
        """새로운 데이터 수신 시 호출 (ML 적응형 로직)"""
        if not self.is_running or not self.models_loaded:
            return

        # 15분봉에서만 신호 생성
        if interval == "15m":
            try:
                # 주기적으로 시장 재분석 (1시간마다)
                if self.last_market_analysis is None or \
                   (datetime.now() - self.last_market_analysis['timestamp']).total_seconds() > 3600:
                    self._analyze_current_market()
                    self._print_current_strategy()

                # ML 기반 신호 생성
                signal = self._generate_ml_adaptive_signal(candle_data)

                if signal:
                    self.signal_count += 1
                    self._print_signal(signal)

                    # ML 적응형 거래 제약 조건 체크
                    if self._check_ml_trading_constraints(signal):
                        user_input = input(f"❓ ML 적응형 신호를 실행하시겠습니까? (y/N): ").strip().lower()

                        if user_input == 'y':
                            success = self.order_executor.execute_signal(signal)

                            if success:
                                self.executed_orders += 1
                                print(f"✅ 주문 실행 성공 (총 {self.executed_orders}개)")

                                # 일일 거래 횟수 업데이트
                                trade_date = datetime.now().date()
                                self.daily_trade_count[trade_date] = self.daily_trade_count.get(trade_date, 0) + 1
                                self.consecutive_losses = 0  # 성공 시 연속 손실 리셋
                            else:
                                print(f"❌ 주문 실행 실패")
                                self.consecutive_losses += 1
                        else:
                            print(f"⏭️ 신호 실행 건너뛰기")

            except Exception as e:
                print(f"❌ ML 적응형 신호 처리 오류: {e}")

    def _generate_ml_adaptive_signal(self, candle_data: Dict) -> Optional[Dict]:
        """ML 적응형 신호 생성"""

        if self.features_df is None or len(self.features_df) < 50:
            return None

        if self.current_trading_strategy is None:
            return None

        try:
            # 최근 특성 데이터 사용
            recent_features = self.features_df.tail(1)

            # ML 모델 예측
            predictions = self.dual_predictor.predict_dual_signals(recent_features)

            if predictions is not None and len(predictions) > 0:
                pred = predictions.iloc[-1]

                # 현재 전략의 임계값 사용
                strategy = self.current_trading_strategy
                long_prob = pred['long_prob']
                short_prob = pred['short_prob']
                final_signal = pred['final_signal']
                confidence = pred['confidence']

                # 신뢰도 필터링 (현재 전략 기준)
                if confidence < strategy['confidence_threshold']:
                    return None

                # 신호 타입 결정
                signal_type = None
                if final_signal == 1 and long_prob > 0.402:  # 기존 롱 임계값 유지
                    signal_type = "BUY"
                elif final_signal == -1 and short_prob > 0.165:  # 기존 숏 임계값 유지
                    signal_type = "SELL"
                else:
                    return None

                # ML 동적 익절 예측 (활성화된 경우)
                ml_exit_info = None
                if strategy['ml_adaptive_tp']:
                    try:
                        current_features = recent_features.iloc[-1]
                        ml_exit_info = self.ml_exit_predictor.predict_optimal_exit(
                            features=current_features,
                            current_price=candle_data['close'],
                            position_type=signal_type,
                            confidence=confidence
                        )
                    except Exception as e:
                        print(f"   ⚠️ ML 익절 예측 실패: {e}")
                        ml_exit_info = None

                # 최종 익절 범위 결정
                if ml_exit_info and strategy['ml_adaptive_tp']:
                    final_take_profit = ml_exit_info['optimal_exit_range']
                    print(f"   🧠 ML 익절 예측: {final_take_profit*100:.1f}% (확률: {ml_exit_info['exit_probability']*100:.0f}%)")
                else:
                    final_take_profit = strategy['take_profit_pct']

                signal = {
                    'timestamp': datetime.now(),
                    'symbol': self.symbol,
                    'signal_type': signal_type,
                    'confidence': confidence,
                    'leverage': strategy['leverage'],
                    'position_size': strategy['position_size'],
                    'stop_loss_pct': strategy['stop_loss_pct'],
                    'take_profit_pct': final_take_profit,
                    'long_prob': long_prob,
                    'short_prob': short_prob,
                    'current_price': candle_data['close'],

                    # ML 적응형 정보
                    'market_condition': self.current_market_condition['type'],
                    'strategy_name': strategy['strategy_name'],
                    'ml_adaptive_tp': strategy['ml_adaptive_tp'],
                    'ml_exit_info': ml_exit_info
                }

                return signal

            return None

        except Exception as e:
            print(f"❌ ML 적응형 신호 생성 오류: {e}")
            return None

    def _check_ml_trading_constraints(self, signal: Dict) -> bool:
        """ML 적응형 거래 제약 조건 체크"""

        strategy = self.current_trading_strategy

        # 1. 신뢰도 체크 (현재 전략 기준)
        if signal['confidence'] < strategy['confidence_threshold']:
            print(f"   ❌ 확신도 부족: {signal['confidence']:.2f} < {strategy['confidence_threshold']:.2f}")
            return False

        # 2. 일일 거래 횟수 제한
        trade_date = datetime.now().date()
        daily_count = self.daily_trade_count.get(trade_date, 0)
        daily_limit = 10  # 기본 일일 한도

        if daily_count >= daily_limit:
            print(f"   ❌ 일일 거래 한도 초과: {daily_count}/{daily_limit}")
            return False

        # 3. 연속 손실 제한
        if self.consecutive_losses >= 3:
            print(f"   ❌ 연속 손실 한도 초과: {self.consecutive_losses}회")
            return False

        # 4. 시스템 상태 확인
        if self.order_executor.is_emergency_stop:
            print(f"   ❌ 긴급 정지 상태")
            return False

        # 5. 최대 포지션 수 확인
        max_positions = 2
        if len(self.order_executor.active_positions) >= max_positions:
            print(f"   ❌ 최대 포지션 수 초과: {len(self.order_executor.active_positions)}/{max_positions}")
            return False

        return True

    def _print_signal(self, signal: Dict):
        """ML 적응형 신호 정보 출력"""
        timestamp = signal['timestamp'].strftime('%H:%M:%S')
        signal_type = signal['signal_type']
        confidence = signal['confidence']
        price = signal['current_price']

        print(f"\n🧠 [{timestamp}] ML 적응형 신호: {signal_type}")
        print(f"   🎯 전략: {signal['strategy_name']}")
        print(f"   📊 시장: {signal['market_condition']}")
        print(f"   💰 현재가: ${price:,.2f}")
        print(f"   📊 신뢰도: {confidence:.3f} (임계값: {self.current_trading_strategy['confidence_threshold']:.3f})")
        print(f"   📈 롱 확률: {signal['long_prob']:.3f}")
        print(f"   📉 숏 확률: {signal['short_prob']:.3f}")
        print(f"   ⚙️ 레버리지: {signal['leverage']:.1f}x")
        print(f"   📍 포지션: {signal['position_size']:.1%}")
        print(f"   📉 손절: {signal['stop_loss_pct']:.1%}")
        print(f"   📈 익절: {signal['take_profit_pct']:.1%}")
        print(f"   🧠 ML 동적 익절: {'✅' if signal['ml_adaptive_tp'] else '❌'}")

    def print_status(self):
        """ML 적응형 시스템 상태 출력"""
        if not self.is_running:
            print("⏹️ 시스템 중지 상태")
            return

        runtime = datetime.now() - self.start_time
        minutes = int(runtime.total_seconds() // 60)

        print(f"\n🧠 ML 적응형 시스템 상태:")
        print(f"   ⏰ 운영 시간: {minutes}분")
        print(f"   🎯 생성된 신호: {self.signal_count}개")
        print(f"   📝 실행된 주문: {self.executed_orders}개")
        print(f"   📍 활성 포지션: {len(self.order_executor.active_positions)}개")
        print(f"   🔄 연속 손실: {self.consecutive_losses}회")

        # 현재 전략 정보
        if self.current_trading_strategy:
            print(f"   🎯 현재 전략: {self.current_trading_strategy['strategy_name']}")
            print(f"   📊 시장 상황: {self.current_market_condition['description'] if self.current_market_condition else 'N/A'}")

        # 일일 거래 횟수
        today = datetime.now().date()
        today_trades = self.daily_trade_count.get(today, 0)
        print(f"   📅 오늘 거래: {today_trades}/10회")

        # 데이터 스트림 상태
        stream_status = self.data_stream.get_status()
        print(f"   📡 데이터 스트림: {'✅ 정상' if stream_status['is_running'] else '❌ 중단'}")

        # 마지막 시장 분석 시간
        if self.last_market_analysis:
            last_analysis = self.last_market_analysis['timestamp'].strftime('%H:%M:%S')
            print(f"   🔍 마지막 시장 분석: {last_analysis}")

    def _generate_final_report(self):
        """최종 ML 적응형 리포트 생성"""
        runtime = datetime.now() - self.start_time if self.start_time else timedelta(0)

        print(f"\n📋 ML 적응형 최종 리포트")
        print("=" * 70)
        print(f"🕐 운영 시간: {runtime}")
        print(f"🎯 생성된 신호: {self.signal_count}개")
        print(f"📝 실행된 주문: {self.executed_orders}개")
        print(f"⚡ 신호 실행률: {self.executed_orders/max(self.signal_count, 1)*100:.1f}%")
        print(f"🔄 연속 손실: {self.consecutive_losses}회")

        if self.current_trading_strategy and self.current_market_condition:
            print(f"\n🧠 최종 ML 적응형 설정:")
            print(f"   📊 시장 분류: {self.current_market_condition['description']}")
            print(f"   🎯 사용된 전략: {self.current_trading_strategy['strategy_name']}")
            print(f"   ⚙️ 레버리지: {self.current_trading_strategy['leverage']:.1f}x")
            print(f"   📉 손절: {self.current_trading_strategy['stop_loss_pct']:.1%}")
            print(f"   📈 익절: {self.current_trading_strategy['take_profit_pct']:.1%}")
            print(f"   🧠 ML 동적 익절: {'✅ 활성화' if self.current_trading_strategy['ml_adaptive_tp'] else '❌ 비활성화'}")

        if self.last_market_analysis:
            analysis = self.last_market_analysis
            print(f"\n📊 최종 시장 분석:")
            print(f"   변동성: {analysis['volatility']*100:.1f}% (연환산)")
            print(f"   총 수익률: {analysis['total_return']*100:+.1f}%")
            print(f"   최대 낙폭: {analysis['max_drawdown']*100:.1f}%")


def main():
    print("🧠 ML 적응형 페이퍼 트레이딩 시스템")
    print("=" * 90)
    print("🎯 목표: 백테스트와 슬라이딩 포워드에서 검증된 모든 ML 시스템을 실시간 적용")
    print("🔥 특징: 시장 상황 자동 분석 → 전략 자동 변경 → ML 동적 파라미터")
    print()

    # API 키 설정
    API_KEY = "30f3132331bef6575c30cd4ec053b90d6e4c4e005cc827066e424c992ff86ccc"
    SECRET_KEY = "ed604353c26ccc02d6180bf32f5f402eeaa0ebc6963ac02ead2c32c654dbba46"

    # ML 적응형 시스템 초기화
    trading_system = MLAdaptiveTradingSystem(API_KEY, SECRET_KEY, "BTCUSDT")

    try:
        # 시스템 시작
        if trading_system.start_trading():
            print(f"\n⏰ 5분간 ML 적응형 시스템 테스트...")
            print(f"🧠 실시간 시장 분석 → 전략 자동 변경 → ML 동적 파라미터")
            print(f"🔄 Ctrl+C로 중단 가능")

            # 5분간 실행하면서 1분마다 상태 출력
            for i in range(5):
                time.sleep(60)
                print(f"\n⏰ {(i+1)}분 경과...")
                trading_system.print_status()

            print(f"\n✅ 5분 테스트 완료!")

    except KeyboardInterrupt:
        print(f"\n⏹️ 사용자가 테스트를 중단했습니다")

    except Exception as e:
        print(f"\n❌ 시스템 오류: {e}")
        import traceback
        traceback.print_exc()

    finally:
        trading_system.stop_trading()
        print(f"\n🎉 ML 적응형 페이퍼 트레이딩 시스템 테스트 완료!")
        print(f"✅ 백테스트와 슬라이딩 포워드에서 검증된 모든 ML 시스템이 실시간 적용됨!")


if __name__ == "__main__":
    main()