"""
Project LEVIATHAN - ML 기반 시장 분석 엔진
시장 상황을 실시간으로 분석하고 적응형 거래 전략을 제공하는 핵심 모듈
"""

import pandas as pd
import numpy as np
from typing import Dict, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')


class MarketAnalyzer:
    """
    ML 기반 시장 분석 엔진
    
    기능:
    1. 시장 변동성 분석
    2. 추세 강도 측정
    3. 거래량 패턴 분석
    4. 5가지 시장 상황 분류
    5. 적응형 거래 설정 제공
    """
    
    def __init__(self):
        """시장 분석 엔진 초기화"""
        self.market_conditions = {
            'HIGH_RISK': '고위험',
            'BEAR_MARKET': '하락장', 
            'SIDEWAYS': '횡보장',
            'LOW_VOLATILITY': '저변동성',
            'NORMAL': '일반시장'
        }
        
        # 시장 분류 임계값
        self.volatility_thresholds = {
            'low': 0.015,      # 1.5% 미만: 저변동성
            'normal': 0.03,    # 3% 미만: 일반
            'high': 0.05       # 5% 이상: 고변동성
        }
        
        self.trend_thresholds = {
            'strong_bear': -0.02,  # -2% 미만: 강한 하락
            'bear': -0.005,        # -0.5% 미만: 하락
            'sideways': 0.005,     # ±0.5%: 횡보
            'bull': 0.02           # 2% 이상: 강한 상승
        }
        
        print("🧠 ML 기반 시장 분석 엔진 초기화 완료")
    
    def calculate_market_volatility(self, data: pd.DataFrame, window: int = 96) -> pd.Series:
        """
        시장 변동성 계산 (96 = 24시간, 15분봉 기준)
        
        Args:
            data: OHLCV 데이터
            window: 변동성 계산 윈도우
            
        Returns:
            변동성 시리즈
        """
        # 수익률 계산
        returns = data['close'].pct_change()
        
        # 롤링 변동성 (표준편차)
        volatility = returns.rolling(window=window).std() * np.sqrt(96)  # 일일 변동성으로 변환
        
        return volatility.fillna(0)
    
    def calculate_trend_strength(self, data: pd.DataFrame, window: int = 96) -> pd.Series:
        """
        추세 강도 계산
        
        Args:
            data: OHLCV 데이터
            window: 추세 계산 윈도우
            
        Returns:
            추세 강도 시리즈 (-1: 강한 하락, +1: 강한 상승)
        """
        # 가격 변화율
        price_change = (data['close'] - data['close'].shift(window)) / data['close'].shift(window)
        
        # MACD 기반 추세 확인
        if 'MACD_trend' in data.columns:
            macd_trend = data['MACD_trend'].fillna(0)
            # MACD와 가격 변화율 결합
            trend_strength = (price_change + macd_trend.rolling(window=24).mean()) / 2
        else:
            trend_strength = price_change
        
        return trend_strength.fillna(0)
    
    def calculate_volume_pattern(self, data: pd.DataFrame, window: int = 96) -> pd.Series:
        """
        거래량 패턴 분석
        
        Args:
            data: OHLCV 데이터
            window: 거래량 분석 윈도우
            
        Returns:
            거래량 패턴 점수 (0-1)
        """
        if 'volume' not in data.columns:
            return pd.Series(0.5, index=data.index)  # 기본값
        
        # 거래량 이동평균 대비 비율
        volume_ma = data['volume'].rolling(window=window).mean()
        volume_ratio = data['volume'] / volume_ma
        
        # 0-1 범위로 정규화
        volume_score = np.clip(volume_ratio / 3, 0, 1)  # 3배 이상은 1로 제한
        
        return volume_score.fillna(0.5)
    
    def classify_market_condition(self, data: pd.DataFrame) -> pd.Series:
        """
        시장 상황 분류
        
        Args:
            data: 피처가 포함된 데이터프레임
            
        Returns:
            시장 상황 분류 시리즈
        """
        # 시장 지표 계산
        volatility = self.calculate_market_volatility(data)
        trend_strength = self.calculate_trend_strength(data)
        volume_pattern = self.calculate_volume_pattern(data)
        
        # 시장 상황 분류
        market_condition = pd.Series('NORMAL', index=data.index)
        
        for i in data.index:
            vol = volatility.loc[i] if i in volatility.index else 0
            trend = trend_strength.loc[i] if i in trend_strength.index else 0
            volume = volume_pattern.loc[i] if i in volume_pattern.index else 0.5
            
            # 분류 로직
            if vol > self.volatility_thresholds['high']:
                market_condition.loc[i] = 'HIGH_RISK'
            elif trend < self.trend_thresholds['strong_bear']:
                market_condition.loc[i] = 'BEAR_MARKET'
            elif vol < self.volatility_thresholds['low']:
                market_condition.loc[i] = 'LOW_VOLATILITY'
            elif abs(trend) < self.trend_thresholds['sideways']:
                market_condition.loc[i] = 'SIDEWAYS'
            else:
                market_condition.loc[i] = 'NORMAL'
        
        return market_condition
    
    def get_adaptive_settings(self, market_condition: str) -> Dict[str, float]:
        """
        시장 상황별 적응형 거래 설정
        
        Args:
            market_condition: 시장 상황
            
        Returns:
            적응형 거래 설정 딕셔너리
        """
        settings_map = {
            'HIGH_RISK': {
                'leverage_multiplier': 0.5,    # 2배 (4 * 0.5)
                'position_size_pct': 0.3,      # 30%
                'stop_loss_pct': 0.05,         # 5%
                'take_profit_min': 0.08,       # 8%
                'take_profit_max': 0.12,       # 12%
                'confidence_threshold': 0.7    # 70%
            },
            'BEAR_MARKET': {
                'leverage_multiplier': 0.75,   # 3배 (4 * 0.75)
                'position_size_pct': 0.5,      # 50%
                'stop_loss_pct': 0.04,         # 4%
                'take_profit_min': 0.06,       # 6%
                'take_profit_max': 0.10,       # 10%
                'confidence_threshold': 0.65   # 65%
            },
            'SIDEWAYS': {
                'leverage_multiplier': 1.0,    # 4배 (4 * 1.0)
                'position_size_pct': 0.7,      # 70%
                'stop_loss_pct': 0.03,         # 3%
                'take_profit_min': 0.05,       # 5%
                'take_profit_max': 0.08,       # 8%
                'confidence_threshold': 0.6    # 60%
            },
            'LOW_VOLATILITY': {
                'leverage_multiplier': 2.5,    # 10배 (4 * 2.5)
                'position_size_pct': 1.0,      # 100%
                'stop_loss_pct': 0.02,         # 2%
                'take_profit_min': 0.05,       # 5%
                'take_profit_max': 0.15,       # 15%
                'confidence_threshold': 0.55   # 55%
            },
            'NORMAL': {
                'leverage_multiplier': 1.25,   # 5배 (4 * 1.25)
                'position_size_pct': 0.7,      # 70%
                'stop_loss_pct': 0.035,        # 3.5%
                'take_profit_min': 0.05,       # 5%
                'take_profit_max': 0.10,       # 10%
                'confidence_threshold': 0.6    # 60%
            }
        }
        
        return settings_map.get(market_condition, settings_map['NORMAL'])
    
    def analyze_market(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        종합 시장 분석
        
        Args:
            data: 피처가 포함된 데이터프레임
            
        Returns:
            시장 분석 결과 데이터프레임
        """
        print("🔍 시장 분석 시작...")
        
        # 시장 지표 계산
        volatility = self.calculate_market_volatility(data)
        trend_strength = self.calculate_trend_strength(data)
        volume_pattern = self.calculate_volume_pattern(data)
        market_condition = self.classify_market_condition(data)
        
        # 결과 데이터프레임 생성
        analysis_result = pd.DataFrame({
            'volatility': volatility,
            'trend_strength': trend_strength,
            'volume_pattern': volume_pattern,
            'market_condition': market_condition
        }, index=data.index)
        
        # 각 시점별 적응형 설정 추가
        adaptive_settings = []
        for condition in market_condition:
            settings = self.get_adaptive_settings(condition)
            adaptive_settings.append(settings)
        
        # 설정을 별도 컬럼으로 추가
        for key in ['leverage_multiplier', 'position_size_pct', 'stop_loss_pct', 
                   'take_profit_min', 'take_profit_max', 'confidence_threshold']:
            analysis_result[key] = [settings[key] for settings in adaptive_settings]
        
        print(f"✅ 시장 분석 완료: {len(analysis_result)}개 데이터 포인트")
        
        # 시장 상황 분포 출력
        condition_counts = market_condition.value_counts()
        print("📊 시장 상황 분포:")
        for condition, count in condition_counts.items():
            pct = count / len(market_condition) * 100
            print(f"   {self.market_conditions[condition]}: {count}개 ({pct:.1f}%)")
        
        return analysis_result


if __name__ == "__main__":
    # 테스트 코드
    print("🧪 시장 분석 엔진 테스트")
    
    # 샘플 데이터 생성
    dates = pd.date_range('2024-01-01', periods=1000, freq='15min')
    sample_data = pd.DataFrame({
        'close': np.random.randn(1000).cumsum() + 50000,
        'high': np.random.randn(1000).cumsum() + 50100,
        'low': np.random.randn(1000).cumsum() + 49900,
        'volume': np.random.randint(1000, 10000, 1000)
    }, index=dates)
    
    # 시장 분석기 테스트
    analyzer = MarketAnalyzer()
    result = analyzer.analyze_market(sample_data)
    
    print(f"\n📈 분석 결과 샘플:")
    print(result.head())
