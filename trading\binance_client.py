#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔗 Project LEVIATHAN - Binance Testnet 클라이언트

Binance Testnet API와의 연동을 담당하는 클라이언트 클래스
실시간 데이터 수신, 주문 실행, 계정 관리 등을 처리

Author: 강현모
Date: 2024-12-15
Version: 1.0
"""

import requests
import hmac
import hashlib
import time
import json
from typing import Dict, List, Optional, Any
from datetime import datetime
import pandas as pd
import sys
from pathlib import Path

# 프로젝트 루트 추가
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.api_config import api_config


class BinanceTestnetClient:
    """Binance Testnet API 클라이언트"""
    
    def __init__(self):
        self.config = api_config.get_testnet_config()
        self.trading_config = api_config.get_trading_config()
        
        self.base_url = self.config["base_url"]
        self.api_key = self.config["api_key"]
        self.secret_key = self.config["secret_key"]
        
        # 세션 설정
        self.session = requests.Session()
        self.session.headers.update({
            'X-MBX-APIKEY': self.api_key,
            'Content-Type': 'application/json'
        })
        
        print("🔗 Binance Testnet 클라이언트 초기화 완료")
    
    def _generate_signature(self, query_string: str) -> str:
        """API 요청 서명 생성"""
        return hmac.new(
            self.secret_key.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def _make_request(self, method: str, endpoint: str, params: Dict = None, signed: bool = False) -> Optional[Dict]:
        """API 요청 실행"""
        url = f"{self.base_url}{endpoint}"
        
        if params is None:
            params = {}
        
        if signed:
            params['timestamp'] = int(time.time() * 1000)
            query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
            params['signature'] = self._generate_signature(query_string)
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url, params=params, timeout=self.trading_config['read_timeout'])
            elif method.upper() == 'POST':
                response = self.session.post(url, params=params, timeout=self.trading_config['read_timeout'])
            elif method.upper() == 'DELETE':
                response = self.session.delete(url, params=params, timeout=self.trading_config['read_timeout'])
            else:
                raise ValueError(f"지원하지 않는 HTTP 메서드: {method}")
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ API 요청 실패: {response.status_code} - {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 네트워크 오류: {e}")
            return None
    
    def test_connection(self) -> bool:
        """API 연결 테스트"""
        print("🔍 API 연결 테스트 중...")
        
        # 1. 서버 시간 확인
        server_time = self._make_request('GET', '/fapi/v1/time')
        if not server_time:
            print("❌ 서버 시간 조회 실패")
            return False
        
        print(f"✅ 서버 시간: {datetime.fromtimestamp(server_time['serverTime']/1000)}")
        
        # 2. 계정 정보 확인
        account_info = self._make_request('GET', '/fapi/v2/account', signed=True)
        if not account_info:
            print("❌ 계정 정보 조회 실패")
            return False
        
        print(f"✅ 계정 연결 성공!")
        print(f"   총 잔고: {account_info.get('totalWalletBalance', 'N/A')} USDT")
        print(f"   사용 가능: {account_info.get('availableBalance', 'N/A')} USDT")
        
        return True
    
    def get_account_info(self) -> Optional[Dict]:
        """계정 정보 조회"""
        return self._make_request('GET', '/fapi/v2/account', signed=True)
    
    def get_symbol_info(self, symbol: str = "BTCUSDT") -> Optional[Dict]:
        """심볼 정보 조회"""
        exchange_info = self._make_request('GET', '/fapi/v1/exchangeInfo')
        if not exchange_info:
            return None
        
        for symbol_info in exchange_info.get('symbols', []):
            if symbol_info['symbol'] == symbol:
                return symbol_info
        
        return None
    
    def get_current_price(self, symbol: str = "BTCUSDT") -> Optional[float]:
        """현재 가격 조회"""
        ticker = self._make_request('GET', '/fapi/v1/ticker/price', {'symbol': symbol})
        if ticker:
            return float(ticker['price'])
        return None
    
    def get_klines(self, symbol: str = "BTCUSDT", interval: str = "15m", limit: int = 100) -> Optional[pd.DataFrame]:
        """K라인 데이터 조회"""
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': limit
        }
        
        klines = self._make_request('GET', '/fapi/v1/klines', params)
        if not klines:
            return None
        
        # DataFrame으로 변환
        df = pd.DataFrame(klines, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_volume', 'trades', 'taker_buy_volume',
            'taker_buy_quote_volume', 'ignore'
        ])
        
        # 데이터 타입 변환
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = df[col].astype(float)
        
        return df[['timestamp', 'open', 'high', 'low', 'close', 'volume']]
    
    def place_order(self, symbol: str, side: str, quantity: float, order_type: str = "MARKET", 
                   price: float = None, time_in_force: str = "GTC") -> Optional[Dict]:
        """주문 실행"""
        params = {
            'symbol': symbol,
            'side': side.upper(),  # BUY or SELL
            'type': order_type.upper(),  # MARKET, LIMIT, etc.
            'quantity': quantity
        }
        
        if order_type.upper() == "LIMIT" and price:
            params['price'] = price
            params['timeInForce'] = time_in_force
        
        print(f"📝 주문 실행: {side} {quantity} {symbol} @ {price if price else 'MARKET'}")
        
        result = self._make_request('POST', '/fapi/v1/order', params, signed=True)
        
        if result:
            print(f"✅ 주문 성공: Order ID {result.get('orderId')}")
        else:
            print(f"❌ 주문 실패")
        
        return result
    
    def get_open_orders(self, symbol: str = "BTCUSDT") -> Optional[List[Dict]]:
        """미체결 주문 조회"""
        params = {'symbol': symbol} if symbol else {}
        return self._make_request('GET', '/fapi/v1/openOrders', params, signed=True)
    
    def cancel_order(self, symbol: str, order_id: int) -> Optional[Dict]:
        """주문 취소"""
        params = {
            'symbol': symbol,
            'orderId': order_id
        }
        
        result = self._make_request('DELETE', '/fapi/v1/order', params, signed=True)
        
        if result:
            print(f"✅ 주문 취소 성공: Order ID {order_id}")
        else:
            print(f"❌ 주문 취소 실패: Order ID {order_id}")
        
        return result
    
    def get_position_info(self, symbol: str = "BTCUSDT") -> Optional[List[Dict]]:
        """포지션 정보 조회"""
        positions = self._make_request('GET', '/fapi/v2/positionRisk', signed=True)
        
        if not positions:
            return None
        
        if symbol:
            return [pos for pos in positions if pos['symbol'] == symbol]
        
        return positions
    
    def print_account_summary(self):
        """계정 요약 정보 출력"""
        print("\n📊 계정 요약:")
        print("=" * 50)
        
        # 계정 정보
        account = self.get_account_info()
        if account:
            print(f"💰 총 잔고: {float(account.get('totalWalletBalance', 0)):.2f} USDT")
            print(f"💵 사용 가능: {float(account.get('availableBalance', 0)):.2f} USDT")
            print(f"📈 미실현 PnL: {float(account.get('totalUnrealizedProfit', 0)):.2f} USDT")
        
        # 현재 가격
        btc_price = self.get_current_price("BTCUSDT")
        if btc_price:
            print(f"₿ BTC 현재가: ${btc_price:,.2f}")
        
        # 포지션 정보
        positions = self.get_position_info("BTCUSDT")
        if positions:
            for pos in positions:
                size = float(pos.get('positionAmt', 0))
                if abs(size) > 0.001:  # 0.001 BTC 이상인 포지션만 표시
                    pnl = float(pos.get('unRealizedProfit', 0))
                    print(f"📍 포지션: {size:+.3f} BTC (PnL: {pnl:+.2f} USDT)")


if __name__ == "__main__":
    # 클라이언트 테스트
    print("🚀 Binance Testnet 클라이언트 테스트")
    print("=" * 50)
    
    # API 설정 검증
    if not api_config.validate_config():
        print("\n⚠️ API 키를 먼저 설정해주세요!")
        print("config/api_keys.txt 파일에 실제 API 키를 입력하세요.")
        exit(1)
    
    # 클라이언트 초기화 및 테스트
    client = BinanceTestnetClient()
    
    if client.test_connection():
        print("\n🎉 API 연동 성공!")
        client.print_account_summary()
        
        # 최근 데이터 조회 테스트
        print(f"\n📈 최근 15분봉 데이터 (5개):")
        klines = client.get_klines("BTCUSDT", "15m", 5)
        if klines is not None:
            print(klines.tail())
        
    else:
        print("\n❌ API 연동 실패!")
        print("API 키와 Secret 키를 확인해주세요.")
