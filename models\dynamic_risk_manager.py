"""
Project LEVIATHAN - 동적 리스크 관리 시스템
시장 상황에 따른 레버리지, 손절/익절, 포지션 크기를 동적으로 조정하는 핵심 모듈
"""

import pandas as pd
import numpy as np
from typing import Dict, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')


class DynamicRiskManager:
    """
    동적 리스크 관리 시스템
    
    기능:
    1. 동적 레버리지 조정 (2-10배)
    2. 동적 손절/익절 조정 (손절: 5%, 익절: 5-15%)
    3. 동적 포지션 사이징 (30-100%)
    4. 시장 상황별 리스크 최적화
    """
    
    def __init__(self, base_leverage: float = 4.0):
        """
        동적 리스크 관리자 초기화
        
        Args:
            base_leverage: 기본 레버리지 (4배)
        """
        self.base_leverage = base_leverage
        
        # 레버리지 범위 설정
        self.leverage_range = {
            'min': 2.0,    # 최소 2배
            'max': 10.0    # 최대 10배
        }
        
        # 손절/익절 범위 설정
        self.stop_loss_range = {
            'fixed': 0.05  # 고정 5%
        }
        
        self.take_profit_range = {
            'min': 0.05,   # 최소 5%
            'max': 0.15    # 최대 15%
        }
        
        # 포지션 크기 범위 설정
        self.position_size_range = {
            'min': 0.3,    # 최소 30%
            'max': 1.0     # 최대 100%
        }
        
        print("⚖️ 동적 리스크 관리 시스템 초기화 완료")
        print(f"   📊 레버리지 범위: {self.leverage_range['min']}-{self.leverage_range['max']}배")
        print(f"   📉 손절: {self.stop_loss_range['fixed']*100}% (고정)")
        print(f"   📈 익절: {self.take_profit_range['min']*100}-{self.take_profit_range['max']*100}%")
        print(f"   💰 포지션: {self.position_size_range['min']*100}-{self.position_size_range['max']*100}%")
    
    def calculate_dynamic_leverage(self, volatility: float, trend_strength: float, 
                                 market_condition: str) -> float:
        """
        동적 레버리지 계산
        
        Args:
            volatility: 시장 변동성 (0-1)
            trend_strength: 추세 강도 (-1 to 1)
            market_condition: 시장 상황
            
        Returns:
            조정된 레버리지 (2-10배)
        """
        # 기본 레버리지에서 시작
        leverage = self.base_leverage
        
        # 1. 변동성 기반 조정
        if volatility < 0.02:  # 저변동성 (2% 미만)
            leverage *= 1.8  # 증가
        elif volatility > 0.05:  # 고변동성 (5% 이상)
            leverage *= 0.6  # 감소
        else:  # 일반 변동성
            leverage *= 1.0  # 유지
        
        # 2. 추세 강도 기반 조정
        trend_abs = abs(trend_strength)
        if trend_abs > 0.02:  # 강한 추세
            leverage *= 1.2  # 증가
        elif trend_abs < 0.005:  # 약한 추세
            leverage *= 0.8  # 감소
        
        # 3. 시장 상황별 추가 조정
        market_multipliers = {
            'HIGH_RISK': 0.5,      # 고위험: 50% 감소
            'BEAR_MARKET': 0.75,   # 하락장: 25% 감소
            'SIDEWAYS': 1.0,       # 횡보장: 유지
            'LOW_VOLATILITY': 2.0, # 저변동성: 100% 증가
            'NORMAL': 1.25         # 일반: 25% 증가
        }
        
        leverage *= market_multipliers.get(market_condition, 1.0)
        
        # 범위 제한
        leverage = np.clip(leverage, self.leverage_range['min'], self.leverage_range['max'])
        
        return round(leverage, 1)
    
    def calculate_dynamic_stop_loss(self, volatility: float, market_condition: str) -> float:
        """
        동적 손절 계산 (고정 5%)
        
        Args:
            volatility: 시장 변동성
            market_condition: 시장 상황
            
        Returns:
            손절 비율 (고정 5%)
        """
        # 사용자 요구사항에 따라 손절은 고정 5%
        return self.stop_loss_range['fixed']
    
    def calculate_dynamic_take_profit(self, volatility: float, trend_strength: float,
                                    market_condition: str, confidence: float) -> Tuple[float, float]:
        """
        동적 익절 계산 (5-15% 범위)
        
        Args:
            volatility: 시장 변동성
            trend_strength: 추세 강도
            market_condition: 시장 상황
            confidence: 신호 확신도
            
        Returns:
            (최소 익절, 최대 익절) 튜플
        """
        base_min = self.take_profit_range['min']  # 5%
        base_max = self.take_profit_range['max']  # 15%
        
        # 1. 변동성 기반 조정
        if volatility > 0.05:  # 고변동성
            take_profit_min = base_min * 1.2  # 6%
            take_profit_max = base_max * 1.0  # 15%
        elif volatility < 0.02:  # 저변동성
            take_profit_min = base_min * 0.8  # 4% -> 5% 최소값 적용
            take_profit_max = base_max * 1.2  # 18% -> 15% 최대값 적용
        else:  # 일반 변동성
            take_profit_min = base_min  # 5%
            take_profit_max = base_max * 0.8  # 12%
        
        # 2. 추세 강도 기반 조정
        trend_abs = abs(trend_strength)
        if trend_abs > 0.02:  # 강한 추세
            take_profit_max *= 1.3  # 최대값 증가
        elif trend_abs < 0.005:  # 약한 추세
            take_profit_max *= 0.7  # 최대값 감소
        
        # 3. 확신도 기반 조정
        if confidence > 0.7:  # 높은 확신도
            take_profit_max *= 1.2  # 최대값 증가
        elif confidence < 0.5:  # 낮은 확신도
            take_profit_max *= 0.8  # 최대값 감소
        
        # 4. 시장 상황별 조정
        market_adjustments = {
            'HIGH_RISK': (0.9, 0.8),      # 보수적
            'BEAR_MARKET': (1.0, 0.7),    # 빠른 익절
            'SIDEWAYS': (0.8, 0.9),       # 중간
            'LOW_VOLATILITY': (0.8, 1.3), # 큰 수익 추구
            'NORMAL': (1.0, 1.0)          # 기본
        }
        
        min_adj, max_adj = market_adjustments.get(market_condition, (1.0, 1.0))
        take_profit_min *= min_adj
        take_profit_max *= max_adj
        
        # 범위 제한
        take_profit_min = np.clip(take_profit_min, self.take_profit_range['min'], 
                                self.take_profit_range['max'])
        take_profit_max = np.clip(take_profit_max, self.take_profit_range['min'], 
                                self.take_profit_range['max'])
        
        # 최소값이 최대값보다 크지 않도록 보정
        if take_profit_min > take_profit_max:
            take_profit_min = take_profit_max * 0.8
        
        return round(take_profit_min, 3), round(take_profit_max, 3)
    
    def calculate_dynamic_position_size(self, volatility: float, confidence: float,
                                      market_condition: str) -> float:
        """
        동적 포지션 크기 계산 (30-100%)
        
        Args:
            volatility: 시장 변동성
            confidence: 신호 확신도
            market_condition: 시장 상황
            
        Returns:
            포지션 크기 비율 (0.3-1.0)
        """
        # 기본 포지션 크기 (확신도 기반)
        base_position = 0.5 + (confidence - 0.5)  # 확신도에 따라 50-100%
        
        # 1. 변동성 기반 조정
        if volatility > 0.05:  # 고변동성
            position_multiplier = 0.7  # 감소
        elif volatility < 0.02:  # 저변동성
            position_multiplier = 1.3  # 증가
        else:  # 일반 변동성
            position_multiplier = 1.0  # 유지
        
        # 2. 시장 상황별 조정
        market_multipliers = {
            'HIGH_RISK': 0.6,      # 30% (0.5 * 0.6)
            'BEAR_MARKET': 0.8,    # 40% (0.5 * 0.8)
            'SIDEWAYS': 1.2,       # 60% (0.5 * 1.2)
            'LOW_VOLATILITY': 1.8, # 90% (0.5 * 1.8)
            'NORMAL': 1.4          # 70% (0.5 * 1.4)
        }
        
        position_size = base_position * position_multiplier * market_multipliers.get(market_condition, 1.0)
        
        # 범위 제한
        position_size = np.clip(position_size, self.position_size_range['min'], 
                              self.position_size_range['max'])
        
        return round(position_size, 2)
    
    def get_dynamic_risk_settings(self, volatility: float, trend_strength: float,
                                market_condition: str, confidence: float) -> Dict[str, float]:
        """
        종합 동적 리스크 설정 계산
        
        Args:
            volatility: 시장 변동성
            trend_strength: 추세 강도
            market_condition: 시장 상황
            confidence: 신호 확신도
            
        Returns:
            동적 리스크 설정 딕셔너리
        """
        # 각 요소 계산
        leverage = self.calculate_dynamic_leverage(volatility, trend_strength, market_condition)
        stop_loss = self.calculate_dynamic_stop_loss(volatility, market_condition)
        take_profit_min, take_profit_max = self.calculate_dynamic_take_profit(
            volatility, trend_strength, market_condition, confidence)
        position_size = self.calculate_dynamic_position_size(volatility, confidence, market_condition)
        
        return {
            'leverage': leverage,
            'stop_loss_pct': stop_loss,
            'take_profit_min': take_profit_min,
            'take_profit_max': take_profit_max,
            'position_size_pct': position_size,
            'market_condition': market_condition,
            'volatility': volatility,
            'confidence': confidence
        }
    
    def apply_dynamic_settings_to_dataframe(self, market_analysis: pd.DataFrame,
                                          signals: pd.DataFrame) -> pd.DataFrame:
        """
        데이터프레임에 동적 리스크 설정 적용
        
        Args:
            market_analysis: 시장 분석 결과
            signals: 거래 신호 데이터
            
        Returns:
            동적 설정이 적용된 데이터프레임
        """
        print("⚖️ 동적 리스크 설정 적용 중...")
        
        # 결과 데이터프레임 초기화
        dynamic_settings = market_analysis.copy()
        
        # 각 시점별 동적 설정 계산
        for idx in market_analysis.index:
            if idx not in signals.index:
                continue
                
            # 시장 분석 데이터
            volatility = market_analysis.loc[idx, 'volatility']
            trend_strength = market_analysis.loc[idx, 'trend_strength']
            market_condition = market_analysis.loc[idx, 'market_condition']
            
            # 신호 확신도 (없으면 기본값 0.5)
            confidence = signals.loc[idx, 'confidence'] if 'confidence' in signals.columns else 0.5
            
            # 동적 설정 계산
            risk_settings = self.get_dynamic_risk_settings(
                volatility, trend_strength, market_condition, confidence)
            
            # 결과에 추가
            for key, value in risk_settings.items():
                if key not in ['market_condition', 'volatility', 'confidence']:  # 중복 제거
                    dynamic_settings.loc[idx, f'dynamic_{key}'] = value
        
        print(f"✅ 동적 리스크 설정 적용 완료: {len(dynamic_settings)}개 데이터 포인트")
        
        return dynamic_settings


if __name__ == "__main__":
    # 테스트 코드
    print("🧪 동적 리스크 관리 시스템 테스트")
    
    # 동적 리스크 관리자 초기화
    risk_manager = DynamicRiskManager()
    
    # 테스트 시나리오
    test_scenarios = [
        {'volatility': 0.01, 'trend': 0.03, 'condition': 'LOW_VOLATILITY', 'confidence': 0.8},
        {'volatility': 0.06, 'trend': -0.025, 'condition': 'HIGH_RISK', 'confidence': 0.6},
        {'volatility': 0.03, 'trend': 0.001, 'condition': 'SIDEWAYS', 'confidence': 0.7},
        {'volatility': 0.04, 'trend': -0.015, 'condition': 'BEAR_MARKET', 'confidence': 0.5},
        {'volatility': 0.025, 'trend': 0.015, 'condition': 'NORMAL', 'confidence': 0.65}
    ]
    
    print("\n📊 동적 설정 테스트 결과:")
    for i, scenario in enumerate(test_scenarios, 1):
        settings = risk_manager.get_dynamic_risk_settings(
            scenario['volatility'], scenario['trend'], 
            scenario['condition'], scenario['confidence'])
        
        print(f"\n{i}. {scenario['condition']} 시장:")
        print(f"   레버리지: {settings['leverage']}배")
        print(f"   포지션: {settings['position_size_pct']*100:.0f}%")
        print(f"   손절: {settings['stop_loss_pct']*100:.1f}%")
        print(f"   익절: {settings['take_profit_min']*100:.1f}%-{settings['take_profit_max']*100:.1f}%")
