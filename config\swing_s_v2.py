"""
Project LEVIATHAN - Swing Strategy S v2.0 Configuration
진정한 스윙 모델 - 장기 보유 및 큰 파동 포착을 위한 설정
"""

from dataclasses import dataclass
from typing import Dict, Any, List
from pathlib import Path


@dataclass
class SwingStrategyV2Config:
    """LEVIATHAN-S v2.0 전략 설정 (진정한 스윙 모델)"""
    
    # ============================================================================
    # 전략 메타데이터
    # ============================================================================
    strategy_name: str = "LEVIATHAN-S"
    version: str = "v2.0"
    description: str = "진정한 스윙 모델 - 장기 보유 및 큰 파동 포착"
    created_date: str = "2025-06-17"
    
    # ============================================================================
    # 데이터 설정 - 다중 시간 프레임 확장
    # ============================================================================
    # 시간 프레임 설정 (확장된 다중 프레임)
    execution_timeframe: str = "15m"  # 진입/청산 신호용
    trend_timeframe: str = "4h"       # 중기 추세 판단용
    long_trend_timeframe: str = "1D"  # 장기 추세 판단용 (새로 추가)
    
    # 데이터 파일 경로
    price_data_file: str = "data/BTCUSDT_15m.csv"
    trend_data_file: str = "data/BTCUSDT_4h.csv"
    daily_data_file: str = "data/BTCUSDT_1D.csv"  # 일봉 데이터 (새로 추가)
    feature_data_file: str = "data/processed/features_swing_v2.csv"
    
    # 훈련/테스트 분할
    train_test_split_date: str = "2024-01-01"
    
    # ============================================================================
    # Triple Barrier Method (TBM) 파라미터 - 스윙용 재설계
    # ============================================================================
    # 손익절 설정 (스윙 트레이딩에 맞게 확장)
    stop_loss_pct: float = 0.04    # -4% (더 넓은 손절)
    take_profit_pct: float = 0.12  # +12% (큰 파동 포착)
    
    # 최대 보유 기간 (15분봉 기준) - 대폭 확장
    max_hold_period: int = 192     # 15분봉 * 192 = 48시간 (2일)
    
    # 타임 배리어 설정
    time_barrier_hours: float = 48.0  # 48시간 후 강제 청산
    
    # ============================================================================
    # 거래 설정 - ML 적응형 동적 시스템 적용
    # ============================================================================
    # 기본 레버리지 (동적 조정 범위: 2-10배)
    leverage: int = 4              # 기본 4배 (ML이 2-10배로 동적 조정)
    max_position_size: float = 1.0   # 계좌 대비 100% (최대 공격적)

    # 거래 비용
    fee_rate: float = 0.0004       # 0.04% (Taker)
    slippage_rate: float = 0.0002  # 0.02%

    # 기본 신호 임계값 (동적 조정됨)
    probability_threshold: float = 0.55   # 55% (기본값, ML이 시장별로 동적 조정)
    long_threshold: float = 0.402         # 롱 진입 임계값 (80th percentile)
    short_threshold: float = 0.165        # 숏 진입 임계값 (80th percentile)
    confidence_weight: float = 0.1        # 확신도 가중치

    # ============================================================================
    # ML 적응형 동적 시스템 설정 (v1에서 가져옴)
    # ============================================================================
    # 동적 레버리지 범위
    min_leverage: float = 2.0      # 최소 2배 (고위험 시장)
    max_leverage: float = 10.0     # 최대 10배 (저변동성 시장)
    default_leverage: float = 4.0  # 기본 4배

    # 동적 손절 범위 (스윙용 재조정)
    min_stop_loss: float = 0.02    # 최소 2% (스윙용 확장)
    max_stop_loss: float = 0.08    # 최대 8% (스윙용 확장)

    # ML 동적 익절 범위 (스윙용 재조정)
    min_take_profit: float = 0.06  # 최소 6% (스윙용 확장)
    max_take_profit: float = 0.24  # 최대 24% (스윙용 확장)
    adaptive_take_profit: bool = True  # ML 기반 동적 익절 활성화

    # 동적 임계값 범위 (시장별 조정)
    min_confidence_threshold: float = 0.5   # 최소 50%
    max_confidence_threshold: float = 0.8   # 최대 80%

    # 신호 임계값 최적화 범위
    long_threshold_range: tuple = (0.5, 0.8)    # 롱 신호 임계값 범위
    short_threshold_range: tuple = (0.5, 0.8)   # 숏 신호 임계값 범위

    # 시장 분류 기준
    high_volatility_threshold: float = 0.8    # 연변동성 80% 이상
    low_volatility_threshold: float = 0.4     # 연변동성 40% 이하
    bear_market_threshold: float = -0.3       # 24개월간 -30% 이상 하락
    bull_market_threshold: float = 0.5        # 24개월간 +50% 이상 상승

    # 리스크 관리 (스윙용 재조정)
    max_single_loss: float = 0.08   # 단일 거래 최대 손실 8% (스윙용 확장)
    min_single_loss: float = 0.02   # 단일 거래 최소 손실 2% (스윙용 확장)
    max_drawdown: float = 0.25      # 최대 낙폭 25% (스윙용 확장)
    
    # ============================================================================
    # 피처 엔지니어링 설정 - 스윙용 확장
    # ============================================================================
    # 기술적 지표 파라미터 (장기 신호 집중)
    technical_indicators: Dict[str, Any] = None
    
    # 상호작용 피처 설정 (확장)
    enable_interaction_features: bool = True
    interaction_features: List[str] = None
    
    def __post_init__(self):
        if self.technical_indicators is None:
            self.technical_indicators = {
                # RSI 설정 (장기화)
                'rsi_period': 30,  # 14 → 30 (더 긴 기간)
                
                # MACD 설정 (장기화)
                'macd_fast': 24,   # 12 → 24
                'macd_slow': 52,   # 26 → 52
                'macd_signal': 18, # 9 → 18
                
                # 볼린저 밴드 (장기화)
                'bb_period': 40,   # 20 → 40
                'bb_std': 2.0,
                
                # 이동평균 (장기 추세 집중)
                'sma_periods': [50, 100, 200],  # 더 긴 기간들
                
                # 기타 지표들 (장기화)
                'atr_period': 28,        # 14 → 28
                'stoch_k': 28,           # 14 → 28
                'stoch_d': 6,            # 3 → 6
                'williams_r_period': 28, # 14 → 28
                'cci_period': 40,        # 20 → 40
                'roc_period': 20,        # 10 → 20
                'mfi_period': 28,        # 14 → 28
                'trix_period': 28,       # 14 → 28
                'dpo_period': 40,        # 20 → 40
                'kama_period': 20,       # 10 → 20
                'tema_period': 60,       # 30 → 60
                'vwma_period': 40,       # 20 → 40
                'hv_period': 40,         # 20 → 40
                'chaikin_period': 20,    # 10 → 20
                'eom_period': 28,        # 14 → 28
                'sma_volume_period': 40, # 20 → 40
                'price_volume_trend': True,
                'obv': True,
                'cmf_period': 40,        # 20 → 40
                'vpt': True,
                
                # 일봉 기반 지표들 (새로 추가)
                'daily_rsi_period': 14,
                'daily_macd_fast': 12,
                'daily_macd_slow': 26,
                'daily_macd_signal': 9,
                'daily_bb_period': 20,
                'daily_sma_periods': [20, 50, 200],
                'daily_atr_period': 14,
                'daily_stoch_k': 14,
                'daily_williams_r': 14,
                'daily_cci_period': 20,
                'daily_roc_period': 10,
                'daily_mfi_period': 14,
                'daily_trix_period': 14,
                'daily_dpo_period': 20,
                'daily_kama_period': 10,
                'daily_tema_period': 30,
                'daily_vwma_period': 20,
                'daily_hv_period': 20,
                'daily_chaikin_period': 10,
                'daily_eom_period': 14,
                'daily_cmf_period': 20
            }
        
        if self.interaction_features is None:
            self.interaction_features = [
                'Volatility_Trend_Filter',
                'VWAP_Divergence',           # 새로 추가: 가격&거래량 상호작용
                'Long_Term_Momentum',        # 새로 추가: 장기 모멘텀
                'Multi_Timeframe_Alignment', # 새로 추가: 다중 시간 프레임 정렬
                'Volume_Price_Confirmation', # 새로 추가: 거래량 확인
                'Trend_Strength_Filter'      # 새로 추가: 추세 강도 필터
            ]
    
    # ============================================================================
    # 모델 설정
    # ============================================================================
    # 모델 파일 경로
    model_dir: str = "models"
    long_model_file: str = "models/swing_s_v2_long_model.pkl"
    short_model_file: str = "models/swing_s_v2_short_model.pkl"
    
    # LightGBM 하이퍼파라미터 (스윙용 최적화)
    lgb_params: Dict[str, Any] = None
    
    def get_lgb_params(self):
        if self.lgb_params is None:
            self.lgb_params = {
                "objective": "binary",
                "metric": "binary_logloss",
                "boosting_type": "gbdt",
                "num_leaves": 63,          # 31 → 63 (더 복잡한 모델)
                "learning_rate": 0.03,     # 0.05 → 0.03 (더 안정적)
                "feature_fraction": 0.85,  # 0.9 → 0.85
                "bagging_fraction": 0.75,  # 0.8 → 0.75
                "bagging_freq": 3,         # 5 → 3
                "max_depth": 8,            # 깊이 제한 추가
                "min_data_in_leaf": 50,    # 과적합 방지
                "lambda_l1": 0.1,          # L1 정규화
                "lambda_l2": 0.1,          # L2 정규화
                "verbose": -1,
                "random_state": 42
            }
        return self.lgb_params
    
    # ============================================================================
    # 백테스팅 설정
    # ============================================================================
    # 초기 자본
    initial_capital: float = 10000.0
    
    # 백테스팅 기간
    backtest_start_date: str = "2023-01-01"
    backtest_end_date: str = "2024-12-31"
    
    # Walk-Forward Analysis 설정
    train_months: int = 24  # 24개월 훈련
    test_months: int = 6    # 6개월 테스트
    step_months: int = 6    # 6개월씩 이동
    
    # ============================================================================
    # ML 적응형 시장별 설정 (v1에서 가져옴)
    # ============================================================================
    def get_ml_adaptive_settings_for_market(self, market_type: str) -> Dict[str, Any]:
        """🧠 ML 기반 시장별 동적 설정"""

        if market_type == 'bull_market':
            return {
                'leverage': 8.0,
                'stop_loss_pct': 0.03,  # 2% → 3% (스윙용)
                'take_profit_pct': 0.18, # 8% → 18% (스윙용 큰 파동 포착)
                'ml_adaptive_tp': True,
                'confidence_threshold': 0.4,
                'long_threshold': 0.55,  # 낮춤 (더 많은 롱 신호)
                'short_threshold': 0.65, # 높임 (더 적은 숏 신호)
                'position_size': 1.0,
                'strategy_name': '스윙 상승장 공격적 전략'
            }
        elif market_type == 'bear_market':
            return {
                'leverage': 4.0,
                'stop_loss_pct': 0.05,  # 3% → 5% (스윙용)
                'take_profit_pct': 0.08, # 4% → 8% (스윙용)
                'ml_adaptive_tp': True,
                'confidence_threshold': 0.5,
                'long_threshold': 0.70,  # 높임 (더 적은 롱 신호)
                'short_threshold': 0.50, # 낮춤 (더 많은 숏 신호)
                'position_size': 0.5,
                'strategy_name': '스윙 하락장 적응 전략'
            }
        elif market_type == 'high_risk':
            return {
                'leverage': 3.0,
                'stop_loss_pct': 0.06,  # 4% → 6% (스윙용)
                'take_profit_pct': 0.06, # 3% → 6% (스윙용)
                'ml_adaptive_tp': True,
                'confidence_threshold': 0.6,
                'long_threshold': 0.65,  # 높임 (더 보수적)
                'short_threshold': 0.65, # 높임 (더 보수적)
                'position_size': 0.3,
                'strategy_name': '스윙 고위험 보수적 전략'
            }
        elif market_type == 'low_volatility':
            return {
                'leverage': 10.0,
                'stop_loss_pct': 0.02,  # 1.5% → 2% (스윙용)
                'take_profit_pct': 0.15, # 6% → 15% (스윙용)
                'ml_adaptive_tp': True,
                'confidence_threshold': 0.3,
                'long_threshold': 0.45,  # 낮춤 (더 많은 신호)
                'short_threshold': 0.45, # 낮춤 (더 많은 신호)
                'position_size': 1.0,
                'strategy_name': '스윙 저변동성 최대활용 전략'
            }
        else:  # normal_market
            return {
                'leverage': self.default_leverage,
                'stop_loss_pct': self.stop_loss_pct,
                'take_profit_pct': self.take_profit_pct,
                'ml_adaptive_tp': self.adaptive_take_profit,
                'confidence_threshold': self.probability_threshold,
                'long_threshold': self.long_threshold,   # 기본값 0.402
                'short_threshold': self.short_threshold, # 기본값 0.165
                'position_size': self.max_position_size,
                'strategy_name': '일반 시장 전략'
            }

    def get_dynamic_thresholds(self, market_condition: str) -> Dict[str, float]:
        """동적 임계값 반환"""
        thresholds = {
            'bull_market': {'long': 0.55, 'short': 0.65},
            'bear_market': {'long': 0.70, 'short': 0.55},
            'sideways_market': {'long': 0.60, 'short': 0.60},
            'high_volatility': {'long': 0.65, 'short': 0.65},
            'low_volatility': {'long': 0.45, 'short': 0.45}
        }
        return thresholds.get(market_condition, {'long': 0.55, 'short': 0.55})

    # ============================================================================
    # 유틸리티 메서드
    # ============================================================================
    def get_data_paths(self) -> Dict[str, str]:
        """데이터 파일 경로 반환"""
        return {
            'price_15m': self.price_data_file,
            'price_4h': self.trend_data_file,
            'price_1d': self.daily_data_file,
            'features': self.feature_data_file
        }

    def get_model_paths(self) -> Dict[str, str]:
        """모델 파일 경로 반환"""
        return {
            'long_model': self.long_model_file,
            'short_model': self.short_model_file
        }

    def get_tbm_params(self) -> Dict[str, Any]:
        """TBM 파라미터 반환"""
        return {
            'stop_loss_pct': self.stop_loss_pct,
            'take_profit_pct': self.take_profit_pct,
            'max_hold_period': self.max_hold_period,
            'time_barrier_hours': self.time_barrier_hours
        }

    def get_ml_adaptive_settings(self) -> Dict[str, Any]:
        """ML 적응형 설정 반환"""
        return {
            'max_leverage': self.max_leverage,
            'min_leverage': self.min_leverage,
            'default_leverage': self.default_leverage,
            'max_single_loss': self.max_single_loss,
            'min_single_loss': self.min_single_loss,
            'max_drawdown': self.max_drawdown,
            'position_size_max': self.max_position_size,
            'min_take_profit': self.min_take_profit,
            'max_take_profit': self.max_take_profit,
            'adaptive_take_profit': self.adaptive_take_profit,
            'high_volatility_threshold': self.high_volatility_threshold,
            'low_volatility_threshold': self.low_volatility_threshold,
            'bear_market_threshold': self.bear_market_threshold,
            'bull_market_threshold': self.bull_market_threshold
        }
    
    def print_summary(self):
        """설정 요약 출력"""
        print(f"🐋 {self.strategy_name} {self.version} - 설정 요약")
        print("=" * 60)
        print(f"📊 시간 프레임: {self.execution_timeframe} + {self.trend_timeframe} + {self.long_trend_timeframe}")
        print(f"⚖️ 레버리지: {self.leverage}x (장기 보유용)")
        print(f"📉 손절매: -{self.stop_loss_pct*100:.1f}% (넓은 손절)")
        print(f"📈 익절: +{self.take_profit_pct*100:.1f}% (큰 파동 포착)")
        print(f"⏰ 최대 보유: {self.time_barrier_hours}시간 (2일)")
        print(f"🎯 신호 임계값: {self.probability_threshold} (높은 확신도)")
        print(f"💸 수수료: {self.fee_rate*100:.2f}%")
        print(f"🧠 상호작용 피처: {len(self.interaction_features)}개")


# 전역 설정 인스턴스
swing_s_v2_config = SwingStrategyV2Config()
