"""
Project LEVIATHAN - 설정 모듈 단위 테스트
"""

import unittest
import sys
from pathlib import Path

# 프로젝트 루트 경로 추가
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.settings import Settings, get_settings


class TestSettings(unittest.TestCase):
    """설정 모듈 테스트"""
    
    def setUp(self):
        """테스트 설정"""
        self.settings = Settings()
    
    def test_settings_initialization(self):
        """설정 초기화 테스트"""
        self.assertIsNotNone(self.settings.data)
        self.assertIsNotNone(self.settings.trading)
        self.assertIsNotNone(self.settings.model)
        self.assertIsNotNone(self.settings.feature)
        self.assertIsNotNone(self.settings.backtest)
        self.assertIsNotNone(self.settings.logging)
    
    def test_trading_config_values(self):
        """거래 설정값 테스트"""
        trading = self.settings.trading
        
        # 기본값 확인
        self.assertEqual(trading.LEVERAGE, 4)
        self.assertEqual(trading.STOP_LOSS_PCT, 0.02)
        self.assertEqual(trading.TAKE_PROFIT_PCT, 0.05)
        self.assertEqual(trading.FEE_RATE, 0.0004)
        
        # 신호 임계값 확인
        thresholds = trading.SIGNAL_THRESHOLDS
        self.assertIn('high_confidence', thresholds)
        self.assertIn('exit_threshold', thresholds)
        self.assertEqual(thresholds['exit_threshold'], 0.15)
    
    def test_update_trading_config(self):
        """거래 설정 업데이트 테스트"""
        # 설정 업데이트
        self.settings.update_trading_config(LEVERAGE=2, STOP_LOSS_PCT=0.015)
        
        # 업데이트 확인
        self.assertEqual(self.settings.trading.LEVERAGE, 2)
        self.assertEqual(self.settings.trading.STOP_LOSS_PCT, 0.015)
        
        # 잘못된 설정 업데이트 시 오류 발생 확인
        with self.assertRaises(ValueError):
            self.settings.update_trading_config(INVALID_SETTING=123)
    
    def test_path_methods(self):
        """경로 메서드 테스트"""
        # 모델 경로
        binary_path = self.settings.get_model_path('binary')
        self.assertTrue(str(binary_path).endswith('binary_model.pkl'))
        
        multiclass_path = self.settings.get_model_path('multiclass')
        self.assertTrue(str(multiclass_path).endswith('multiclass_model.pkl'))
        
        # 잘못된 모델 타입
        with self.assertRaises(ValueError):
            self.settings.get_model_path('invalid')
        
        # 데이터 경로
        price_path = self.settings.get_data_path('price')
        self.assertTrue(str(price_path).endswith('BTCUSDT_15m.csv'))
        
        # 잘못된 데이터 타입
        with self.assertRaises(ValueError):
            self.settings.get_data_path('invalid')
    
    def test_backtest_period_update(self):
        """백테스팅 기간 업데이트 테스트"""
        start_date = "2023-01-01"
        end_date = "2023-12-31"
        
        self.settings.update_backtest_period(start_date, end_date)
        
        self.assertEqual(self.settings.backtest.DEFAULT_START_DATE, start_date)
        self.assertEqual(self.settings.backtest.DEFAULT_END_DATE, end_date)
    
    def test_singleton_pattern(self):
        """싱글톤 패턴 테스트"""
        settings1 = get_settings()
        settings2 = get_settings()
        
        # 같은 인스턴스인지 확인
        self.assertIs(settings1, settings2)
    
    def test_to_dict(self):
        """딕셔너리 변환 테스트"""
        config_dict = self.settings.to_dict()
        
        # 필수 키 확인
        required_keys = ['data', 'trading', 'model', 'feature', 'backtest', 'logging']
        for key in required_keys:
            self.assertIn(key, config_dict)
        
        # 값 타입 확인
        self.assertIsInstance(config_dict['trading']['LEVERAGE'], int)
        self.assertIsInstance(config_dict['trading']['STOP_LOSS_PCT'], float)


if __name__ == '__main__':
    unittest.main()
