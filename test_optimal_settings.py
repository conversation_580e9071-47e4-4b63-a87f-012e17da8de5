"""
🎯 Project LEVIATHAN - 최적화 설정 테스트 시스템

3가지 권장 설정을 쉽게 테스트할 수 있는 시스템입니다.

Author: 강현모
Date: 2024-12-14
Version: 1.0
"""

import pandas as pd
import numpy as np
import sys
import os
from typing import Dict, List

# 프로젝트 루트 경로 추가
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backtesting.dual_model_backtest import DualModelBacktester


class OptimalSettingsTester:
    """최적화 설정 테스트기"""
    
    def __init__(self):
        """초기화"""
        self.results = []
        self.settings = self._load_optimal_settings()
        print("🎯 LEVIATHAN 최적화 설정 테스트 시스템")
        print("🚀 3가지 권장 설정을 테스트합니다")
    
    def _load_optimal_settings(self) -> Dict:
        """최적화 설정 로드"""
        return {
            "EXTREME_PROFIT": {
                "name": "극한 수익률 (SS급)",
                "description": "최대 수익률 추구 (고위험)",
                "leverage": 10.0,
                "position_size": 0.5,  # 50%
                "stop_loss_pct": 0.02,  # -2.0%
                "take_profit_pct": 0.05,  # +5.0%
                "expected_annual_return": 9589.26,
                "expected_mdd": 6.28,
                "target_user": "고위험 고수익 추구자",
                "warning": "높은 변동성, 강한 멘탈 필요"
            },
            "BALANCED_OPTIMAL": {
                "name": "균형 최적화 (S급)",
                "description": "수익률과 안정성의 최적 균형",
                "leverage": 4.0,
                "position_size": 0.1,  # 10%
                "stop_loss_pct": 0.015,  # -1.5%
                "take_profit_pct": 0.06,  # +6.0%
                "expected_annual_return": 412.07,
                "expected_mdd": 0.86,
                "target_user": "안정적 고수익 추구자",
                "warning": "가장 권장하는 설정"
            },
            "SAFE_GROWTH": {
                "name": "안전 우선 (A급)",
                "description": "안전성 우선, 꾸준한 성장",
                "leverage": 2.0,
                "position_size": 0.1,  # 10%
                "stop_loss_pct": 0.02,  # -2.0%
                "take_profit_pct": 0.05,  # +5.0%
                "expected_annual_return": 60.10,
                "expected_mdd": 0.26,
                "target_user": "안전성 우선 투자자",
                "warning": "초보자에게 권장"
            }
        }
    
    def test_all_settings(self, start_date: str = '2024-10-01', 
                         end_date: str = '2024-12-14') -> List[Dict]:
        """모든 설정 테스트"""
        print(f"\n🚀 3가지 최적화 설정 테스트 시작")
        print(f"📅 테스트 기간: {start_date} ~ {end_date}")
        print("=" * 70)
        
        for setting_key, setting in self.settings.items():
            print(f"\n🎯 {setting['name']} 테스트")
            print(f"   설명: {setting['description']}")
            print(f"   대상: {setting['target_user']}")
            print(f"   주의: {setting['warning']}")
            print("-" * 50)
            
            try:
                result = self._test_single_setting(setting_key, setting, start_date, end_date)
                self.results.append(result)
                self._print_setting_result(result)
                
            except Exception as e:
                print(f"   ❌ 테스트 실패: {e}")
                failed_result = {
                    'setting_key': setting_key,
                    'setting_name': setting['name'],
                    'status': 'FAILED',
                    'error': str(e)
                }
                self.results.append(failed_result)
        
        return self.results
    
    def _test_single_setting(self, setting_key: str, setting: Dict, 
                           start_date: str, end_date: str) -> Dict:
        """단일 설정 테스트"""
        print(f"   ⚙️ 설정 적용 중...")
        print(f"      레버리지: {setting['leverage']}x")
        print(f"      포지션 크기: {setting['position_size']*100:.0f}%")
        print(f"      손절: {setting['stop_loss_pct']*100:.1f}%")
        print(f"      익절: {setting['take_profit_pct']*100:.1f}%")
        
        # 백테스터 초기화
        backtester = DualModelBacktester(initial_capital=10000)
        backtester.leverage = setting['leverage']
        backtester.stop_loss_pct = setting['stop_loss_pct']
        backtester.take_profit_pct = setting['take_profit_pct']
        
        # 포지션 사이징은 레버리지로 조정 (임시)
        if setting['position_size'] > 0.1:
            effective_leverage = setting['leverage'] * (setting['position_size'] / 0.1)
            backtester.leverage = min(effective_leverage, 10.0)
        
        print(f"      실제 레버리지: {backtester.leverage:.1f}x")
        
        # 백테스팅 실행
        print(f"   🚀 백테스팅 실행 중...")
        results = backtester.run_backtest(start_date, end_date)
        
        # 연간 수익률 계산
        months = 3  # 3개월 테스트
        monthly_return = results['total_return'] / months
        annual_return = monthly_return * 12
        
        # 결과 정리
        result = {
            'setting_key': setting_key,
            'setting_name': setting['name'],
            'status': 'SUCCESS',
            'test_period': f"{start_date} ~ {end_date}",
            
            # 설정 정보
            'leverage': backtester.leverage,
            'position_size': setting['position_size'],
            'stop_loss_pct': setting['stop_loss_pct'],
            'take_profit_pct': setting['take_profit_pct'],
            
            # 실제 성과
            'actual_3month_return': results['total_return'],
            'actual_annual_return': annual_return,
            'actual_win_rate': results['win_rate'],
            'actual_mdd': results['max_drawdown'],
            'actual_sharpe': results['sharpe_ratio'],
            'actual_trades': results['total_trades'],
            
            # 예상 성과
            'expected_annual_return': setting['expected_annual_return'],
            'expected_mdd': setting['expected_mdd'],
            
            # 성과 비교
            'return_accuracy': (annual_return / setting['expected_annual_return']) * 100,
            'mdd_accuracy': (results['max_drawdown'] / setting['expected_mdd']) * 100 if setting['expected_mdd'] > 0 else 100,
            
            # 기타
            'target_user': setting['target_user'],
            'warning': setting['warning']
        }
        
        return result
    
    def _print_setting_result(self, result: Dict) -> None:
        """설정별 결과 출력"""
        if result['status'] == 'FAILED':
            print(f"   ❌ 실패: {result['error']}")
            return
        
        print(f"   📊 실제 성과:")
        print(f"      3개월 수익률: {result['actual_3month_return']:+.2f}%")
        print(f"      연간 수익률: {result['actual_annual_return']:+.2f}%")
        print(f"      승률: {result['actual_win_rate']:.1f}%")
        print(f"      최대 낙폭: {result['actual_mdd']:.2f}%")
        print(f"      샤프 비율: {result['actual_sharpe']:.2f}")
        print(f"      거래 횟수: {result['actual_trades']}회")
        
        print(f"   🎯 예상 대비 정확도:")
        print(f"      수익률 정확도: {result['return_accuracy']:.1f}%")
        print(f"      MDD 정확도: {result['mdd_accuracy']:.1f}%")
        
        # 성과 평가
        if result['return_accuracy'] > 80:
            accuracy_grade = "🎯 매우 정확"
        elif result['return_accuracy'] > 60:
            accuracy_grade = "✅ 정확"
        elif result['return_accuracy'] > 40:
            accuracy_grade = "⚠️ 보통"
        else:
            accuracy_grade = "❌ 부정확"
        
        print(f"      평가: {accuracy_grade}")
    
    def generate_comparison_report(self) -> None:
        """비교 보고서 생성"""
        print(f"\n📋 3가지 설정 비교 보고서")
        print("=" * 70)
        
        if not self.results:
            print("❌ 분석할 결과가 없습니다.")
            return
        
        # 성공한 결과만 필터링
        successful_results = [r for r in self.results if r['status'] == 'SUCCESS']
        
        if not successful_results:
            print("❌ 성공한 테스트가 없습니다.")
            return
        
        # 비교 테이블
        print(f"📊 성과 비교 (연간 기준):")
        print(f"{'설정':<15} {'수익률':<12} {'MDD':<8} {'샤프':<8} {'거래':<6} {'평가'}")
        print("-" * 65)
        
        for result in successful_results:
            setting_name = result['setting_key'][:12]
            return_str = f"{result['actual_annual_return']:+.1f}%"
            mdd_str = f"{result['actual_mdd']:.2f}%"
            sharpe_str = f"{result['actual_sharpe']:.2f}"
            trades_str = f"{result['actual_trades']}회"
            
            # 등급 평가
            if result['actual_annual_return'] > 1000:
                grade = "🏆 SS급"
            elif result['actual_annual_return'] > 300:
                grade = "🥇 S급"
            elif result['actual_annual_return'] > 100:
                grade = "🥈 A급"
            elif result['actual_annual_return'] > 50:
                grade = "🥉 B급"
            else:
                grade = "📊 C급"
            
            print(f"{setting_name:<15} {return_str:<12} {mdd_str:<8} {sharpe_str:<8} {trades_str:<6} {grade}")
        
        # 최고 성과
        best_result = max(successful_results, key=lambda x: x['actual_annual_return'])
        print(f"\n🏆 최고 성과: {best_result['setting_name']}")
        print(f"   연간 수익률: {best_result['actual_annual_return']:+.2f}%")
        print(f"   최대 낙폭: {best_result['actual_mdd']:.2f}%")
        print(f"   대상: {best_result['target_user']}")
        
        # 권장 설정
        print(f"\n🎯 권장 설정:")
        balanced_result = next((r for r in successful_results if r['setting_key'] == 'BALANCED_OPTIMAL'), None)
        if balanced_result:
            print(f"   {balanced_result['setting_name']}")
            print(f"   연간 수익률: {balanced_result['actual_annual_return']:+.2f}%")
            print(f"   최대 낙폭: {balanced_result['actual_mdd']:.2f}%")
            print(f"   이유: 수익률과 안정성의 최적 균형")
        
        print(f"\n✅ 3가지 설정 테스트 완료!")


def main():
    """메인 실행 함수"""
    print("🎯 Project LEVIATHAN - 최적화 설정 테스트")
    print("🚀 3가지 권장 설정의 실제 성과를 확인합니다")
    print("=" * 70)
    
    # 테스트 기간 설정
    start_date = '2024-10-01'
    end_date = '2024-12-14'
    
    print(f"📅 테스트 기간: {start_date} ~ {end_date} (약 3개월)")
    print(f"🎯 목표: 최적화 설정의 실제 성과 검증")
    
    # 테스터 초기화
    tester = OptimalSettingsTester()
    
    # 모든 설정 테스트
    results = tester.test_all_settings(start_date, end_date)
    
    # 비교 보고서
    tester.generate_comparison_report()
    
    print(f"\n🎉 테스트 완료!")
    print(f"📋 결과는 LEVIATHAN_OPTIMIZATION_RESULTS.md 파일을 참고하세요")


if __name__ == "__main__":
    main()
