"""
Project LEVIATHAN - 모델 통합 테스트
LightGBM 모델과 예측 엔진의 전체 워크플로우를 테스트합니다.
"""

import pandas as pd
import numpy as np
import sys
from pathlib import Path

# 프로젝트 루트를 Python 경로에 추가
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from models import TargetGenerator, LightGBMModelV2, BinaryTradingModel, HybridPredictor
from data.features import generate_features_from_csv


def test_target_generator():
    """
    타겟 생성기 테스트
    """
    print("🎯 타겟 생성기 테스트...")
    
    try:
        # 간단한 설정으로 타겟 생성기 초기화
        config = {
            'forward_periods': [4],  # 1시간 후만 테스트
            'return_thresholds': {
                'strong_buy': 0.015,
                'buy': 0.005,
                'hold': 0.005,
                'sell': -0.005,
                'strong_sell': -0.015
            },
            'volatility_adjustment': False,  # 속도를 위해 비활성화
            'outlier_clip': 0.05
        }
        
        target_gen = TargetGenerator(config)
        
        # 타겟 정보 확인
        info = target_gen.get_target_info()
        print(f"   ✅ 설정 로드 완료")
        print(f"   📊 예측 기간: {info['forward_periods_minutes']} 분")
        
        # 간단한 타겟 생성 (전체가 아닌 샘플만)
        price_data = target_gen.load_price_data()
        sample_prices = price_data['close'].tail(1000)  # 최근 1000개만
        
        forward_returns = target_gen.calculate_forward_returns(sample_prices, 4)
        classification_target = target_gen.create_classification_target(forward_returns)
        
        print(f"   ✅ 샘플 타겟 생성 완료: {len(classification_target)}개")
        print(f"   📊 클래스 분포: {classification_target.value_counts().to_dict()}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 타겟 생성기 테스트 실패: {e}")
        return False


def test_lgb_model():
    """
    LightGBM 모델 v2 테스트
    """
    print("\n🤖 LightGBM 모델 v2 테스트...")

    try:
        # 이진 분류 모델 초기화
        model = BinaryTradingModel()
        
        # 빠른 훈련 (최적화 없이)
        print("   🚀 빠른 이진 분류 모델 훈련...")
        model.train_binary_model(optimize=False)

        # 모델 저장
        model_path = 'models/test_binary_model.pkl'
        model.save_model(model_path)
        print(f"   ✅ 모델 저장: {model_path}")
        
        return True, model_path
        
    except Exception as e:
        print(f"   ❌ LightGBM 모델 테스트 실패: {e}")
        import traceback
        traceback.print_exc()
        return False, None


def test_predictor(model_path):
    """
    하이브리드 예측 엔진 테스트
    """
    print("\n🔮 하이브리드 예측 엔진 테스트...")

    try:
        # 하이브리드 예측 엔진 초기화
        predictor = HybridPredictor(model_path)
        
        # 최신 피처 데이터 가져오기
        print("   📊 최신 피처 데이터 가져오기...")
        latest_features = predictor.get_latest_features(10)

        # 하이브리드 신호 생성
        print("   🎯 하이브리드 신호 생성...")
        signals_df = predictor.generate_hybrid_signals(latest_features)

        # 신호 품질 분석
        analysis = predictor.analyze_signal_quality(signals_df)

        print(f"   ✅ 하이브리드 예측 완료")
        print(f"      총 신호: {analysis['overall']['total_signals']}개")
        print(f"      거래 신호: {analysis['overall']['trading_signals']}개")
        print(f"      평균 익절 확률: {analysis['overall']['avg_win_probability']:.1%}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 예측 엔진 테스트 실패: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_full_workflow():
    """
    전체 워크플로우 통합 테스트
    """
    print("🚀 Project LEVIATHAN - 모델 통합 테스트")
    print("=" * 60)
    
    results = {
        'target_generator': False,
        'lgb_model': False,
        'predictor': False
    }
    
    # 1. 타겟 생성기 테스트
    results['target_generator'] = test_target_generator()
    
    # 2. LightGBM 모델 테스트
    lgb_success, model_path = test_lgb_model()
    results['lgb_model'] = lgb_success
    
    # 3. 예측 엔진 테스트 (모델이 성공한 경우에만)
    if lgb_success and model_path:
        results['predictor'] = test_predictor(model_path)
    
    # 결과 요약
    print("\n" + "=" * 60)
    print("📊 테스트 결과 요약:")
    
    for component, success in results.items():
        status = "✅ 성공" if success else "❌ 실패"
        print(f"   {component}: {status}")
    
    all_success = all(results.values())
    
    if all_success:
        print("\n🎉 모든 테스트 성공! LightGBM 모델 시스템이 완전히 작동합니다.")
        print("\n🎯 다음 단계:")
        print("   1. 하이퍼파라미터 최적화 (Optuna)")
        print("   2. 백테스팅 시스템 구축")
        print("   3. 성과 측정 및 검증")
    else:
        print("\n⚠️ 일부 테스트 실패. 문제를 해결한 후 다시 시도하세요.")
    
    return all_success


if __name__ == "__main__":
    test_full_workflow()
