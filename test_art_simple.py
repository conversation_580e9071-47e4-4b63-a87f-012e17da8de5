"""
Project LEVIATHAN - 간단한 ART 시스템 테스트
근본적인 문제 해결을 위한 최소한의 테스트 버전
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')


def create_sample_data(n_samples=1000):
    """샘플 데이터 생성"""
    print("📊 샘플 데이터 생성 중...")
    
    # 날짜 인덱스 생성
    dates = pd.date_range('2024-01-01', periods=n_samples, freq='15min')
    
    # 가격 데이터 생성 (랜덤 워크)
    np.random.seed(42)
    price_changes = np.random.normal(0, 0.001, n_samples)
    prices = 50000 * (1 + price_changes).cumprod()
    
    # OHLCV 데이터 생성
    data = pd.DataFrame({
        'close': prices,
        'high': prices * (1 + np.random.uniform(0, 0.01, n_samples)),
        'low': prices * (1 - np.random.uniform(0, 0.01, n_samples)),
        'volume': np.random.randint(100000, 1000000, n_samples)
    }, index=dates)
    
    data['open'] = data['close'].shift(1).fillna(data['close'])
    
    print(f"✅ 샘플 데이터 생성 완료: {data.shape}")
    return data


def create_sample_features(data):
    """간단한 피처 생성"""
    print("🧠 간단한 피처 생성 중...")
    
    features = pd.DataFrame(index=data.index)
    
    # 이동평균
    features['SMA_20_exec'] = data['close'].rolling(20).mean()
    features['SMA_50_trend'] = data['close'].rolling(50).mean()
    
    # RSI
    delta = data['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
    rs = gain / loss
    features['RSI_14_exec'] = 100 - (100 / (1 + rs))
    
    # 볼린저 밴드
    bb_period = 20
    bb_std = 2
    sma = data['close'].rolling(bb_period).mean()
    std = data['close'].rolling(bb_period).std()
    features['BB_upper_exec'] = sma + (std * bb_std)
    features['BB_lower_exec'] = sma - (std * bb_std)
    features['BB_position_exec'] = (data['close'] - features['BB_lower_exec']) / (features['BB_upper_exec'] - features['BB_lower_exec'])
    
    # 거래량 비율
    features['Volume_MA_exec'] = data['volume'].rolling(20).mean()
    features['Volume_ratio_exec'] = data['volume'] / features['Volume_MA_exec']
    
    # 결측값 제거
    features = features.fillna(0)
    
    print(f"✅ 피처 생성 완료: {features.shape}")
    return features


def create_sample_ml_signals(features):
    """간단한 ML 신호 생성"""
    print("🤖 ML 신호 생성 중...")
    
    signals = pd.DataFrame(index=features.index)
    
    # 간단한 규칙 기반 신호 (ML 대신)
    rsi = features['RSI_14_exec']
    bb_pos = features['BB_position_exec']
    vol_ratio = features['Volume_ratio_exec']
    
    # 매수 신호 조건
    buy_condition = (
        (rsi < 40) &  # RSI 과매도
        (bb_pos < 0.2) &  # 볼린저 밴드 하단
        (vol_ratio > 1.2)  # 거래량 증가
    )
    
    # 신호 생성
    signals['confidence'] = np.where(buy_condition, 
                                   np.random.uniform(0.6, 0.9, len(features)),  # 매수 신호
                                   np.random.uniform(0.1, 0.4, len(features)))  # 보유 신호
    
    print(f"✅ ML 신호 생성 완료: {signals.shape}")
    return signals


class SimpleMarketAnalyzer:
    """간단한 시장 분석기"""
    
    def analyze_market(self, data):
        print("🔍 간단한 시장 분석 중...")
        
        # 변동성 계산
        returns = data['close'].pct_change()
        volatility = returns.rolling(96).std() * np.sqrt(96)
        
        # 추세 강도
        price_change = data['close'].pct_change(96)
        
        # 시장 상황 분류
        market_condition = pd.Series('NORMAL', index=data.index)
        market_condition[volatility > 0.05] = 'HIGH_RISK'
        market_condition[price_change < -0.02] = 'BEAR_MARKET'
        market_condition[volatility < 0.015] = 'LOW_VOLATILITY'
        
        result = pd.DataFrame({
            'volatility': volatility.fillna(0.03),
            'trend_strength': price_change.fillna(0),
            'market_condition': market_condition
        }, index=data.index)
        
        print(f"✅ 시장 분석 완료: {result.shape}")
        return result


class SimpleDynamicRiskManager:
    """간단한 동적 리스크 관리자"""
    
    def get_dynamic_settings(self, volatility, market_condition, confidence):
        """동적 설정 계산"""
        
        # 기본 설정
        base_leverage = 4.0
        base_position = 0.5
        
        # 시장 상황별 조정
        if market_condition == 'HIGH_RISK':
            leverage = 2.0
            position_size = 0.3
            take_profit = 0.08
        elif market_condition == 'LOW_VOLATILITY':
            leverage = 8.0
            position_size = 1.0
            take_profit = 0.12
        else:
            leverage = base_leverage
            position_size = base_position
            take_profit = 0.10
        
        # 확신도 기반 조정
        position_size *= confidence
        
        return {
            'leverage': leverage,
            'position_size_pct': min(position_size, 1.0),
            'stop_loss_pct': 0.05,  # 고정 5%
            'take_profit_pct': take_profit
        }


class SimpleARTBacktester:
    """간단한 ART 백테스터"""
    
    def __init__(self, initial_capital=10000):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.trades = []
        self.current_position = None
        
        self.market_analyzer = SimpleMarketAnalyzer()
        self.risk_manager = SimpleDynamicRiskManager()
        
        print(f"🎯 간단한 ART 백테스터 초기화: ${initial_capital:,}")
    
    def run_simple_backtest(self):
        """간단한 백테스팅 실행"""
        print("🚀 간단한 ART 백테스팅 시작...")
        
        # 1. 샘플 데이터 생성
        data = create_sample_data(1000)
        
        # 2. 피처 생성
        features = create_sample_features(data)
        
        # 3. ML 신호 생성
        signals = create_sample_ml_signals(features)
        
        # 4. 시장 분석
        market_analysis = self.market_analyzer.analyze_market(data)
        
        # 5. 백테스팅 실행
        for i, (timestamp, row) in enumerate(data.iterrows()):
            if i < 100:  # 초기 데이터 건너뛰기
                continue
                
            # 현재 정보
            current_price = row['close']
            confidence = signals.loc[timestamp, 'confidence']
            volatility = market_analysis.loc[timestamp, 'volatility']
            market_condition = market_analysis.loc[timestamp, 'market_condition']
            
            # 동적 설정 계산
            settings = self.risk_manager.get_dynamic_settings(
                volatility, market_condition, confidence)
            
            # 거래 로직
            if self.current_position is None and confidence > 0.6:
                # 진입
                self._open_position(timestamp, current_price, settings)
            elif self.current_position is not None:
                # 청산 조건 확인
                exit_reason = self._check_exit_conditions(current_price, settings)
                if exit_reason:
                    self._close_position(timestamp, current_price, exit_reason)
        
        # 결과 분석
        return self._analyze_results()
    
    def _open_position(self, timestamp, price, settings):
        """포지션 진입"""
        position_size = self.current_capital * settings['position_size_pct'] * settings['leverage']
        
        self.current_position = {
            'entry_time': timestamp,
            'entry_price': price,
            'position_size': position_size,
            'settings': settings
        }
        
        print(f"📈 진입: {timestamp} @ ${price:,.0f} (크기: ${position_size:,.0f})")
    
    def _close_position(self, timestamp, price, reason):
        """포지션 청산"""
        if not self.current_position:
            return
        
        entry_price = self.current_position['entry_price']
        position_size = self.current_position['position_size']
        leverage = self.current_position['settings']['leverage']
        
        # 수익률 계산
        price_change = (price - entry_price) / entry_price
        leveraged_return = price_change * leverage
        pnl = self.current_capital * 0.1 * leveraged_return  # 간단한 계산
        
        self.current_capital += pnl
        
        trade = {
            'entry_time': self.current_position['entry_time'],
            'exit_time': timestamp,
            'entry_price': entry_price,
            'exit_price': price,
            'pnl': pnl,
            'exit_reason': reason
        }
        
        self.trades.append(trade)
        print(f"📉 청산: {timestamp} @ ${price:,.0f} ({reason}) PnL: ${pnl:+,.0f}")
        
        self.current_position = None
    
    def _check_exit_conditions(self, current_price, settings):
        """청산 조건 확인"""
        entry_price = self.current_position['entry_price']
        price_change = (current_price - entry_price) / entry_price
        
        if price_change <= -settings['stop_loss_pct']:
            return 'stop_loss'
        elif price_change >= settings['take_profit_pct']:
            return 'take_profit'
        
        return None
    
    def _analyze_results(self):
        """결과 분석"""
        if not self.trades:
            return {'error': '거래 없음'}
        
        total_return = (self.current_capital - self.initial_capital) / self.initial_capital * 100
        winning_trades = len([t for t in self.trades if t['pnl'] > 0])
        win_rate = winning_trades / len(self.trades) * 100
        
        return {
            'total_return': total_return,
            'total_trades': len(self.trades),
            'win_rate': win_rate,
            'final_capital': self.current_capital
        }


def main():
    """메인 실행 함수"""
    print("🎯 간단한 ART 시스템 테스트")
    print("=" * 50)
    
    try:
        # 백테스터 실행
        backtester = SimpleARTBacktester()
        results = backtester.run_simple_backtest()
        
        # 결과 출력
        if 'error' not in results:
            print(f"\n✅ 테스트 완료!")
            print(f"📈 총 수익률: {results['total_return']:+.2f}%")
            print(f"📊 총 거래: {results['total_trades']}회")
            print(f"🎯 승률: {results['win_rate']:.1f}%")
            print(f"💰 최종 자본: ${results['final_capital']:,.0f}")
        else:
            print(f"❌ {results['error']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 오류 발생: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 간단한 ART 시스템 테스트 성공!")
    else:
        print("\n💥 테스트 실패!")
