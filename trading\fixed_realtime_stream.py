#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📡 수정된 실시간 데이터 스트림

WebSocket SSL 문제 해결된 버전
"""

import websocket
import json
import threading
import time
import pandas as pd
from datetime import datetime
from typing import Dict, Callable, Optional
import sys
from pathlib import Path
from collections import deque

# 프로젝트 루트 추가
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class FixedRealTimeDataStream:
    """수정된 실시간 데이터 스트림"""
    
    def __init__(self, symbol: str = "BTCUSDT", callback: Callable = None):
        self.symbol = symbol.lower()
        self.callback = callback
        
        # 작동하는 WebSocket 설정
        self.ws_url = f"wss://stream.binance.com:9443/ws/{self.symbol}@kline_15m"
        self.ws = None
        self.is_running = False
        
        # 데이터 버퍼
        self.kline_buffer = deque(maxlen=100)
        self.last_update = None
        
        print(f"📡 수정된 실시간 데이터 스트림 초기화: {symbol}")
    
    def on_message(self, ws, message):
        """WebSocket 메시지 처리"""
        try:
            data = json.loads(message)
            
            if 'k' in data:  # Kline 데이터
                kline = data['k']
                is_closed = kline['x']  # 캔들이 완료되었는지
                
                if is_closed:  # 완료된 캔들만 처리
                    candle_data = {
                        'timestamp': pd.to_datetime(kline['t'], unit='ms'),
                        'open': float(kline['o']),
                        'high': float(kline['h']),
                        'low': float(kline['l']),
                        'close': float(kline['c']),
                        'volume': float(kline['v'])
                    }
                    
                    # 버퍼에 추가
                    self.kline_buffer.append(candle_data)
                    self.last_update = datetime.now()
                    
                    # 콜백 호출
                    if self.callback:
                        self.callback("15m", candle_data)
                    
                    # 로그 출력
                    timestamp = candle_data['timestamp'].strftime('%H:%M:%S')
                    price = candle_data['close']
                    print(f"📈 [{timestamp}] 새로운 15분봉: ${price:,.2f}")
                    
        except Exception as e:
            print(f"❌ 메시지 처리 오류: {e}")
    
    def on_error(self, ws, error):
        """WebSocket 오류 처리"""
        print(f"❌ WebSocket 오류: {error}")
    
    def on_close(self, ws, close_status_code, close_msg):
        """WebSocket 연결 종료"""
        print("🔌 WebSocket 연결 종료")
        self.is_running = False
    
    def on_open(self, ws):
        """WebSocket 연결 시작"""
        print("✅ WebSocket 연결 성공")
        print(f"📊 구독: {self.symbol.upper()} 15분봉")
        self.is_running = True
    
    def start_stream(self):
        """스트리밍 시작"""
        print("🚀 실시간 데이터 스트리밍 시작...")
        
        websocket.enableTrace(False)
        
        self.ws = websocket.WebSocketApp(
            self.ws_url,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close,
            on_open=self.on_open
        )
        
        # 별도 스레드에서 실행
        self.ws_thread = threading.Thread(target=self.ws.run_forever)
        self.ws_thread.daemon = True
        self.ws_thread.start()
    
    def stop_stream(self):
        """스트리밍 중지"""
        print("⏹️ 실시간 데이터 스트리밍 중지...")
        self.is_running = False
        
        if self.ws:
            self.ws.close()
    
    def get_latest_data(self, count: int = 10) -> Optional[pd.DataFrame]:
        """최신 데이터 반환"""
        if len(self.kline_buffer) == 0:
            return None
        
        # 최근 count개 데이터를 DataFrame으로 변환
        data_list = list(self.kline_buffer)[-count:]
        df = pd.DataFrame(data_list)
        
        if len(df) > 0:
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)
        
        return df
    
    def get_status(self) -> Dict:
        """스트림 상태 반환"""
        return {
            'is_running': self.is_running,
            'symbol': self.symbol,
            'buffer_size': len(self.kline_buffer),
            'last_update': self.last_update,
            'ws_url': self.ws_url
        }
    
    def print_status(self):
        """상태 정보 출력"""
        status = self.get_status()
        
        print(f"\n📡 실시간 데이터 스트림 상태:")
        print(f"   심볼: {status['symbol'].upper()}")
        print(f"   실행 중: {'✅' if status['is_running'] else '❌'}")
        print(f"   버퍼 크기: {status['buffer_size']}개")
        print(f"   WebSocket URL: {status['ws_url']}")
        
        if status['last_update']:
            print(f"   마지막 업데이트: {status['last_update'].strftime('%H:%M:%S')}")


class RESTPollingStream:
    """REST API 폴링 방식 대안"""
    
    def __init__(self, symbol: str = "BTCUSDT", callback: Callable = None, interval: int = 15):
        self.symbol = symbol
        self.callback = callback
        self.interval = interval  # 폴링 간격 (초)
        
        self.is_running = False
        self.last_price = None
        self.poll_thread = None
        
        print(f"🔄 REST API 폴링 스트림 초기화: {symbol} ({interval}초 간격)")
    
    def _poll_data(self):
        """데이터 폴링"""
        import requests
        
        url = f"https://fapi.binance.com/fapi/v1/ticker/price?symbol={self.symbol}"
        
        while self.is_running:
            try:
                response = requests.get(url, timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    current_price = float(data['price'])
                    
                    # 가격이 변경된 경우만 콜백 호출
                    if current_price != self.last_price:
                        candle_data = {
                            'timestamp': datetime.now(),
                            'close': current_price
                        }
                        
                        if self.callback:
                            self.callback("15m", candle_data)
                        
                        print(f"💰 [{datetime.now().strftime('%H:%M:%S')}] 가격 업데이트: ${current_price:,.2f}")
                        self.last_price = current_price
                
                time.sleep(self.interval)
                
            except Exception as e:
                print(f"❌ 폴링 오류: {e}")
                time.sleep(self.interval)
    
    def start_stream(self):
        """폴링 시작"""
        print("🔄 REST API 폴링 시작...")
        
        self.is_running = True
        self.poll_thread = threading.Thread(target=self._poll_data)
        self.poll_thread.daemon = True
        self.poll_thread.start()
    
    def stop_stream(self):
        """폴링 중지"""
        print("⏹️ REST API 폴링 중지...")
        self.is_running = False
    
    def get_status(self) -> Dict:
        """상태 반환"""
        return {
            'is_running': self.is_running,
            'symbol': self.symbol,
            'interval': self.interval,
            'last_price': self.last_price
        }


def signal_callback(interval: str, candle_data: Dict):
    """신호 콜백 함수 예시"""
    timestamp = candle_data['timestamp'].strftime('%H:%M:%S')
    price = candle_data['close']
    print(f"🔔 신호 콜백: [{timestamp}] ${price:,.2f}")


def test_websocket_stream():
    """WebSocket 스트림 테스트"""
    print("🧪 WebSocket 스트림 테스트")
    print("=" * 40)
    
    stream = FixedRealTimeDataStream("BTCUSDT", signal_callback)
    
    try:
        stream.start_stream()
        
        print("⏰ 30초간 데이터 수신 테스트...")
        time.sleep(30)
        
        # 상태 확인
        stream.print_status()
        
        # 최신 데이터 확인
        latest_data = stream.get_latest_data(5)
        if latest_data is not None and len(latest_data) > 0:
            print(f"\n📊 수신된 데이터:")
            print(latest_data)
        else:
            print(f"\n⚠️ 수신된 데이터 없음")
        
        return True
        
    except KeyboardInterrupt:
        print("\n⏹️ 사용자 중단")
        return True
        
    except Exception as e:
        print(f"\n❌ WebSocket 테스트 오류: {e}")
        return False
        
    finally:
        stream.stop_stream()

def test_rest_polling():
    """REST 폴링 테스트"""
    print("\n🧪 REST API 폴링 테스트")
    print("=" * 40)
    
    stream = RESTPollingStream("BTCUSDT", signal_callback, interval=5)
    
    try:
        stream.start_stream()
        
        print("⏰ 20초간 폴링 테스트...")
        time.sleep(20)
        
        # 상태 확인
        status = stream.get_status()
        print(f"\n📊 폴링 상태:")
        for key, value in status.items():
            print(f"   {key}: {value}")
        
        return True
        
    except KeyboardInterrupt:
        print("\n⏹️ 사용자 중단")
        return True
        
    except Exception as e:
        print(f"\n❌ REST 폴링 테스트 오류: {e}")
        return False
        
    finally:
        stream.stop_stream()

def main():
    print("📡 수정된 실시간 데이터 스트림 테스트")
    print("=" * 60)
    print("🎯 목표: WebSocket 및 REST 폴링 방식 검증")
    print()
    
    # 1. WebSocket 테스트
    websocket_success = test_websocket_stream()
    
    # 2. REST 폴링 테스트
    rest_success = test_rest_polling()
    
    # 결과 요약
    print(f"\n🏁 테스트 결과:")
    print(f"   WebSocket: {'✅ 성공' if websocket_success else '❌ 실패'}")
    print(f"   REST 폴링: {'✅ 성공' if rest_success else '❌ 실패'}")
    
    if websocket_success:
        print(f"\n🎉 WebSocket 실시간 스트리밍 복구 성공!")
        print(f"✅ 실시간 신호 생성 시스템 준비 완료")
    elif rest_success:
        print(f"\n⚠️ WebSocket 실패, REST 폴링 사용 권장")
        print(f"💡 5-15초 간격으로 가격 업데이트 가능")
    else:
        print(f"\n❌ 모든 실시간 데이터 수신 방법 실패")

if __name__ == "__main__":
    main()
