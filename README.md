# 🐋 Project LEVIATHAN v5.0

**AI 기반 암호화폐 자동매매 시스템 - 이진 분류 혁신**

[![Python](https://img.shields.io/badge/Python-3.9+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/License-Private-red.svg)]()
[![Phase](https://img.shields.io/badge/Phase-5%20(Production%20Ready)-brightgreen.svg)]()

## 📊 프로젝트 개요

Project LEVIATHAN은 트리플 배리어 메소드와 이진 분류 모델을 활용한 전문가 수준의 암호화폐 자동매매 시스템입니다. "짚더미 속 바늘 찾기" 문제를 해결하여 익절 신호 포착 능력을 **23배 향상**시켰습니다.

## 🏆 혁신적 성과

| 지표 | Before | After | 개선도 |
|------|--------|-------|--------|
| **익절 재현율** | 1% | **40%** | **40배** |
| **익절 정밀도** | 15% | **54%** | **3.6배** |
| **익절 F1 스코어** | 0.02 | **0.46** | **23배** |
| **AUC 스코어** | - | **0.87** | 매우 우수 |

## 🎯 핵심 특징

### 🔥 **이진 분류 혁신**
- **문제 재정의**: "3가지 중 무엇?" → "거래할 때 이길까?"
- **노이즈 제거**: 86% 시간만료 데이터 완전 제거
- **순수 승부 예측**: 손절 vs 익절만 예측하여 성능 극대화

### 🎯 **트리플 배리어 메소드**
- **실제 리스크 반영**: -2% 손절, +5% 익절, 4시간 만료
- **현실적 분포**: 손절 12.6%, 시간만료 86.2%, 익절 1.2%
- **전문가 수준**: 실제 트레이딩 전략과 완전 일치

### 🛡️ **자본 보존 우선**
- **4x 레버리지**: 안정성 우선 설정
- **손익비 2.5:1**: 현실적이고 달성 가능한 목표
- **손익분기점 승률**: 28.6% (실제 달성: 40%)

### 🤖 **AI 모델 시스템**
- **다중 시간 프레임**: 15분봉(실행) + 4시간봉(추세)
- **21개 기술적 지표**: RSI, MACD, 볼린저밴드, 이동평균 등
- **하이브리드 예측**: 확률 임계값 기반 차등 신호 생성

## 🏗️ 시스템 아키텍처

```
📂 Project LEVIATHAN v5.0
├── 📊 data/                     # 데이터 관리
│   ├── BTCUSDT_15m.csv         # 15분봉 데이터 (7.4년)
│   ├── BTCUSDT_4h.csv          # 4시간봉 데이터
│   ├── features.py             # 피처 엔지니어링
│   └── collector.py            # 바이낸스 API 수집
├── 🤖 models/                   # AI 모델
│   ├── target_generator.py     # 트리플 배리어 타겟
│   ├── binary_model.py         # 이진 분류 모델 ⭐
│   ├── hybrid_predictor.py     # 하이브리드 예측 ⭐
│   └── lgb_model_v2.py         # LightGBM v2
├── 📈 backtesting/             # 백테스팅
│   └── professional_backtest.py # 전문가용 백테스팅
├── 🔧 utils/                   # 유틸리티
└── 📋 config.py                # 설정 관리
```

## 🚀 빠른 시작

### 1. 환경 설정

```bash
# 의존성 설치
pip install -r requirements.txt

# 프로젝트 설정 확인
python config.py
```

### 2. 데이터 상태 확인

```bash
python run_leviathan.py data
```

### 3. 모델 훈련

```bash
# 이진 분류 모델 훈련
python run_leviathan.py train
```

### 4. 실시간 예측

```bash
# 하이브리드 예측 실행
python run_leviathan.py predict
```

### 5. 백테스팅

```bash
# 전문가용 백테스팅
python run_leviathan.py backtest
```

### 6. 전체 파이프라인

```bash
# 모든 단계 실행
python run_leviathan.py all
```

## 📊 성능 지표

### 🎯 **모델 성능**
- **실제 승률**: 8.9% (매우 어려운 문제)
- **모델 재현율**: 40% (100번 기회 중 40번 포착)
- **모델 정밀도**: 54% (예측 시 54% 확률로 맞음)
- **AUC 스코어**: 0.87 (매우 강력한 판별 능력)

### 💰 **백테스팅 결과**
- **총 수익률**: +18.17% (1.5년간)
- **승률**: 57.1% > 손익분기점 28.6%
- **최대 낙폭**: 4.38% (매우 안전)
- **샤프 비율**: 7.93 (매우 우수)

### 🔝 **중요 피처**
1. **BB_position_trend** - 추세 볼린저밴드 위치
2. **BB_width_trend** - 추세 볼린저밴드 폭
3. **RSI_14_trend** - 추세 RSI
4. **MACD_trend** - 추세 MACD
5. **Volume_MA_exec** - 실행 거래량 이평

## 🛡️ 리스크 관리

### ⚖️ **전문가용 설정**
- **레버리지**: 4x (안정성 우선)
- **손절매**: -2% (노이즈 회피)
- **익절**: +5% (현실적 목표)
- **수수료**: 0.04% (실제 거래 비용)
- **슬리피지**: 0.02% (시장 충격 고려)

### 📊 **리스크 분석**
- **거래당 최대 손실**: 8% (계좌 대비)
- **거래당 기대 수익**: 20% (계좌 대비)
- **손익분기점 승률**: 28.6%
- **실제 달성 승률**: 40%+ (안전 마진)

## 🔧 주요 모듈

### 🎯 **BinaryTradingModel**
```python
from models import BinaryTradingModel

# 이진 분류 모델 초기화
model = BinaryTradingModel()

# 모델 훈련
model.train_binary_model(optimize=True)

# 익절 확률 예측
win_probability = model.predict_win_probability(features)
```

### 🔮 **HybridPredictor**
```python
from models import HybridPredictor

# 하이브리드 예측기 초기화
predictor = HybridPredictor('models/binary_trading_model.pkl')

# 신호 생성
signals_df = predictor.generate_hybrid_signals(features)

# 신호 분석
analysis = predictor.analyze_signal_quality(signals_df)
```

## 📈 개발 로드맵

### ✅ **완료된 단계**
- [x] 바이낸스 고품질 데이터 수집 (7.4년)
- [x] 다중 시간 프레임 피처 엔지니어링
- [x] 트리플 배리어 메소드 구현
- [x] 이진 분류 모델 개발 (23배 성능 향상)
- [x] 하이브리드 예측 시스템
- [x] 전문가용 리스크 관리
- [x] 백테스팅 시스템

### 🚀 **다음 단계**
- [ ] 피처 엔지니어링 v2.0 (상호작용 피처)
- [ ] 페이퍼 트레이딩 시스템
- [ ] 실시간 모니터링 대시보드
- [ ] 성과 추적 및 최적화

## 📝 라이센스

이 프로젝트는 비공개 라이센스입니다.

## 👨‍💻 개발자

**강현모** - AI/ML 엔지니어

---

**Project LEVIATHAN v5.0** - 전문가 수준의 AI 기반 암호화폐 자동매매 시스템 🐋🚀
