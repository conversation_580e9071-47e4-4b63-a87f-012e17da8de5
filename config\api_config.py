#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔑 Project LEVIATHAN - API 설정 관리

Binance Testnet API 키 및 설정 관리
보안을 위해 실제 API 키는 환경변수나 별도 파일에서 로드

Author: 강현모
Date: 2024-12-15
Version: 1.0
"""

import os
from typing import Dict, Optional

class APIConfig:
    """API 설정 관리 클래스"""
    
    def __init__(self):
        self.testnet_config = {
            # Binance Testnet 설정
            "base_url": "https://testnet.binancefuture.com",
            "ws_url": "wss://stream.binancefuture.com",
            "api_key": self._get_api_key(),
            "secret_key": self._get_secret_key(),
            
            # 거래 설정
            "symbol": "BTCUSDT",
            "timeframes": {
                "15m": "15m",
                "4h": "4h"
            },
            
            # 제한 설정
            "rate_limit": {
                "requests_per_minute": 1200,
                "orders_per_second": 10,
                "orders_per_day": 200000
            }
        }
        
        self.trading_config = {
            # 거래 파라미터
            "min_order_size": 0.001,  # 최소 주문 크기 (BTC)
            "max_order_size": 1.0,    # 최대 주문 크기 (BTC)
            "precision": 3,           # 소수점 자릿수
            
            # 타임아웃 설정
            "connection_timeout": 10,
            "read_timeout": 30,
            "order_timeout": 5,
            
            # 재시도 설정
            "max_retries": 3,
            "retry_delay": 1.0
        }
    
    def _get_api_key(self) -> Optional[str]:
        """API 키 로드 (환경변수 또는 파일에서)"""
        # 1. 환경변수에서 시도
        api_key = os.getenv('BINANCE_TESTNET_API_KEY')
        if api_key:
            return api_key
        
        # 2. 별도 파일에서 시도 (Git에 업로드되지 않는 파일)
        try:
            with open('config/api_keys.txt', 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for line in lines:
                    if line.startswith('API_KEY='):
                        return line.split('=')[1].strip()
        except FileNotFoundError:
            pass
        
        # 3. 기본값 (개발 시 임시로 여기에 입력 가능)
        return "YOUR_API_KEY_HERE"  # 실제 키로 교체 필요
    
    def _get_secret_key(self) -> Optional[str]:
        """Secret 키 로드 (환경변수 또는 파일에서)"""
        # 1. 환경변수에서 시도
        secret_key = os.getenv('BINANCE_TESTNET_SECRET_KEY')
        if secret_key:
            return secret_key
        
        # 2. 별도 파일에서 시도
        try:
            with open('config/api_keys.txt', 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for line in lines:
                    if line.startswith('SECRET_KEY='):
                        return line.split('=')[1].strip()
        except FileNotFoundError:
            pass
        
        # 3. 기본값 (개발 시 임시로 여기에 입력 가능)
        return "YOUR_SECRET_KEY_HERE"  # 실제 키로 교체 필요
    
    def get_testnet_config(self) -> Dict:
        """Testnet 설정 반환"""
        return self.testnet_config.copy()
    
    def get_trading_config(self) -> Dict:
        """거래 설정 반환"""
        return self.trading_config.copy()
    
    def validate_config(self) -> bool:
        """설정 유효성 검증"""
        api_key = self.testnet_config["api_key"]
        secret_key = self.testnet_config["secret_key"]
        
        if not api_key or api_key == "YOUR_API_KEY_HERE":
            print("❌ API 키가 설정되지 않았습니다!")
            return False
        
        if not secret_key or secret_key == "YOUR_SECRET_KEY_HERE":
            print("❌ Secret 키가 설정되지 않았습니다!")
            return False
        
        if len(api_key) < 50:
            print("❌ API 키 형식이 올바르지 않습니다!")
            return False
        
        if len(secret_key) < 50:
            print("❌ Secret 키 형식이 올바르지 않습니다!")
            return False
        
        print("✅ API 설정이 유효합니다!")
        return True
    
    def print_config_status(self):
        """설정 상태 출력"""
        print("🔑 API 설정 상태:")
        print(f"   Base URL: {self.testnet_config['base_url']}")
        print(f"   WebSocket URL: {self.testnet_config['ws_url']}")
        print(f"   Symbol: {self.testnet_config['symbol']}")
        print(f"   API Key: {'✅ 설정됨' if self.testnet_config['api_key'] != 'YOUR_API_KEY_HERE' else '❌ 미설정'}")
        print(f"   Secret Key: {'✅ 설정됨' if self.testnet_config['secret_key'] != 'YOUR_SECRET_KEY_HERE' else '❌ 미설정'}")


# 전역 설정 인스턴스
api_config = APIConfig()


if __name__ == "__main__":
    # 설정 테스트
    config = APIConfig()
    config.print_config_status()
    
    if config.validate_config():
        print("\n🚀 API 연동 준비 완료!")
    else:
        print("\n⚠️ API 키를 먼저 설정해주세요.")
        print("\n📝 설정 방법:")
        print("1. config/api_keys.txt 파일 생성")
        print("2. 다음 형식으로 키 입력:")
        print("   API_KEY=your_actual_api_key")
        print("   SECRET_KEY=your_actual_secret_key")
        print("3. 또는 환경변수 설정:")
        print("   BINANCE_TESTNET_API_KEY=your_api_key")
        print("   BINANCE_TESTNET_SECRET_KEY=your_secret_key")
