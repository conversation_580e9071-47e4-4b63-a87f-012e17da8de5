#!/usr/bin/env python3
"""
🧠 Project LEVIATHAN - 상호작용 피처 생성기
다양한 기술적 지표들 간의 상호작용을 통해 모델의 예측 성능을 향상시키는 피처들을 생성합니다.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple


class InteractionFeatureGenerator:
    """상호작용 피처 생성기"""
    
    def __init__(self):
        self.feature_names = []
        
    def generate_all_interaction_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """모든 상호작용 피처 생성"""
        print(f"🧠 상호작용 피처 생성 중...")
        
        result_df = df.copy()
        initial_features = len(result_df.columns)
        
        # 1. 추세-모멘텀 정렬 피처
        result_df = self._add_trend_momentum_alignment(result_df)
        
        # 2. 거래량-가격 모멘텀 피처
        result_df = self._add_volume_price_momentum(result_df)
        
        # 3. 변동성-추세 필터 피처
        result_df = self._add_volatility_trend_filter(result_df)
        
        # 4. RSI 다이버전스 피처
        result_df = self._add_rsi_divergence_features(result_df)
        
        # 5. 볼린저 밴드 상호작용 피처
        result_df = self._add_bollinger_interaction_features(result_df)
        
        final_features = len(result_df.columns)
        added_features = final_features - initial_features
        
        print(f"   ✅ 상호작용 피처 {added_features}개 추가 완료")
        
        return result_df
    
    def _add_trend_momentum_alignment(self, df: pd.DataFrame) -> pd.DataFrame:
        """추세-모멘텀 정렬 피처"""
        try:
            # 추세와 모멘텀이 같은 방향인지 확인
            if 'SMA_50_trend' in df.columns and 'SMA_20_exec' in df.columns:
                df['Trend_Momentum_Alignment'] = (
                    (df['SMA_20_exec'] > df['SMA_50_trend']).astype(int)
                )
                self.feature_names.append('Trend_Momentum_Alignment')
            
            # RSI 추세 정렬
            if 'RSI_14_trend' in df.columns and 'RSI_14_exec' in df.columns:
                df['RSI_Trend_Alignment'] = (
                    ((df['RSI_14_trend'] > 50) & (df['RSI_14_exec'] > 50)) |
                    ((df['RSI_14_trend'] < 50) & (df['RSI_14_exec'] < 50))
                ).astype(int)
                self.feature_names.append('RSI_Trend_Alignment')
                
        except Exception as e:
            print(f"   ⚠️ 추세-모멘텀 정렬 피처 생성 실패: {e}")
        
        return df
    
    def _add_volume_price_momentum(self, df: pd.DataFrame) -> pd.DataFrame:
        """거래량-가격 모멘텀 피처"""
        try:
            # 거래량과 가격 모멘텀의 상호작용
            if 'Volume_ratio_exec' in df.columns and 'RSI_14_exec' in df.columns:
                df['Volume_Price_Momentum'] = (
                    df['Volume_ratio_exec'] * (df['RSI_14_exec'] - 50) / 50
                )
                self.feature_names.append('Volume_Price_Momentum')
            
            # 거래량 급증과 가격 돌파
            if 'Volume_ratio_exec' in df.columns and 'BB_position_exec' in df.columns:
                df['Volume_Breakout_Signal'] = (
                    (df['Volume_ratio_exec'] > 1.5) & 
                    ((df['BB_position_exec'] > 0.8) | (df['BB_position_exec'] < 0.2))
                ).astype(int)
                self.feature_names.append('Volume_Breakout_Signal')
                
        except Exception as e:
            print(f"   ⚠️ 거래량-가격 모멘텀 피처 생성 실패: {e}")
        
        return df
    
    def _add_volatility_trend_filter(self, df: pd.DataFrame) -> pd.DataFrame:
        """변동성-추세 필터 피처"""
        try:
            # 변동성과 추세 강도의 조합
            if 'BB_width_exec' in df.columns and 'MACD_histogram_exec' in df.columns:
                df['Volatility_Trend_Filter'] = (
                    df['BB_width_exec'] * abs(df['MACD_histogram_exec'])
                )
                self.feature_names.append('Volatility_Trend_Filter')
            
            # 저변동성 환경에서의 추세 신호
            if 'BB_width_exec' in df.columns and 'SMA_20_exec' in df.columns:
                bb_width_ma = df['BB_width_exec'].rolling(20).mean()
                df['Low_Vol_Trend_Signal'] = (
                    (df['BB_width_exec'] < bb_width_ma * 0.8) & 
                    (abs(df['SMA_20_exec'].pct_change(5)) > 0.01)
                ).astype(int)
                self.feature_names.append('Low_Vol_Trend_Signal')
                
        except Exception as e:
            print(f"   ⚠️ 변동성-추세 필터 피처 생성 실패: {e}")
        
        return df
    
    def _add_rsi_divergence_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """RSI 다이버전스 피처"""
        try:
            if 'RSI_14_exec' in df.columns:
                # RSI 다이버전스 (가격과 RSI의 방향성 차이)
                price_change = df.index.to_series().diff().dt.total_seconds() / 3600  # 시간 차이
                if len(df) > 10:
                    price_slope = df['close'].rolling(10).apply(
                        lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) == 10 else 0
                    ) if 'close' in df.columns else 0
                    
                    rsi_slope = df['RSI_14_exec'].rolling(10).apply(
                        lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) == 10 else 0
                    )
                    
                    # 다이버전스 신호 (가격과 RSI가 반대 방향)
                    df['RSI_Divergence_Signal'] = (
                        ((price_slope > 0) & (rsi_slope < 0)) |
                        ((price_slope < 0) & (rsi_slope > 0))
                    ).astype(int)
                    self.feature_names.append('RSI_Divergence_Signal')
                
        except Exception as e:
            print(f"   ⚠️ RSI 다이버전스 피처 생성 실패: {e}")
        
        return df
    
    def _add_bollinger_interaction_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """볼린저 밴드 상호작용 피처"""
        try:
            if 'BB_position_exec' in df.columns and 'RSI_14_exec' in df.columns:
                # 볼린저 밴드와 RSI의 조합 신호
                df['BB_RSI_Oversold'] = (
                    (df['BB_position_exec'] < 0.2) & (df['RSI_14_exec'] < 30)
                ).astype(int)
                
                df['BB_RSI_Overbought'] = (
                    (df['BB_position_exec'] > 0.8) & (df['RSI_14_exec'] > 70)
                ).astype(int)
                
                self.feature_names.extend(['BB_RSI_Oversold', 'BB_RSI_Overbought'])
            
            # 볼린저 밴드 스퀴즈 후 확장
            if 'BB_width_exec' in df.columns:
                bb_width_ma = df['BB_width_exec'].rolling(20).mean()
                bb_width_std = df['BB_width_exec'].rolling(20).std()
                
                df['BB_Squeeze_Signal'] = (
                    df['BB_width_exec'] < (bb_width_ma - bb_width_std)
                ).astype(int)
                
                df['BB_Expansion_Signal'] = (
                    df['BB_width_exec'] > (bb_width_ma + bb_width_std)
                ).astype(int)
                
                self.feature_names.extend(['BB_Squeeze_Signal', 'BB_Expansion_Signal'])
                
        except Exception as e:
            print(f"   ⚠️ 볼린저 밴드 상호작용 피처 생성 실패: {e}")
        
        return df
    
    def create_multi_timeframe_features(self, df_15m: pd.DataFrame, df_4h: pd.DataFrame) -> pd.DataFrame:
        """멀티 타임프레임 상호작용 피처 생성"""
        try:
            # 4시간봉 데이터를 15분봉에 맞춰 리샘플링
            df_4h_resampled = df_4h.resample('15T').ffill()
            
            # 공통 인덱스로 정렬
            common_index = df_15m.index.intersection(df_4h_resampled.index)
            df_15m_aligned = df_15m.loc[common_index]
            df_4h_aligned = df_4h_resampled.loc[common_index]
            
            # 멀티 타임프레임 피처 추가
            df_15m_aligned['MTF_Trend_Alignment'] = (
                (df_15m_aligned['SMA_20_exec'] > df_4h_aligned['SMA_50_trend']).astype(int)
            )
            
            return df_15m_aligned
            
        except Exception as e:
            print(f"   ⚠️ 멀티 타임프레임 피처 생성 실패: {e}")
            return df_15m


# 편의 함수
def generate_interaction_features(df: pd.DataFrame) -> pd.DataFrame:
    """상호작용 피처 생성 편의 함수"""
    generator = InteractionFeatureGenerator()
    return generator.generate_all_interaction_features(df)


if __name__ == "__main__":
    print("🧠 Project LEVIATHAN - 상호작용 피처 생성기 테스트")
    print("=" * 60)
    
    # 테스트용 더미 데이터 생성
    test_data = {
        'SMA_20_exec': np.random.randn(100),
        'SMA_50_trend': np.random.randn(100),
        'RSI_14_exec': np.random.uniform(20, 80, 100),
        'RSI_14_trend': np.random.uniform(20, 80, 100),
        'BB_position_exec': np.random.uniform(0, 1, 100),
        'BB_width_exec': np.random.uniform(0.01, 0.1, 100),
        'Volume_ratio_exec': np.random.uniform(0.5, 3.0, 100),
        'MACD_histogram_exec': np.random.randn(100)
    }
    
    df_test = pd.DataFrame(test_data)
    
    generator = InteractionFeatureGenerator()
    result = generator.generate_all_interaction_features(df_test)
    
    print(f"✅ 테스트 완료!")
    print(f"   원본 피처: {len(test_data)}개")
    print(f"   최종 피처: {len(result.columns)}개")
    print(f"   추가된 피처: {len(generator.feature_names)}개")
    print(f"   피처 목록: {generator.feature_names}")
