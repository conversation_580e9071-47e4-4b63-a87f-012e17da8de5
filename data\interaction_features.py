"""
Project LEVIATHAN - 상호작용 피처 생성 모듈
모델의 지능을 강화하는 핵심 상호작용 피처 3개 구현
"""

import pandas as pd
import numpy as np
from typing import Dict, Any
import warnings
warnings.filterwarnings('ignore')


class InteractionFeatureGenerator:
    """
    상호작용 피처 생성기
    개별 지표들을 조합하여 더 정교한 시장 패턴을 포착
    """
    
    def __init__(self):
        """상호작용 피처 생성기 초기화"""
        self.feature_names = []
        print("🧠 상호작용 피처 생성기 초기화")
        print("🎯 목표: 모델의 지능 강화를 위한 복합 패턴 인식")
    
    def create_trend_momentum_alignment(self, df: pd.DataFrame) -> pd.Series:
        """
        핵심 피처 1: 추세-모멘텀 정렬도
        
        4시간 추세와 15분 모멘텀의 정렬 상태를 측정
        추세와 모멘텀이 같은 방향일 때 강한 신호 생성
        
        Args:
            df: 피처 데이터프레임
            
        Returns:
            추세-모멘텀 정렬도 (-1 ~ +1)
        """
        print("   🔄 추세-모멘텀 정렬도 생성 중...")
        
        # 4시간 추세 방향 (SMA 기울기 기반)
        sma_trend = df['SMA_50_trend'].fillna(0)
        trend_direction = np.where(sma_trend > sma_trend.shift(1), 1, -1)
        
        # 15분 모멘텀 강도 (RSI 기반, -1 ~ +1 정규화)
        rsi_exec = df['RSI_14_exec'].fillna(50)
        momentum_strength = (rsi_exec - 50) / 50
        
        # 추세와 모멘텀이 같은 방향일 때 강한 신호
        # 양수: 상승추세 + 상승모멘텀 또는 하락추세 + 하락모멘텀
        # 음수: 추세와 모멘텀이 반대 방향 (다이버전스)
        alignment = trend_direction * momentum_strength
        
        # 극값 제한 (-1 ~ +1)
        alignment = np.clip(alignment, -1, 1)
        
        print(f"      ✅ 추세-모멘텀 정렬도 범위: {alignment.min():.3f} ~ {alignment.max():.3f}")
        
        return pd.Series(alignment, index=df.index, name='Trend_Momentum_Alignment')
    
    def create_volume_price_momentum(self, df: pd.DataFrame) -> pd.Series:
        """
        핵심 피처 2: 거래량-가격 모멘텀
        
        거래량과 가격 움직임의 결합 신호
        거래량이 많으면서 가격이 움직일 때 강한 신호
        
        Args:
            df: 피처 데이터프레임
            
        Returns:
            거래량-가격 모멘텀 신호
        """
        print("   📊 거래량-가격 모멘텀 생성 중...")
        
        # 가격 변화율 (15분봉 기준)
        close_prices = df.index.to_series().apply(lambda x: df.loc[x, 'close'] if 'close' in df.columns else 0)
        if close_prices.sum() == 0:  # close 컬럼이 없는 경우 대체 방법
            # SMA를 가격 대용으로 사용
            price_proxy = df['SMA_20_exec'].fillna(method='ffill')
            price_change = price_proxy.pct_change().fillna(0)
        else:
            price_change = close_prices.pct_change().fillna(0)
        
        # 거래량 비율 (평균 대비)
        volume_ma = df['Volume_MA_exec'].fillna(1)
        volume_baseline = volume_ma.rolling(window=20, min_periods=1).mean()
        volume_ratio = volume_ma / volume_baseline
        
        # 거래량이 많으면서 가격이 움직일 때 강한 신호
        # 양수: 거래량 증가 + 가격 상승
        # 음수: 거래량 증가 + 가격 하락
        volume_price_signal = price_change * volume_ratio
        
        # 이상치 제거 (99% 분위수 기준)
        upper_bound = volume_price_signal.quantile(0.99)
        lower_bound = volume_price_signal.quantile(0.01)
        volume_price_signal = np.clip(volume_price_signal, lower_bound, upper_bound)
        
        print(f"      ✅ 거래량-가격 모멘텀 범위: {volume_price_signal.min():.6f} ~ {volume_price_signal.max():.6f}")
        
        return pd.Series(volume_price_signal, index=df.index, name='Volume_Price_Momentum')
    
    def create_volatility_trend_filter(self, df: pd.DataFrame) -> pd.Series:
        """
        핵심 피처 3: 변동성 조정 추세 신호
        
        변동성 환경에 따른 추세 신호 조정
        낮은 변동성에서 강한 추세 = 신뢰도 높은 신호
        
        Args:
            df: 피처 데이터프레임
            
        Returns:
            변동성 조정 추세 신호
        """
        print("   🌊 변동성 조정 추세 신호 생성 중...")
        
        # 볼린저밴드 폭 (변동성 측정)
        bb_width = df['BB_width_exec'].fillna(0.01)  # 0으로 나누기 방지
        
        # 가격 대비 정규화된 변동성
        sma_price = df['SMA_20_exec'].fillna(1)
        normalized_volatility = bb_width / sma_price
        
        # 추세 강도 (MACD 절댓값)
        macd_trend = df['MACD_trend'].fillna(0)
        trend_strength = abs(macd_trend)
        
        # 낮은 변동성에서 강한 추세 = 높은 신뢰도
        # 높은 변동성에서는 추세 신호를 할인
        volatility_adjusted_trend = trend_strength / (normalized_volatility + 0.001)
        
        # 이상치 제거 및 정규화
        upper_bound = volatility_adjusted_trend.quantile(0.95)
        volatility_adjusted_trend = np.clip(volatility_adjusted_trend, 0, upper_bound)
        
        # 0-1 범위로 정규화
        if volatility_adjusted_trend.max() > 0:
            volatility_adjusted_trend = volatility_adjusted_trend / volatility_adjusted_trend.max()
        
        print(f"      ✅ 변동성 조정 추세 범위: {volatility_adjusted_trend.min():.3f} ~ {volatility_adjusted_trend.max():.3f}")
        
        return pd.Series(volatility_adjusted_trend, index=df.index, name='Volatility_Trend_Filter')
    
    def generate_all_interaction_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        최적화된 상호작용 피처 생성 (성능 검증된 1개만)

        Args:
            df: 기존 피처 데이터프레임

        Returns:
            상호작용 피처가 추가된 데이터프레임
        """
        print(f"\n🧠 최적화된 상호작용 피처 생성 시작...")
        print(f"📊 입력 데이터: {df.shape[0]}행 × {df.shape[1]}열")
        print(f"🎯 전략: 성능 검증된 1개 피처만 적용 (모델 효율성 향상)")

        # 결과 데이터프레임 복사
        result_df = df.copy()

        # ✅ 성능 검증된 피처만 생성
        # 변동성 조정 추세 신호 (중요도 10위, 200점)
        volatility_trend = self.create_volatility_trend_filter(df)
        result_df['Volatility_Trend_Filter'] = volatility_trend

        # ❌ 제거된 피처들 (성능 미흡)
        # - Trend_Momentum_Alignment (11위 이하)
        # - Volume_Price_Momentum (11위 이하)

        # 피처 이름 저장 (1개만)
        self.feature_names = ['Volatility_Trend_Filter']

        print(f"\n✅ 최적화된 상호작용 피처 생성 완료!")
        print(f"📈 최종 데이터: {result_df.shape[0]}행 × {result_df.shape[1]}열")
        print(f"🎯 추가된 피처: {len(self.feature_names)}개 (성능 검증됨)")
        print(f"🗑️ 제거된 피처: 2개 (성능 미흡)")

        # 피처 통계 출력
        self.print_feature_statistics(result_df)

        return result_df
    
    def print_feature_statistics(self, df: pd.DataFrame) -> None:
        """
        생성된 상호작용 피처의 통계 정보 출력
        
        Args:
            df: 피처가 포함된 데이터프레임
        """
        print(f"\n📊 상호작용 피처 통계:")
        print("=" * 60)
        
        for feature_name in self.feature_names:
            if feature_name in df.columns:
                feature_data = df[feature_name]
                print(f"🔹 {feature_name}:")
                print(f"   평균: {feature_data.mean():.4f}")
                print(f"   표준편차: {feature_data.std():.4f}")
                print(f"   최솟값: {feature_data.min():.4f}")
                print(f"   최댓값: {feature_data.max():.4f}")
                print(f"   결측값: {feature_data.isna().sum()}개")
                print()


def test_interaction_features():
    """
    상호작용 피처 생성 테스트
    """
    print("🧪 상호작용 피처 생성 테스트")
    print("=" * 50)
    
    try:
        # 기존 피처 로드
        import sys
        from pathlib import Path
        project_root = Path(__file__).parent.parent
        sys.path.insert(0, str(project_root))

        from data.features import generate_features_from_csv
        
        print("📂 기존 피처 데이터 로드 중...")
        features_df = generate_features_from_csv()
        
        print(f"✅ 기존 피처 로드 완료: {features_df.shape}")
        
        # 상호작용 피처 생성기 초기화
        generator = InteractionFeatureGenerator()
        
        # 상호작용 피처 생성
        enhanced_features = generator.generate_all_interaction_features(features_df)
        
        print(f"\n🎉 상호작용 피처 테스트 완료!")
        print(f"📊 기존 피처: {features_df.shape[1]}개")
        print(f"📈 최종 피처: {enhanced_features.shape[1]}개")
        print(f"🆕 추가된 피처: {len(generator.feature_names)}개")
        
        return enhanced_features
        
    except Exception as e:
        print(f"❌ 테스트 중 오류 발생: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    # 상호작용 피처 생성 테스트 실행
    enhanced_features = test_interaction_features()
