"""
Project LEVIATHAN - 바이낸스 API 고품질 데이터 수집기
바이낸스 거래소의 공식 API를 사용하여 최고 품질의 암호화폐 데이터를 수집합니다.
15분봉부터 1분봉까지 원하는 기간만큼 제한 없이 데이터를 다운로드할 수 있습니다.
"""

from binance.client import Client
import pandas as pd
import os
import sys
from pathlib import Path
from datetime import datetime, timedelta
import time

# 프로젝트 루트를 Python 경로에 추가
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 새로운 설정 import
try:
    from config import TICKER, START_DATE, TIMEFRAMES, DATA_PATHS, BINANCE_CONFIG
except ImportError:
    # 기본 설정 (호환성)
    TICKER = "BTCUSDT"
    START_DATE = "2018-01-01"
    TIMEFRAMES = {
        'EXECUTION': '15m',
        'TREND': '4h',
        'DAILY': '1d'  # 일봉 추가
    }
    DATA_PATHS = {
        '15m': 'data/BTCUSDT_15m.csv',
        '4h': 'data/BTCUSDT_4h.csv',
        '1d': 'data/BTCUSDT_1D.csv'  # 일봉 추가
    }


def get_binance_klines(symbol, interval, start_date, end_date=None):
    """
    바이낸스 API를 사용하여 K라인(캔들스틱) 데이터를 가져옵니다.

    Args:
        symbol (str): 바이낸스 심볼 (예: 'BTCUSDT')
        interval (str): 시간 간격 (예: '15m', '4h', '1d')
        start_date (str): 시작 날짜 (YYYY-MM-DD)
        end_date (str): 종료 날짜 (YYYY-MM-DD), None이면 현재까지

    Returns:
        pd.DataFrame: OHLCV 데이터프레임
    """
    print(f"📡 바이낸스에서 {symbol} {interval} 데이터를 다운로드 중...")

    try:
        # 바이낸스 클라이언트 생성 (API 키 없이도 공개 데이터 접근 가능)
        client = Client()

        # 날짜 문자열을 밀리초 타임스탬프로 변환
        start_ts = int(datetime.strptime(start_date, '%Y-%m-%d').timestamp() * 1000)
        end_ts = int(datetime.now().timestamp() * 1000) if end_date is None else int(datetime.strptime(end_date, '%Y-%m-%d').timestamp() * 1000)

        all_klines = []
        current_start = start_ts

        print(f"   📅 기간: {start_date} ~ {end_date or '현재'}")

        # 바이낸스 API는 한 번에 최대 1000개 레코드만 반환하므로 여러 번 요청
        while current_start < end_ts:
            print(f"   � {datetime.fromtimestamp(current_start/1000).strftime('%Y-%m-%d')} 부터 데이터 수집 중...")

            # K라인 데이터 요청
            klines = client.get_historical_klines(
                symbol=symbol,
                interval=interval,
                start_str=current_start,
                end_str=end_ts,
                limit=1000
            )

            if not klines:
                break

            all_klines.extend(klines)

            # 다음 요청을 위해 시작 시간 업데이트
            current_start = klines[-1][0] + 1

            # API 제한을 피하기 위한 짧은 대기
            time.sleep(0.1)

            print(f"   📊 {len(klines)}개 레코드 수집 완료 (총 {len(all_klines)}개)")

        # 데이터프레임으로 변환
        df = pd.DataFrame(all_klines, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
        ])

        # 필요한 컬럼만 선택하고 데이터 타입 변환
        df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume']].copy()
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df.set_index('timestamp', inplace=True)

        # 숫자 컬럼을 float로 변환
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = pd.to_numeric(df[col], errors='coerce')

        print(f"✅ 바이낸스 데이터 수집 완료: {len(df)}개 레코드")
        print(f"   📅 실제 기간: {df.index[0]} ~ {df.index[-1]}")

        return df

    except Exception as e:
        print(f"❌ 바이낸스 데이터 수집 실패: {e}")
        return None


def download_binance_data_to_csv(symbol, start_date, interval, output_path):
    """
    바이낸스 데이터를 다운로드하여 CSV 파일로 저장합니다.

    Args:
        symbol (str): 바이낸스 심볼 (예: 'BTCUSDT')
        start_date (str): 시작 날짜 (YYYY-MM-DD)
        interval (str): 시간 간격 (예: '15m', '4h')
        output_path (str): 저장할 CSV 파일 경로

    Returns:
        bool: 성공 여부
    """
    # 바이낸스에서 데이터 가져오기
    df = get_binance_klines(symbol, interval, start_date)

    if df is None or df.empty:
        print(f"❌ {symbol}에 대한 {interval} 데이터를 가져올 수 없습니다.")
        return False

    try:
        # 데이터 디렉토리가 없으면 생성
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # CSV 파일로 저장
        df.to_csv(output_path)
        print(f"✅ 성공: {len(df)}개 레코드가 '{output_path}'에 저장되었습니다.")

        return True

    except Exception as e:
        print(f"❌ 파일 저장 실패: {e}")
        return False
def verify_data_files():
    """
    다운로드된 데이터 파일들을 검증합니다.
    """
    print("\n🔍 데이터 파일 검증 중...")

    all_valid = True
    for timeframe, path in DATA_PATHS.items():
        if os.path.exists(path):
            try:
                df = pd.read_csv(path, index_col=0, parse_dates=True)
                print(f"✅ {timeframe}: {len(df)}개 레코드 ({df.index[0]} ~ {df.index[-1]})")
            except Exception as e:
                print(f"❌ {timeframe}: 파일 읽기 오류 - {e}")
                all_valid = False
        else:
            print(f"❌ {timeframe}: 파일이 존재하지 않음 - {path}")
            all_valid = False

    return all_valid


if __name__ == '__main__':
    """
    메인 실행 부분: 실제 고빈도 데이터를 다운로드하여 CSV로 저장
    이 스크립트는 최초 1회만 실행하면 됩니다.
    """
    print("🚀 Project LEVIATHAN - 바이낸스 API 고품질 데이터 수집기")
    print("=" * 60)
    print("🎯 바이낸스 거래소 공식 API를 사용하여 최고 품질의 데이터를 수집합니다.")
    print("⚠️  이 작업은 최초 한 번만 실행하면 됩니다.")
    print("⚠️  2018년부터 현재까지의 모든 데이터를 다운로드하므로 시간이 걸릴 수 있습니다.")
    print("📊 15분봉 데이터: 약 350,000개 레코드 예상")
    print("📊 4시간봉 데이터: 약 22,000개 레코드 예상")
    print()

    # 사용자 확인
    response = input("계속 진행하시겠습니까? (y/N): ").strip().lower()
    if response != 'y':
        print("작업이 취소되었습니다.")
        exit()

    print("\n📡 데이터 수집을 시작합니다...")

    success_count = 0
    total_count = len(TIMEFRAMES)

    # 설정된 모든 타임프레임에 대해 데이터 다운로드 실행
    for timeframe_name, timeframe_value in TIMEFRAMES.items():
        print(f"\n📊 {timeframe_name} 타임프레임 ({timeframe_value}) 처리 중...")

        if timeframe_name == 'EXECUTION':
            output_path = DATA_PATHS['15m']
        elif timeframe_name == 'TREND':
            output_path = DATA_PATHS['4h']
        elif timeframe_name == 'DAILY':
            output_path = DATA_PATHS['1d']
        else:
            continue

        success = download_binance_data_to_csv(TICKER, START_DATE, timeframe_value, output_path)
        if success:
            success_count += 1

    print(f"\n📋 데이터 수집 결과:")
    print(f"   성공: {success_count}/{total_count}")

    if success_count == total_count:
        print("\n🎉 모든 데이터 수집이 완료되었습니다!")

        # 데이터 파일 검증
        if verify_data_files():
            print("\n✅ 모든 데이터 파일이 정상적으로 생성되었습니다.")
            print("\n📝 다음 단계:")
            print("   1. python data/features.py 실행하여 피처 엔지니어링 테스트")
            print("   2. 이후 모든 작업은 저장된 CSV 파일을 사용합니다")
            print("   3. 더 이상 이 스크립트를 실행할 필요가 없습니다")
        else:
            print("\n❌ 일부 데이터 파일에 문제가 있습니다. 다시 실행해주세요.")
    else:
        print(f"\n❌ 일부 데이터 수집에 실패했습니다. 네트워크를 확인하고 다시 시도해주세요.")
