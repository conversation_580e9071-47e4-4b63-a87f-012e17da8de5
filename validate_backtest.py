#!/usr/bin/env python3
"""
🔍 Project LEVIATHAN - 백테스팅 검증
백테스팅 결과의 신뢰성을 검증하고 문제점을 찾아냄
"""

import pandas as pd
import numpy as np
import joblib
from pathlib import Path
import sys

# 프로젝트 루트 추가
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import get_config

def validate_backtest():
    """백테스팅 검증"""
    print("🔍 Project LEVIATHAN - 백테스팅 검증")
    print("=" * 60)
    
    try:
        # 1. 데이터 로드 및 기본 검증
        print("📊 1. 데이터 무결성 검증")
        print("-" * 40)
        
        # 피처 데이터
        features_df = pd.read_csv("data/processed/features_swing_enhanced_optimized.csv", 
                                index_col='timestamp', parse_dates=True)
        print(f"✅ 피처 데이터: {features_df.shape}")
        print(f"   기간: {features_df.index.min()} ~ {features_df.index.max()}")
        
        # 가격 데이터
        price_data = pd.read_csv('data/BTCUSDT_15m.csv', 
                               index_col='timestamp', parse_dates=True)
        print(f"✅ 가격 데이터: {price_data.shape}")
        print(f"   기간: {price_data.index.min()} ~ {price_data.index.max()}")
        
        # 2. 훈련/백테스팅 기간 겹침 검증
        print(f"\n🎯 2. 훈련/백테스팅 기간 겹침 검증")
        print("-" * 40)
        
        # 훈련 기간 (80% 분할점 계산)
        split_idx = int(len(features_df) * 0.8)
        train_end = features_df.index[split_idx-1]
        test_start = features_df.index[split_idx]
        
        print(f"📚 모델 훈련 기간: {features_df.index.min()} ~ {train_end}")
        print(f"🧪 모델 검증 기간: {test_start} ~ {features_df.index.max()}")
        
        # 백테스팅 기간 (수정된 기간)
        backtest_start = pd.to_datetime('2024-10-01')
        backtest_end = pd.to_datetime('2025-06-01')
        print(f"📈 백테스팅 기간: {backtest_start} ~ {backtest_end}")
        
        # 겹침 검증
        if backtest_start < train_end:
            overlap_start = max(backtest_start, features_df.index.min())
            overlap_end = min(backtest_end, train_end)
            overlap_days = (overlap_end - overlap_start).days
            
            print(f"⚠️ 훈련 데이터와 겹침 발견!")
            print(f"   겹치는 기간: {overlap_start} ~ {overlap_end} ({overlap_days}일)")
            print(f"   🚨 이는 미래 데이터 참조(Lookahead Bias)를 의미합니다!")
        else:
            print(f"✅ 훈련 데이터와 겹침 없음 (안전)")
        
        # 3. 신호 생성 시점 vs 실행 시점 검증
        print(f"\n⏰ 3. 신호 생성/실행 시점 검증")
        print("-" * 40)
        
        # 백테스팅 기간 데이터 추출
        mask = (features_df.index >= backtest_start) & (features_df.index <= backtest_end)
        backtest_features = features_df[mask]
        
        price_mask = (price_data.index >= backtest_start) & (price_data.index <= backtest_end)
        backtest_prices = price_data[price_mask]
        
        # 공통 인덱스
        common_index = backtest_features.index.intersection(backtest_prices.index)
        print(f"📊 공통 데이터 포인트: {len(common_index):,}개")
        
        # 샘플 검증 (처음 10개)
        print(f"\n🔍 샘플 시점 검증 (처음 5개):")
        for i in range(min(5, len(common_index))):
            timestamp = common_index[i]
            print(f"   {i+1}. {timestamp}")
            
            # 다음 시점 확인
            if i < len(common_index) - 1:
                next_timestamp = common_index[i+1]
                time_diff = next_timestamp - timestamp
                print(f"      다음 시점까지: {time_diff}")
        
        # 4. 모델 예측값 분포 검증
        print(f"\n🔮 4. 모델 예측값 분포 검증")
        print("-" * 40)
        
        model = joblib.load("models/swing_s_v2_long_model_final_v2.pkl")
        features_aligned = backtest_features.loc[common_index]
        predictions = model.predict(features_aligned)
        
        print(f"📊 예측값 통계:")
        print(f"   최소값: {predictions.min():.4f}")
        print(f"   최대값: {predictions.max():.4f}")
        print(f"   평균값: {predictions.mean():.4f}")
        print(f"   중간값: {np.median(predictions):.4f}")
        print(f"   표준편차: {predictions.std():.4f}")
        
        # 임계값별 신호 개수
        config = get_config('swing_s_v2')
        threshold = config.probability_threshold
        
        print(f"\n📊 임계값별 신호 개수:")
        for thresh in [0.3, 0.4, 0.5, 0.55, 0.6, 0.7, 0.8]:
            signal_count = (predictions > thresh).sum()
            percentage = signal_count / len(predictions) * 100
            marker = " ← 현재 설정" if thresh == threshold else ""
            print(f"   {thresh:.2f}: {signal_count:,}개 ({percentage:.2f}%){marker}")
        
        # 5. 수익률 현실성 검증
        print(f"\n💰 5. 수익률 현실성 검증")
        print("-" * 40)
        
        # BTC 실제 수익률 계산
        btc_start_price = backtest_prices.iloc[0]['close']
        btc_end_price = backtest_prices.iloc[-1]['close']
        btc_return = (btc_end_price - btc_start_price) / btc_start_price
        
        print(f"📈 BTC 실제 수익률 (2023-2024):")
        print(f"   시작 가격: ${btc_start_price:,.2f}")
        print(f"   종료 가격: ${btc_end_price:,.2f}")
        print(f"   수익률: {btc_return:.2%}")
        
        # 백테스팅 수익률과 비교 (수정된 현실적 결과)
        backtest_return = 0.2021  # 20.21% (수정된 결과)
        print(f"\n🤖 백테스팅 수익률: {backtest_return:.2%}")
        print(f"📊 BTC 대비 배수: {backtest_return/btc_return:.2f}배")

        if backtest_return > btc_return * 2:  # BTC의 2배 이상
            print(f"⚠️ 높은 수익률 (검토 필요)")
        elif backtest_return > btc_return:
            print(f"✅ BTC 대비 우수한 성과")
        else:
            print(f"📊 현실적인 수익률 범위")
        
        # 6. 거래 빈도 검증
        print(f"\n📅 6. 거래 빈도 검증")
        print("-" * 40)
        
        signals = (predictions > threshold).astype(int)
        signal_count = signals.sum()
        total_days = (backtest_end - backtest_start).days
        
        print(f"📊 거래 빈도 분석:")
        print(f"   총 신호: {signal_count:,}개")
        print(f"   총 기간: {total_days}일")
        print(f"   평균 신호 간격: {total_days/signal_count:.1f}일")
        print(f"   월평균 신호: {signal_count/(total_days/30):.1f}개")
        
        # 7. 권장사항
        print(f"\n💡 7. 권장사항")
        print("-" * 40)
        
        print(f"🔧 백테스팅 개선 방안:")
        print(f"   1. 훈련 데이터와 겹치지 않는 기간 사용")
        print(f"   2. 신호 생성 시점과 실행 시점 분리")
        print(f"   3. 현실적인 수수료/슬리피지 적용")
        print(f"   4. 레버리지 중복 적용 방지")
        print(f"   5. 포지션 크기 제한")
        print(f"   6. 일일 거래 한도 설정")
        
        print(f"\n🎯 현실적인 목표 수익률:")
        print(f"   - 연 50-200% (매우 우수)")
        print(f"   - 샤프 비율 1.5+ (우수)")
        print(f"   - 최대 낙폭 20% 이하")
        
    except Exception as e:
        print(f"❌ 검증 실패: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    validate_backtest()
