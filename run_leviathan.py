"""
Project LEVIATHAN - 메인 실행 스크립트
AI 기반 암호화폐 자동매매 시스템 통합 실행
"""

import sys
from pathlib import Path
import argparse

# 프로젝트 루트를 Python 경로에 추가
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from models import TargetGenerator, BinaryTradingModel, HybridPredictor
# from backtesting.professional_backtest import ProfessionalBacktester  # TODO: 업데이트 필요
from data.features import generate_features_from_csv
import config


def show_project_info():
    """
    프로젝트 정보 출력
    """
    print("🐋 Project LEVIATHAN v5.0")
    print("=" * 60)
    print("🎯 AI 기반 암호화폐 자동매매 시스템")
    print("🛡️ 자본 보존 우선 전략")
    print("🤖 이진 분류 모델 기반 예측")
    print("=" * 60)
    print(f"📊 데이터: BTCUSDT")
    print(f"⚖️ 레버리지: {config.LEVERAGE}x")
    print(f"📉 손절매: -{config.STOP_LOSS_PCT*100:.1f}%")
    print(f"📈 익절: +{config.TAKE_PROFIT_PCT*100:.1f}%")
    print(f"🎯 손익비: {config.RISK_REWARD_RATIO:.1f}:1")
    print("=" * 60)


def train_model():
    """
    모델 훈련 실행
    """
    print("\n🤖 이진 분류 모델 훈련 시작...")
    
    try:
        # 이진 분류 모델 초기화
        model = BinaryTradingModel()
        
        # 모델 훈련 (최적화 포함)
        model.train_binary_model(optimize=True, n_trials=50)
        
        # 모델 저장
        model.save_model('models/binary_trading_model.pkl')
        
        print("✅ 모델 훈련 완료!")
        
    except Exception as e:
        print(f"❌ 모델 훈련 실패: {e}")
        return False
    
    return True


def run_prediction():
    """
    실시간 예측 실행
    """
    print("\n🔮 하이브리드 예측 시스템 실행...")
    
    try:
        # 하이브리드 예측기 초기화
        predictor = HybridPredictor('models/binary_trading_model.pkl')
        
        # 최신 피처 데이터 가져오기
        latest_features = predictor.get_latest_features(50)
        
        # 하이브리드 신호 생성
        signals_df = predictor.generate_hybrid_signals(latest_features)
        
        # 신호 품질 분석
        analysis = predictor.analyze_signal_quality(signals_df)
        predictor.print_signal_analysis(analysis)
        
        # 최근 10개 신호 출력
        print(f"\n📊 최근 10개 신호:")
        recent_signals = signals_df.tail(10)[['signal', 'win_probability', 'position_size']]
        for idx, row in recent_signals.iterrows():
            print(f"   {row['signal']:>10}: {row['win_probability']:.1%} 확률, {row['position_size']:.0%} 포지션")
        
        print("✅ 예측 완료!")
        
    except Exception as e:
        print(f"❌ 예측 실패: {e}")
        return False
    
    return True


def run_backtest():
    """
    백테스팅 실행 (임시 비활성화)
    """
    print("\n📊 백테스팅 시스템 (업데이트 중)...")
    print("⚠️ 백테스팅 시스템이 이진 분류 모델에 맞게 업데이트 중입니다.")
    print("💡 현재는 evaluate_model.py를 사용하여 모델 성능을 확인할 수 있습니다.")

    return True


def generate_target():
    """
    타겟 변수 생성
    """
    print("\n🎯 트리플 배리어 타겟 생성...")
    
    try:
        # 타겟 생성기 초기화
        target_gen = TargetGenerator()
        
        # 가격 데이터 로드
        price_data = target_gen.load_price_data()
        
        # 트리플 배리어 타겟 생성
        target = target_gen.generate_triple_barrier_target(price_data)
        
        print("✅ 타겟 생성 완료!")
        
    except Exception as e:
        print(f"❌ 타겟 생성 실패: {e}")
        return False
    
    return True


def check_data():
    """
    데이터 상태 확인
    """
    print("\n📊 데이터 상태 확인...")
    
    try:
        # 피처 데이터 생성
        features_df = generate_features_from_csv()
        
        print(f"✅ 피처 데이터: {features_df.shape}")
        print(f"   기간: {features_df.index[0]} ~ {features_df.index[-1]}")
        print(f"   피처 수: {features_df.shape[1]}개")
        
        # 결측값 확인
        missing_data = features_df.isnull().sum().sum()
        print(f"   결측값: {missing_data}개")
        
        if missing_data == 0:
            print("✅ 데이터 품질: 우수")
        else:
            print("⚠️ 데이터 품질: 결측값 존재")
        
    except Exception as e:
        print(f"❌ 데이터 확인 실패: {e}")
        return False
    
    return True


def main():
    """
    메인 실행 함수
    """
    parser = argparse.ArgumentParser(description='Project LEVIATHAN - AI 암호화폐 자동매매 시스템')
    parser.add_argument('command', choices=['info', 'data', 'target', 'train', 'predict', 'backtest', 'all'],
                       help='실행할 명령')
    
    args = parser.parse_args()
    
    # 프로젝트 정보 출력
    show_project_info()
    
    if args.command == 'info':
        print("\n📋 사용 가능한 명령:")
        print("   data     - 데이터 상태 확인")
        print("   target   - 트리플 배리어 타겟 생성")
        print("   train    - 이진 분류 모델 훈련")
        print("   predict  - 하이브리드 예측 실행")
        print("   backtest - 전문가용 백테스팅")
        print("   all      - 전체 파이프라인 실행")
        
    elif args.command == 'data':
        check_data()
        
    elif args.command == 'target':
        generate_target()
        
    elif args.command == 'train':
        train_model()
        
    elif args.command == 'predict':
        run_prediction()
        
    elif args.command == 'backtest':
        run_backtest()
        
    elif args.command == 'all':
        print("\n🚀 전체 파이프라인 실행...")
        
        success = True
        success &= check_data()
        success &= generate_target()
        success &= train_model()
        success &= run_prediction()
        success &= run_backtest()
        
        if success:
            print("\n🎉 전체 파이프라인 실행 완료!")
        else:
            print("\n❌ 파이프라인 실행 중 오류 발생")
    
    print(f"\n🐋 Project LEVIATHAN 실행 완료")


if __name__ == "__main__":
    main()
