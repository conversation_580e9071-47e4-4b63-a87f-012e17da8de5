"""
Project LEVIATHAN - 개선된 타겟 생성 테스트
24시간 보유기간으로 타겟 분포 확인
"""

import pandas as pd
import numpy as np
import sys
from pathlib import Path

# 프로젝트 루트를 Python 경로에 추가
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_target_distribution():
    """개선된 타겟 분포 테스트"""
    print("🎯 개선된 타겟 분포 테스트")
    print("=" * 50)
    print("⏰ 보유기간: 24시간 (96개 봉)")
    print("📉 손절: -2%")
    print("📈 익절: +5%")
    print("=" * 50)
    
    try:
        # 1. 롱 타겟 테스트
        print("\n🔥 롱 타겟 분포 테스트...")
        from models.target_generator import TargetGenerator
        
        # 가격 데이터 로드 (샘플)
        price_data = pd.read_csv('data/BTCUSDT_15m.csv', index_col='timestamp', parse_dates=True)
        sample_data = price_data.tail(10000)  # 최근 10,000개만 테스트
        
        generator = TargetGenerator()
        long_targets = generator.generate_triple_barrier_target(sample_data)
        
        print(f"✅ 롱 타겟 생성 완료: {long_targets.shape}")
        print(f"📊 롱 타겟 분포:")
        target_counts = long_targets.value_counts().sort_index()
        total = len(long_targets)
        
        for label, count in target_counts.items():
            percentage = (count / total) * 100
            label_name = {-1: "손절", 0: "시간만료", 1: "익절"}[label]
            print(f"   {label_name}({label}): {count:,}개 ({percentage:.1f}%)")
        
        # 2. 숏 타겟 테스트
        print(f"\n🔻 숏 타겟 분포 테스트...")
        from models.short_target_generator import ShortTargetGenerator
        
        short_generator = ShortTargetGenerator()
        short_targets = short_generator.generate_short_target(sample_data)
        
        print(f"✅ 숏 타겟 생성 완료: {short_targets.shape}")
        print(f"📊 숏 타겟 분포:")
        short_counts = short_targets.value_counts().sort_index()
        short_total = len(short_targets)
        
        for label, count in short_counts.items():
            percentage = (count / short_total) * 100
            label_name = {-1: "손절", 0: "시간만료", 1: "익절"}[label]
            print(f"   {label_name}({label}): {count:,}개 ({percentage:.1f}%)")
        
        # 3. 분포 분석
        print(f"\n📊 타겟 분포 분석:")
        
        # 롱 분석
        long_signal_ratio = (target_counts.get(1, 0) + target_counts.get(-1, 0)) / total * 100
        long_profit_ratio = target_counts.get(1, 0) / total * 100
        
        print(f"   🔥 롱 모델:")
        print(f"      신호 비율: {long_signal_ratio:.1f}% (손절+익절)")
        print(f"      익절 비율: {long_profit_ratio:.1f}%")
        print(f"      시간만료: {target_counts.get(0, 0) / total * 100:.1f}%")
        
        # 숏 분석
        short_signal_ratio = (short_counts.get(1, 0) + short_counts.get(-1, 0)) / short_total * 100
        short_profit_ratio = short_counts.get(1, 0) / short_total * 100
        
        print(f"   🔻 숏 모델:")
        print(f"      신호 비율: {short_signal_ratio:.1f}% (손절+익절)")
        print(f"      익절 비율: {short_profit_ratio:.1f}%")
        print(f"      시간만료: {short_counts.get(0, 0) / short_total * 100:.1f}%")
        
        # 4. 권장사항
        print(f"\n💡 분석 결과:")
        
        if long_signal_ratio < 20:
            print(f"   ⚠️ 롱 신호 비율이 낮음 ({long_signal_ratio:.1f}%)")
            print(f"      → 손익절 비율 조정 고려")
        else:
            print(f"   ✅ 롱 신호 비율 양호 ({long_signal_ratio:.1f}%)")
        
        if short_signal_ratio < 20:
            print(f"   ⚠️ 숏 신호 비율이 낮음 ({short_signal_ratio:.1f}%)")
            print(f"      → 손익절 비율 조정 고려")
        else:
            print(f"   ✅ 숏 신호 비율 양호 ({short_signal_ratio:.1f}%)")
        
        # 5. 개선 제안
        print(f"\n🔧 개선 제안:")
        
        if long_profit_ratio < 5:
            print(f"   📈 익절 비율 증가 방안:")
            print(f"      - 익절 목표 4% → 3%로 낮추기")
            print(f"      - 손절 목표 2% → 3%로 높이기")
        
        if long_signal_ratio > 30:
            print(f"   📊 신호 품질 향상 방안:")
            print(f"      - 더 엄격한 진입 조건")
            print(f"      - 피처 최적화 필요")
        
        return {
            'long_signal_ratio': long_signal_ratio,
            'short_signal_ratio': short_signal_ratio,
            'long_profit_ratio': long_profit_ratio,
            'short_profit_ratio': short_profit_ratio
        }
        
    except Exception as e:
        print(f"❌ 테스트 실패: {e}")
        import traceback
        traceback.print_exc()
        return None


def suggest_optimal_settings(results):
    """최적 설정 제안"""
    if not results:
        return
    
    print(f"\n🎯 최적 설정 제안:")
    print("=" * 50)
    
    # 현재 설정
    print(f"📊 현재 설정 (24시간):")
    print(f"   손절: -2%, 익절: +5%")
    print(f"   롱 신호율: {results['long_signal_ratio']:.1f}%")
    print(f"   숏 신호율: {results['short_signal_ratio']:.1f}%")
    
    # 제안 설정들
    print(f"\n💡 제안 설정:")
    
    if results['long_signal_ratio'] < 15:
        print(f"   🔧 Option A (신호 증가):")
        print(f"      손절: -3%, 익절: +4% (24시간)")
        print(f"      → 더 많은 거래 기회")
    
    if results['long_profit_ratio'] < 3:
        print(f"   🔧 Option B (수익성 개선):")
        print(f"      손절: -2%, 익절: +3% (24시간)")
        print(f"      → 더 현실적인 목표")
    
    print(f"   🔧 Option C (균형):")
    print(f"      손절: -2.5%, 익절: +4% (24시간)")
    print(f"      → 신호율과 수익성 균형")
    
    print(f"   🔧 Option D (보수적):")
    print(f"      손절: -1.5%, 익절: +3% (12시간)")
    print(f"      → 빠른 손절, 현실적 익절")


def main():
    """메인 실행"""
    results = test_target_distribution()
    
    if results:
        suggest_optimal_settings(results)
        
        print(f"\n🚀 다음 단계:")
        print(f"   1. 최적 설정 선택")
        print(f"   2. 35개 피처로 모델 재훈련")
        print(f"   3. AUC 성능 비교")
        
        return True
    else:
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n✅ 타겟 분포 분석 완료!")
    else:
        print(f"\n❌ 타겟 분포 분석 실패!")
