"""
Project LEVIATHAN - 전문가용 백테스팅 엔진
자본 보존 우선 전략을 적용한 리스크 관리 백테스팅 시스템
"""

import pandas as pd
import numpy as np
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 프로젝트 루트를 Python 경로에 추가
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config import (
    LEVERAGE, STOP_LOSS_PCT, TAKE_PROFIT_PCT, FEE_RATE, SLIPPAGE_RATE,
    MAX_POSITION_SIZE, LIQUIDATION_BUFFER, RISK_PER_TRADE, REWARD_PER_TRADE,
    RISK_REWARD_RATIO, BREAKEVEN_WIN_RATE
)
from models import BinaryTradingModel, HybridPredictor
from data.features import generate_features_from_csv


class ProfessionalBacktester:
    """
    전문가용 백테스팅 엔진 - 자본 보존 우선 전략
    """
    
    def __init__(self, initial_capital: float = 10000.0):
        """
        백테스터 초기화
        
        Args:
            initial_capital: 초기 자본 (USD)
        """
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        
        # 거래 기록
        self.trades = []
        self.portfolio_history = []
        
        # 성과 지표
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.total_fees = 0.0
        self.max_drawdown = 0.0
        self.peak_capital = initial_capital
        
        print(f"🛡️ 전문가용 백테스터 초기화")
        print(f"   💰 초기 자본: ${initial_capital:,.2f}")
        print(f"   ⚖️ 레버리지: {LEVERAGE}x")
        print(f"   📉 손절매: -{STOP_LOSS_PCT*100:.1f}%")
        print(f"   📈 익절: +{TAKE_PROFIT_PCT*100:.1f}%")
        print(f"   🎯 손익비: {RISK_REWARD_RATIO:.1f}:1")
        print(f"   🎲 손익분기점 승률: {BREAKEVEN_WIN_RATE*100:.1f}%")
        
    def calculate_position_size(self, entry_price: float, signal_strength: float = 1.0) -> float:
        """
        포지션 크기 계산 (자본 보존 우선)
        
        Args:
            entry_price: 진입 가격
            signal_strength: 신호 강도 (0.0 ~ 1.0)
            
        Returns:
            포지션 크기 (USD)
        """
        # 기본 포지션 크기 (계좌의 10%)
        base_position = self.current_capital * MAX_POSITION_SIZE
        
        # 신호 강도에 따른 조정
        adjusted_position = base_position * signal_strength
        
        # 레버리지 적용
        leveraged_position = adjusted_position * LEVERAGE
        
        return leveraged_position
    
    def calculate_stop_loss_take_profit(self, entry_price: float, 
                                      position_type: str) -> Tuple[float, float]:
        """
        손절매/익절 가격 계산
        
        Args:
            entry_price: 진입 가격
            position_type: 'long' 또는 'short'
            
        Returns:
            (손절가, 익절가) 튜플
        """
        if position_type == 'long':
            stop_loss_price = entry_price * (1 - STOP_LOSS_PCT)
            take_profit_price = entry_price * (1 + TAKE_PROFIT_PCT)
        else:  # short
            stop_loss_price = entry_price * (1 + STOP_LOSS_PCT)
            take_profit_price = entry_price * (1 - TAKE_PROFIT_PCT)
            
        return stop_loss_price, take_profit_price
    
    def calculate_trading_costs(self, position_size: float) -> float:
        """
        거래 비용 계산 (수수료 + 슬리피지)
        
        Args:
            position_size: 포지션 크기
            
        Returns:
            총 거래 비용
        """
        # 진입 + 청산 수수료 (양방향)
        fees = position_size * FEE_RATE * 2
        
        # 진입 + 청산 슬리피지 (양방향)
        slippage = position_size * SLIPPAGE_RATE * 2
        
        return fees + slippage
    
    def execute_trade(self, entry_time: pd.Timestamp, entry_price: float,
                     exit_time: pd.Timestamp, exit_price: float,
                     position_type: str, signal_strength: float = 1.0,
                     exit_reason: str = 'unknown') -> Dict:
        """
        거래 실행 및 기록
        
        Args:
            entry_time: 진입 시간
            entry_price: 진입 가격
            exit_time: 청산 시간
            exit_price: 청산 가격
            position_type: 'long' 또는 'short'
            signal_strength: 신호 강도
            exit_reason: 청산 이유
            
        Returns:
            거래 결과 딕셔너리
        """
        # 포지션 크기 계산
        position_size = self.calculate_position_size(entry_price, signal_strength)
        
        # 손절/익절 가격 계산
        stop_loss_price, take_profit_price = self.calculate_stop_loss_take_profit(
            entry_price, position_type
        )
        
        # 수익률 계산 (자산 가격 기준)
        if position_type == 'long':
            price_return = (exit_price - entry_price) / entry_price
        else:  # short
            price_return = (entry_price - exit_price) / entry_price
            
        # 레버리지 적용 수익률
        leveraged_return = price_return * LEVERAGE
        
        # 거래 비용 계산
        trading_costs = self.calculate_trading_costs(position_size)
        
        # 순손익 계산
        gross_pnl = self.current_capital * MAX_POSITION_SIZE * leveraged_return
        net_pnl = gross_pnl - trading_costs
        
        # 자본 업데이트
        self.current_capital += net_pnl
        self.total_fees += trading_costs
        
        # 최대 낙폭 업데이트
        if self.current_capital > self.peak_capital:
            self.peak_capital = self.current_capital
        
        current_drawdown = (self.peak_capital - self.current_capital) / self.peak_capital
        if current_drawdown > self.max_drawdown:
            self.max_drawdown = current_drawdown
        
        # 거래 분류
        self.total_trades += 1
        if net_pnl > 0:
            self.winning_trades += 1
        else:
            self.losing_trades += 1
        
        # 거래 기록
        trade_record = {
            'trade_id': self.total_trades,
            'entry_time': entry_time,
            'exit_time': exit_time,
            'entry_price': entry_price,
            'exit_price': exit_price,
            'position_type': position_type,
            'position_size': position_size,
            'stop_loss_price': stop_loss_price,
            'take_profit_price': take_profit_price,
            'signal_strength': signal_strength,
            'price_return': price_return,
            'leveraged_return': leveraged_return,
            'gross_pnl': gross_pnl,
            'trading_costs': trading_costs,
            'net_pnl': net_pnl,
            'exit_reason': exit_reason,
            'capital_after': self.current_capital,
            'drawdown': current_drawdown
        }
        
        self.trades.append(trade_record)
        
        return trade_record
    
    def run_backtest_with_model(self, model_path: str, 
                               start_date: Optional[str] = None,
                               end_date: Optional[str] = None) -> Dict:
        """
        LightGBM 모델을 사용한 백테스팅 실행
        
        Args:
            model_path: 훈련된 모델 파일 경로
            start_date: 백테스팅 시작일 (YYYY-MM-DD)
            end_date: 백테스팅 종료일 (YYYY-MM-DD)
            
        Returns:
            백테스팅 결과 딕셔너리
        """
        print(f"🚀 전문가용 백테스팅 시작...")
        print(f"   📊 모델: {model_path}")
        
        # 하이브리드 예측 엔진 초기화
        predictor = HybridPredictor(model_path)
        
        # 피처 데이터 로드
        features_df = generate_features_from_csv()
        
        # 날짜 필터링
        if start_date:
            features_df = features_df[features_df.index >= start_date]
        if end_date:
            features_df = features_df[features_df.index <= end_date]
            
        print(f"   📅 백테스팅 기간: {features_df.index[0]} ~ {features_df.index[-1]}")
        print(f"   📊 총 데이터 포인트: {len(features_df)}개")
        
        # 가격 데이터 로드
        from config import DATA_PATHS
        price_data = pd.read_csv(DATA_PATHS['15m'], index_col='timestamp', parse_dates=True)
        price_data = price_data.reindex(features_df.index)
        
        # 백테스팅 실행
        current_position = None
        entry_info = None
        
        for i, (timestamp, features) in enumerate(features_df.iterrows()):
            if i < 100:  # 초기 100개는 건너뛰기 (피처 안정화)
                continue
                
            current_price = price_data.loc[timestamp, 'close']
            
            # 예측 수행
            prediction_result = predictor.predict_single(features)
            signal = predictor.generate_trading_signal(prediction_result)
            
            # 포지션이 없는 경우 - 진입 신호 확인
            if current_position is None:
                if signal['action'] in ['buy', 'strong_buy']:
                    # 롱 포지션 진입
                    current_position = 'long'
                    entry_info = {
                        'time': timestamp,
                        'price': current_price,
                        'signal_strength': signal['confidence']
                    }
                elif signal['action'] in ['sell', 'strong_sell']:
                    # 숏 포지션 진입
                    current_position = 'short'
                    entry_info = {
                        'time': timestamp,
                        'price': current_price,
                        'signal_strength': signal['confidence']
                    }
            
            # 포지션이 있는 경우 - 청산 조건 확인
            elif current_position is not None and entry_info is not None:
                entry_price = entry_info['price']
                stop_loss_price, take_profit_price = self.calculate_stop_loss_take_profit(
                    entry_price, current_position
                )
                
                exit_reason = None
                
                # 손절/익절 조건 확인
                if current_position == 'long':
                    if current_price <= stop_loss_price:
                        exit_reason = 'stop_loss'
                    elif current_price >= take_profit_price:
                        exit_reason = 'take_profit'
                    elif signal['action'] in ['sell', 'strong_sell']:
                        exit_reason = 'signal_reversal'
                else:  # short
                    if current_price >= stop_loss_price:
                        exit_reason = 'stop_loss'
                    elif current_price <= take_profit_price:
                        exit_reason = 'take_profit'
                    elif signal['action'] in ['buy', 'strong_buy']:
                        exit_reason = 'signal_reversal'
                
                # 청산 실행
                if exit_reason:
                    self.execute_trade(
                        entry_time=entry_info['time'],
                        entry_price=entry_info['price'],
                        exit_time=timestamp,
                        exit_price=current_price,
                        position_type=current_position,
                        signal_strength=entry_info['signal_strength'],
                        exit_reason=exit_reason
                    )
                    
                    current_position = None
                    entry_info = None
            
            # 포트폴리오 기록
            self.portfolio_history.append({
                'timestamp': timestamp,
                'capital': self.current_capital,
                'position': current_position,
                'price': current_price
            })
        
        # 백테스팅 결과 계산
        results = self.calculate_performance_metrics()
        
        print(f"✅ 백테스팅 완료!")
        self.print_performance_summary(results)
        
        return results
    
    def calculate_performance_metrics(self) -> Dict:
        """
        성과 지표 계산
        
        Returns:
            성과 지표 딕셔너리
        """
        if self.total_trades == 0:
            return {'error': '거래가 없습니다.'}
        
        # 기본 지표
        total_return = (self.current_capital - self.initial_capital) / self.initial_capital
        win_rate = self.winning_trades / self.total_trades if self.total_trades > 0 else 0
        
        # 거래별 수익률
        trade_returns = [trade['net_pnl'] / self.initial_capital for trade in self.trades]
        
        # 샤프 비율 (일간 기준 추정)
        if len(trade_returns) > 1:
            avg_return = np.mean(trade_returns)
            std_return = np.std(trade_returns)
            sharpe_ratio = avg_return / std_return * np.sqrt(252) if std_return > 0 else 0
        else:
            sharpe_ratio = 0
        
        # 평균 손익
        winning_trades_pnl = [t['net_pnl'] for t in self.trades if t['net_pnl'] > 0]
        losing_trades_pnl = [t['net_pnl'] for t in self.trades if t['net_pnl'] < 0]
        
        avg_win = np.mean(winning_trades_pnl) if winning_trades_pnl else 0
        avg_loss = np.mean(losing_trades_pnl) if losing_trades_pnl else 0
        
        return {
            'initial_capital': self.initial_capital,
            'final_capital': self.current_capital,
            'total_return': total_return,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': abs(avg_win / avg_loss) if avg_loss != 0 else float('inf'),
            'max_drawdown': self.max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'total_fees': self.total_fees,
            'risk_reward_ratio': RISK_REWARD_RATIO,
            'breakeven_win_rate': BREAKEVEN_WIN_RATE
        }
    
    def print_performance_summary(self, results: Dict) -> None:
        """
        성과 요약 출력
        
        Args:
            results: 성과 지표 딕셔너리
        """
        print(f"\n" + "="*60)
        print(f"📊 전문가용 백테스팅 결과 요약")
        print(f"="*60)
        
        print(f"💰 자본 변화:")
        print(f"   초기 자본: ${results['initial_capital']:,.2f}")
        print(f"   최종 자본: ${results['final_capital']:,.2f}")
        print(f"   총 수익률: {results['total_return']*100:+.2f}%")
        
        print(f"\n📈 거래 통계:")
        print(f"   총 거래 수: {results['total_trades']}회")
        print(f"   승리 거래: {results['winning_trades']}회")
        print(f"   패배 거래: {results['losing_trades']}회")
        print(f"   승률: {results['win_rate']*100:.1f}%")
        
        print(f"\n💡 손익 분석:")
        print(f"   평균 승리: ${results['avg_win']:,.2f}")
        print(f"   평균 손실: ${results['avg_loss']:,.2f}")
        print(f"   손익비: {results['profit_factor']:.2f}:1")
        
        print(f"\n🛡️ 리스크 지표:")
        print(f"   최대 낙폭: {results['max_drawdown']*100:.2f}%")
        print(f"   샤프 비율: {results['sharpe_ratio']:.2f}")
        print(f"   총 수수료: ${results['total_fees']:,.2f}")
        
        print(f"\n🎯 전략 설정:")
        print(f"   설정 손익비: {results['risk_reward_ratio']:.1f}:1")
        print(f"   손익분기점 승률: {results['breakeven_win_rate']*100:.1f}%")
        print(f"   실제 승률: {results['win_rate']*100:.1f}%")
        
        # 성과 평가
        if results['win_rate'] > results['breakeven_win_rate']:
            print(f"\n✅ 성공: 실제 승률이 손익분기점을 상회합니다!")
        else:
            print(f"\n⚠️ 주의: 실제 승률이 손익분기점에 미달합니다.")


if __name__ == "__main__":
    """
    전문가용 백테스터 테스트
    """
    print("🛡️ Project LEVIATHAN - 전문가용 백테스팅 테스트")
    print("=" * 60)
    
    try:
        # 백테스터 초기화
        backtester = ProfessionalBacktester(initial_capital=10000.0)
        
        # 모델을 사용한 백테스팅 실행
        model_path = 'models/lgb_classification_60min.pkl'
        results = backtester.run_backtest_with_model(
            model_path=model_path,
            start_date='2024-01-01'  # 최근 1년간 테스트
        )
        
        print(f"\n🎉 전문가용 백테스팅 테스트 완료!")
        
    except Exception as e:
        print(f"❌ 오류 발생: {e}")
        import traceback
        traceback.print_exc()
