"""
Project LEVIATHAN - 1시간봉 데이터 생성
15분봉에서 1시간봉을 리샘플링하여 생성
"""

import pandas as pd
import numpy as np
from pathlib import Path


def create_1h_from_15m():
    """15분봉에서 1시간봉 생성"""
    print("🔄 15분봉에서 1시간봉 데이터 생성 중...")
    
    try:
        # 15분봉 데이터 로드
        df_15m = pd.read_csv('data/BTCUSDT_15m.csv', index_col='timestamp', parse_dates=True)
        print(f"📊 15분봉 데이터: {len(df_15m)}개 레코드")
        print(f"📅 기간: {df_15m.index[0]} ~ {df_15m.index[-1]}")
        
        # OHLCV 리샘플링 규칙
        ohlc_dict = {
            'open': 'first',   # 첫 번째 값
            'high': 'max',     # 최고값
            'low': 'min',      # 최저값
            'close': 'last',   # 마지막 값
            'volume': 'sum'    # 합계
        }
        
        # 1시간봉으로 리샘플링
        df_1h = df_15m.resample('1H').agg(ohlc_dict)
        
        # 결측값 제거
        df_1h = df_1h.dropna()
        
        print(f"✅ 1시간봉 생성 완료: {len(df_1h)}개 레코드")
        print(f"📅 기간: {df_1h.index[0]} ~ {df_1h.index[-1]}")
        
        # 데이터 품질 확인
        print(f"\n📊 1시간봉 데이터 품질 확인:")
        print(f"   평균 거래량: {df_1h['volume'].mean():,.0f}")
        print(f"   평균 가격: ${df_1h['close'].mean():,.0f}")
        print(f"   가격 범위: ${df_1h['close'].min():,.0f} ~ ${df_1h['close'].max():,.0f}")
        
        # 1시간봉 저장
        output_path = 'data/BTCUSDT_1h.csv'
        df_1h.to_csv(output_path)
        print(f"💾 1시간봉 저장: {output_path}")
        
        return df_1h
        
    except Exception as e:
        print(f"❌ 1시간봉 생성 실패: {e}")
        return None


def analyze_trading_frequency():
    """거래 빈도 분석"""
    print(f"\n📊 타임프레임별 거래 빈도 분석")
    print("=" * 50)
    
    timeframes = {
        '15분봉': {'candles_per_day': 96, 'file': 'data/BTCUSDT_15m.csv'},
        '1시간봉': {'candles_per_day': 24, 'file': 'data/BTCUSDT_1h.csv'},
        '4시간봉': {'candles_per_day': 6, 'file': 'data/BTCUSDT_4h.csv'},
        '1일봉': {'candles_per_day': 1, 'file': 'data/BTCUSDT_1D.csv'}
    }
    
    for name, info in timeframes.items():
        try:
            if Path(info['file']).exists():
                df = pd.read_csv(info['file'], index_col='timestamp', parse_dates=True)
                days = (df.index[-1] - df.index[0]).days
                actual_candles_per_day = len(df) / days if days > 0 else 0
                
                print(f"📈 {name}:")
                print(f"   이론적 신호/일: {info['candles_per_day']}개")
                print(f"   실제 신호/일: {actual_candles_per_day:.1f}개")
                print(f"   총 레코드: {len(df):,}개")
                print(f"   기간: {days}일")
                
                # 거래 기회 추정 (신호율 10% 가정)
                estimated_trades_per_day = actual_candles_per_day * 0.1
                print(f"   예상 거래/일: {estimated_trades_per_day:.1f}개")
                print()
            else:
                print(f"⚠️ {name}: 파일 없음 ({info['file']})")
                
        except Exception as e:
            print(f"❌ {name} 분석 실패: {e}")


def suggest_optimal_timeframe():
    """최적 타임프레임 조합 제안"""
    print(f"💡 스윙 트레이딩 최적 타임프레임 제안")
    print("=" * 50)
    
    options = [
        {
            'name': 'Option A: 하이브리드 (권장)',
            'composition': '1시간 (진입/청산) + 4시간 (신호) + 1일 + 1주',
            'trades_per_day': '2-4개',
            'pros': ['적절한 거래 빈도', '정밀한 타이밍', '스윙에 최적'],
            'cons': ['복잡성 증가']
        },
        {
            'name': 'Option B: 4시간 중심 (현재)',
            'composition': '4시간 (메인) + 1일 + 1주',
            'trades_per_day': '0.5-1개',
            'pros': ['단순함', '노이즈 최소', '안정적 신호'],
            'cons': ['거래 기회 부족', '자본 효율성 저하']
        },
        {
            'name': 'Option C: 1시간 중심',
            'composition': '1시간 (메인) + 4시간 + 1일 + 1주',
            'trades_per_day': '2-5개',
            'pros': ['균형잡힌 빈도', '스윙 적합'],
            'cons': ['15분봉보다 신호 적음']
        },
        {
            'name': 'Option D: 멀티 레이어',
            'composition': '1시간 (실행) + 4시간 (신호) + 1일 (추세) + 1주 (방향)',
            'trades_per_day': '1-3개',
            'pros': ['각 타임프레임 역할 분담', '최적 균형'],
            'cons': ['구현 복잡성']
        }
    ]
    
    for i, option in enumerate(options, 1):
        print(f"🎯 {option['name']}")
        print(f"   구성: {option['composition']}")
        print(f"   예상 거래/일: {option['trades_per_day']}")
        print(f"   장점: {', '.join(option['pros'])}")
        print(f"   단점: {', '.join(option['cons'])}")
        print()


def main():
    """메인 실행"""
    print("🚀 Project LEVIATHAN - 1시간봉 데이터 생성 및 분석")
    print("=" * 60)
    
    # 1. 1시간봉 생성
    df_1h = create_1h_from_15m()
    
    if df_1h is not None:
        # 2. 거래 빈도 분석
        analyze_trading_frequency()
        
        # 3. 최적 타임프레임 제안
        suggest_optimal_timeframe()
        
        print(f"🎯 결론:")
        print(f"   1시간봉이 성공적으로 생성되었습니다!")
        print(f"   이제 다양한 타임프레임 조합을 테스트할 수 있습니다.")
        print(f"   권장: 1시간 + 4시간 + 1일 + 1주 조합")
        
        return True
    else:
        print(f"❌ 1시간봉 생성 실패")
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n✅ 1시간봉 생성 및 분석 완료!")
    else:
        print(f"\n💥 1시간봉 생성 실패!")
