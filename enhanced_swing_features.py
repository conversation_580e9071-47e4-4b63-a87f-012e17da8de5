#!/usr/bin/env python3
"""
🧠 Project LEVIATHAN - 업그레이드된 스윙 피처 엔지니어링
주봉 + 컨플루언스 + ATR 동적 리스크 관리 피처 통합 시스템
"""

import pandas as pd
import numpy as np
import sys
from pathlib import Path
from typing import Dict, Any, Optional
import warnings
warnings.filterwarnings('ignore')

# 프로젝트 루트 추가
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import get_config
from data.features import calculate_rsi, calculate_macd, calculate_bollinger_bands


def calculate_atr(high, low, close, period=14):
    """ATR (Average True Range) 계산"""
    tr1 = high - low
    tr2 = abs(high - close.shift(1))
    tr3 = abs(low - close.shift(1))
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    atr = tr.rolling(window=period).mean()
    return atr


def calculate_stochastic(high, low, close, k_period=14, d_period=3):
    """스토캐스틱 오실레이터 계산"""
    lowest_low = low.rolling(window=k_period).min()
    highest_high = high.rolling(window=k_period).max()
    k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
    d_percent = k_percent.rolling(window=d_period).mean()
    return k_percent, d_percent


def calculate_mfi(high, low, close, volume, period=14):
    """MFI (Money Flow Index) 계산"""
    typical_price = (high + low + close) / 3
    money_flow = typical_price * volume
    
    # 가격 변화 방향 결정
    price_change = typical_price.diff()
    positive_flow = money_flow.where(price_change > 0, 0).rolling(window=period).sum()
    negative_flow = money_flow.where(price_change < 0, 0).rolling(window=period).sum()
    
    # MFI 계산
    money_flow_ratio = positive_flow / negative_flow
    mfi = 100 - (100 / (1 + money_flow_ratio))
    return mfi


class EnhancedSwingFeatureGenerator:
    """업그레이드된 스윙 피처 생성기"""
    
    def __init__(self):
        """초기화"""
        self.config = get_config('swing_s_v2')
        
        print(f"🧠 {self.config.strategy_name} {self.config.version} 업그레이드 피처 생성기 초기화")
        print(f"📝 전략: {self.config.description}")
        print(f"🎯 새로운 기능: 주봉 + 컨플루언스 + ATR 동적 리스크 관리")
        
    def load_all_timeframe_data(self) -> Dict[str, pd.DataFrame]:
        """모든 시간 프레임 데이터 로드 (15m, 4h, 1d, 1w)"""
        print("📊 전체 시간 프레임 데이터 로딩 중...")
        
        data = {}
        
        # 15분봉 데이터 (실행 프레임)
        try:
            data['15m'] = pd.read_csv('data/BTCUSDT_15m.csv', index_col='timestamp', parse_dates=True)
            print(f"   ✅ 15분봉: {len(data['15m']):,}개 레코드")
        except FileNotFoundError:
            print(f"   ❌ 15분봉 파일 없음")
            return {}
        
        # 4시간봉 데이터 (중기 추세)
        try:
            data['4h'] = pd.read_csv('data/BTCUSDT_4h.csv', index_col='timestamp', parse_dates=True)
            print(f"   ✅ 4시간봉: {len(data['4h']):,}개 레코드")
        except FileNotFoundError:
            print(f"   ❌ 4시간봉 파일 없음")
            return {}
        
        # 일봉 데이터 (장기 추세)
        try:
            data['1d'] = pd.read_csv('data/BTCUSDT_1D.csv', index_col='timestamp', parse_dates=True)
            print(f"   ✅ 일봉: {len(data['1d']):,}개 레코드")
        except FileNotFoundError:
            print(f"   ⚠️ 일봉 파일 없음")
            data['1d'] = None
        
        # 주봉 데이터 (초장기 추세) - 새로 추가
        try:
            data['1w'] = pd.read_csv('data/BTCUSDT_1W.csv', index_col='timestamp', parse_dates=True)
            print(f"   ✅ 주봉: {len(data['1w']):,}개 레코드")
        except FileNotFoundError:
            print(f"   ⚠️ 주봉 파일 없음")
            data['1w'] = None
        
        return data
    
    def generate_technical_indicators(self, df: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """시간 프레임별 기술적 지표 생성"""
        print(f"   📈 {timeframe} 기술적 지표 생성 중...")
        
        result_df = df.copy()
        
        # RSI
        result_df[f'RSI_14_{timeframe}'] = calculate_rsi(df['close'], 14)
        
        # MACD
        macd, macd_signal, macd_hist = calculate_macd(df['close'], 12, 26, 9)
        result_df[f'MACD_{timeframe}'] = macd
        result_df[f'MACD_signal_{timeframe}'] = macd_signal
        result_df[f'MACD_hist_{timeframe}'] = macd_hist
        
        # 볼린저 밴드
        bb_upper, bb_middle, bb_lower, bb_width, bb_position = calculate_bollinger_bands(df['close'], 20, 2.0)
        result_df[f'BB_upper_{timeframe}'] = bb_upper
        result_df[f'BB_middle_{timeframe}'] = bb_middle
        result_df[f'BB_lower_{timeframe}'] = bb_lower
        result_df[f'BB_width_{timeframe}'] = bb_width
        result_df[f'BB_position_{timeframe}'] = bb_position
        
        # 이동평균
        for period in [20, 50, 200]:
            result_df[f'SMA_{period}_{timeframe}'] = df['close'].rolling(window=period).mean()
        
        # ATR (동적 리스크 관리용)
        result_df[f'ATR_14_{timeframe}'] = calculate_atr(df['high'], df['low'], df['close'], 14)
        
        # 스토캐스틱
        stoch_k, stoch_d = calculate_stochastic(df['high'], df['low'], df['close'], 14, 3)
        result_df[f'Stoch_K_{timeframe}'] = stoch_k
        result_df[f'Stoch_D_{timeframe}'] = stoch_d
        
        # 거래량 기반 지표들
        if 'volume' in df.columns:
            # 거래량 이동평균
            result_df[f'Volume_MA_20_{timeframe}'] = df['volume'].rolling(window=20).mean()
            result_df[f'Volume_ratio_{timeframe}'] = df['volume'] / result_df[f'Volume_MA_20_{timeframe}']
            
            # MFI
            result_df[f'MFI_14_{timeframe}'] = calculate_mfi(df['high'], df['low'], df['close'], df['volume'], 14)
        
        # 고가/저가 기반 지표
        result_df[f'High_20_{timeframe}'] = df['high'].rolling(window=20).max()
        result_df[f'Low_20_{timeframe}'] = df['low'].rolling(window=20).min()
        
        print(f"   ✅ {timeframe} 기술적 지표 완료: {len([c for c in result_df.columns if timeframe in c])}개 추가")
        return result_df
    
    def generate_confluence_features(self, merged_df: pd.DataFrame) -> pd.DataFrame:
        """컨플루언스(Confluence) 상호작용 피처 생성"""
        print("🧠 컨플루언스 상호작용 피처 생성 중...")
        
        result_df = merged_df.copy()
        
        # 1. 추세 + 모멘텀 컨플루언스 (상승)
        print("   🔄 추세 + 모멘텀 컨플루언스 생성...")
        try:
            # 장기 추세 상승 조건
            long_trend_up = result_df['close'] > result_df['SMA_50_1d']
            
            # 중기 모멘텀 상승 조건
            rsi_bullish = (result_df['RSI_14_4h'] > 50) & (result_df['RSI_14_4h'] > result_df['RSI_14_4h'].shift(1))
            
            # 컨플루언스 조건
            result_df['Is_Trend_Momentum_Confluence_Bull'] = (long_trend_up & rsi_bullish).astype(int)
            print("   ✅ 추세 + 모멘텀 컨플루언스 완료")
        except KeyError as e:
            print(f"   ⚠️ 추세 + 모멘텀 컨플루언스 생략 (필요 피처 없음: {e})")
        
        # 2. 돌파 + 거래량 컨플루언스 (상승)
        print("   🔄 돌파 + 거래량 컨플루언스 생성...")
        try:
            # 저항선 돌파 조건
            breakout_condition = result_df['close'] > result_df['High_20_4h'].shift(1)
            
            # 거래량 증가 조건
            volume_surge = result_df['Volume_ratio_4h'] > 1.5
            
            # 컨플루언스 조건
            result_df['Is_Breakout_Volume_Confluence_Bull'] = (breakout_condition & volume_surge).astype(int)
            print("   ✅ 돌파 + 거래량 컨플루언스 완료")
        except KeyError as e:
            print(f"   ⚠️ 돌파 + 거래량 컨플루언스 생략 (필요 피처 없음: {e})")
        
        # 3. 다중 시간 프레임 정렬 컨플루언스
        print("   🔄 다중 시간 프레임 정렬 컨플루언스 생성...")
        try:
            # 각 시간 프레임별 추세 방향
            trend_15m = (result_df['SMA_20_15m'] > result_df['SMA_50_15m']).astype(int)
            trend_4h = (result_df['SMA_20_4h'] > result_df['SMA_50_4h']).astype(int)
            trend_1d = (result_df['SMA_20_1d'] > result_df['SMA_50_1d']).astype(int)
            
            # 주봉 추세 (있는 경우)
            if 'SMA_20_1w' in result_df.columns:
                trend_1w = (result_df['SMA_20_1w'] > result_df['SMA_50_1w']).astype(int)
                # 4개 시간 프레임 정렬도 (0~4)
                result_df['Multi_Timeframe_Alignment'] = trend_15m + trend_4h + trend_1d + trend_1w
            else:
                # 3개 시간 프레임 정렬도 (0~3)
                result_df['Multi_Timeframe_Alignment'] = trend_15m + trend_4h + trend_1d
            
            print("   ✅ 다중 시간 프레임 정렬 완료")
        except KeyError as e:
            print(f"   ⚠️ 다중 시간 프레임 정렬 생략 (필요 피처 없음: {e})")
        
        # 4. MACD + RSI 컨플루언스 (추가)
        print("   🔄 MACD + RSI 컨플루언스 생성...")
        try:
            # MACD 상승 신호
            macd_bullish = (result_df['MACD_4h'] > result_df['MACD_signal_4h']) & \
                          (result_df['MACD_hist_4h'] > result_df['MACD_hist_4h'].shift(1))
            
            # RSI 과매도에서 회복
            rsi_recovery = (result_df['RSI_14_4h'] > 30) & (result_df['RSI_14_4h'].shift(1) <= 30)
            
            # 컨플루언스 조건
            result_df['Is_MACD_RSI_Confluence_Bull'] = (macd_bullish & rsi_recovery).astype(int)
            print("   ✅ MACD + RSI 컨플루언스 완료")
        except KeyError as e:
            print(f"   ⚠️ MACD + RSI 컨플루언스 생략 (필요 피처 없음: {e})")
        
        confluence_features = [col for col in result_df.columns if 'Confluence' in col or 'Alignment' in col]
        print(f"   🎯 컨플루언스 피처 완료: {len(confluence_features)}개 생성")
        
        return result_df
    
    def generate_atr_risk_features(self, merged_df: pd.DataFrame) -> pd.DataFrame:
        """ATR 기반 동적 리스크 관리 피처 생성"""
        print("⚖️ ATR 기반 동적 리스크 관리 피처 생성 중...")
        
        result_df = merged_df.copy()
        
        # 1. 변동성 수준 피처 (각 시간 프레임별)
        print("   📊 변동성 수준 피처 생성...")
        for timeframe in ['15m', '4h', '1d']:
            atr_col = f'ATR_14_{timeframe}'
            if atr_col in result_df.columns:
                # 현재 ATR / 과거 50기간 ATR 평균
                atr_ma = result_df[atr_col].rolling(window=50).mean()
                result_df[f'Volatility_Level_{timeframe}'] = result_df[atr_col] / atr_ma
                print(f"   ✅ {timeframe} 변동성 수준 완료")
        
        # 2. 손절선까지의 거리 피처
        print("   📏 손절선까지의 거리 피처 생성...")
        for timeframe in ['4h', '1d']:
            atr_col = f'ATR_14_{timeframe}'
            if atr_col in result_df.columns:
                # 진입가를 현재 종가로 가정
                entry_price = result_df['close']
                
                # ATR 기반 손절선 (진입가 - 2.5 * ATR)
                atr_stop = entry_price - (2.5 * result_df[atr_col])
                
                # 손절선까지의 거리 (비율)
                result_df[f'Distance_to_ATR_Stop_{timeframe}'] = (entry_price - atr_stop) / entry_price
                print(f"   ✅ {timeframe} 손절선 거리 완료")
        
        # 3. 변동성 브레이크아웃 신호
        print("   🚀 변동성 브레이크아웃 신호 생성...")
        try:
            # 변동성이 평균보다 낮을 때 (압축)
            low_volatility = result_df['Volatility_Level_4h'] < 0.8
            
            # 가격이 볼린저 밴드 상단 돌파
            bb_breakout = result_df['close'] > result_df['BB_upper_4h']
            
            # 변동성 브레이크아웃 신호
            result_df['Volatility_Breakout_Signal'] = (low_volatility.shift(1) & bb_breakout).astype(int)
            print("   ✅ 변동성 브레이크아웃 신호 완료")
        except KeyError as e:
            print(f"   ⚠️ 변동성 브레이크아웃 신호 생략 (필요 피처 없음: {e})")
        
        # 4. 리스크 조정 신호 강도
        print("   ⚖️ 리스크 조정 신호 강도 생성...")
        try:
            # 기본 신호 강도 (RSI 기반)
            base_signal = (result_df['RSI_14_4h'] - 50) / 50  # -1 ~ 1 범위
            
            # 변동성으로 조정 (변동성이 높으면 신호 강도 감소)
            volatility_adjustment = 1 / result_df['Volatility_Level_4h'].clip(0.5, 2.0)
            
            # 리스크 조정된 신호 강도
            result_df['Risk_Adjusted_Signal_Strength'] = base_signal * volatility_adjustment
            print("   ✅ 리스크 조정 신호 강도 완료")
        except KeyError as e:
            print(f"   ⚠️ 리스크 조정 신호 강도 생략 (필요 피처 없음: {e})")
        
        atr_features = [col for col in result_df.columns if any(keyword in col for keyword in 
                       ['Volatility_Level', 'Distance_to_ATR', 'Volatility_Breakout', 'Risk_Adjusted'])]
        print(f"   🎯 ATR 리스크 관리 피처 완료: {len(atr_features)}개 생성")
        
        return result_df


def main():
    """메인 실행 함수"""
    print("🧠 Project LEVIATHAN - 업그레이드된 스윙 피처 엔지니어링")
    print("=" * 80)
    print("🎯 새로운 기능: 주봉 + 컨플루언스 + ATR 동적 리스크 관리")
    print()
    
    try:
        generator = EnhancedSwingFeatureGenerator()
        
        # 1. 모든 시간 프레임 데이터 로드
        data = generator.load_all_timeframe_data()
        if not data:
            raise FileNotFoundError("필수 데이터 파일이 없습니다.")
        
        # 2. 각 시간 프레임별 기술적 지표 생성
        print("\n🔄 시간 프레임별 기술적 지표 생성...")
        enhanced_data = {}
        for timeframe, df in data.items():
            if df is not None:
                enhanced_data[timeframe] = generator.generate_technical_indicators(df, timeframe)
        
        # 3. 15분봉 기준으로 데이터 병합
        print("\n🔗 다중 시간 프레임 데이터 병합...")
        base_df = enhanced_data['15m'].copy()
        
        # 다른 시간 프레임 데이터 병합
        for timeframe in ['4h', '1d', '1w']:
            if timeframe in enhanced_data and enhanced_data[timeframe] is not None:
                # 리샘플링하여 15분봉에 맞춤
                if timeframe == '4h':
                    resampled = enhanced_data[timeframe].resample('15T').ffill()
                elif timeframe == '1d':
                    resampled = enhanced_data[timeframe].resample('15T').ffill()
                elif timeframe == '1w':
                    resampled = enhanced_data[timeframe].resample('15T').ffill()
                
                # 공통 인덱스로 병합
                common_index = base_df.index.intersection(resampled.index)
                base_df = base_df.loc[common_index]
                
                # 피처 추가 (OHLCV 제외)
                for col in enhanced_data[timeframe].columns:
                    if col not in ['open', 'high', 'low', 'close', 'volume']:
                        base_df[col] = resampled.loc[common_index, col]
        
        print(f"   ✅ 병합 완료: {base_df.shape}")
        
        # 4. 컨플루언스 피처 생성
        print("\n🧠 컨플루언스 피처 생성...")
        base_df = generator.generate_confluence_features(base_df)
        
        # 5. ATR 기반 동적 리스크 관리 피처 생성
        print("\n⚖️ ATR 기반 동적 리스크 관리 피처 생성...")
        final_df = generator.generate_atr_risk_features(base_df)
        
        # 6. 원본 OHLCV 컬럼 제거 (피처만 남김)
        feature_columns = [col for col in final_df.columns 
                          if col not in ['open', 'high', 'low', 'close', 'volume']]
        final_df = final_df[feature_columns].dropna()
        
        print(f"\n✅ 업그레이드된 스윙 피처 생성 완료!")
        print(f"   📊 최종 데이터: {final_df.shape}")
        print(f"   📈 총 피처 수: {len(final_df.columns)}개")
        
        # 피처 타입별 분류
        features_15m = [col for col in final_df.columns if col.endswith('_15m')]
        features_4h = [col for col in final_df.columns if col.endswith('_4h')]
        features_1d = [col for col in final_df.columns if col.endswith('_1d')]
        features_1w = [col for col in final_df.columns if col.endswith('_1w')]
        confluence_features = [col for col in final_df.columns if 'Confluence' in col or 'Alignment' in col]
        atr_features = [col for col in final_df.columns if any(keyword in col for keyword in 
                       ['Volatility_Level', 'Distance_to_ATR', 'Volatility_Breakout', 'Risk_Adjusted'])]
        other_features = [col for col in final_df.columns if not any(col.endswith(suffix) for suffix in 
                         ['_15m', '_4h', '_1d', '_1w']) and col not in confluence_features and col not in atr_features]
        
        print(f"\n🎯 피처 분류:")
        print(f"      15분봉: {len(features_15m)}개")
        print(f"      4시간봉: {len(features_4h)}개")
        print(f"      일봉: {len(features_1d)}개")
        print(f"      주봉: {len(features_1w)}개")
        print(f"      컨플루언스: {len(confluence_features)}개")
        print(f"      ATR 리스크: {len(atr_features)}개")
        print(f"      기타: {len(other_features)}개")
        
        # 결과 저장
        output_path = "data/processed/features_swing_enhanced.csv"
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        final_df.to_csv(output_path)
        
        print(f"\n💾 업그레이드된 피처 데이터 저장 완료: {output_path}")
        print(f"📊 저장된 데이터: {final_df.shape}")
        
        # 샘플 출력
        print(f"\n📋 새로운 피처 샘플:")
        new_features = confluence_features + atr_features + features_1w
        if new_features:
            sample_features = new_features[:10]  # 상위 10개만
            print(final_df[sample_features].tail())
        
        print(f"\n🎉 업그레이드된 스윙 피처 생성 완료!")
        print(f"🎯 다음 단계: 피처 최적화 및 모델 훈련")
        
        return final_df
        
    except Exception as e:
        print(f"❌ 피처 생성 실패: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    main()
