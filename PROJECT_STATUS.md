# 🐋 Project LEVIATHAN - 현재 개발 상태

## 📊 프로젝트 개요
- **목표**: AI 기반 암호화폐 선물 자동매매 시스템 (3단계: 백테스팅 → 페이퍼 → 실거래)
- **현재 Phase**: Phase 1 (데이터 인프라 & 피처 엔지니어링) - **90% 완성** 🔥
- **개발 기간**: 2024년 12월 시작
- **개발자**: 강현모
- **핵심 혁신**: 바이낸스 API + 다중 시간 프레임 분석 (MTFA)

## ✅ 완성된 기능들

### 1. 프로젝트 구조 (100% 완성)
```
c:\project/
├── 📄 config.py                    # 바이낸스 설정 및 시스템 구성
├── 📄 verify_features.py           # 데이터 품질 검증 스크립트 ✨ 신규
├── 📄 PROJECT_STATUS.md            # 프로젝트 상태 문서
├── 📄 .gitignore                   # Git 제외 파일 설정
│
├── 📂 data/                        # 데이터 모듈
│   ├── 📄 __init__.py
│   ├── 📄 collector.py             # 바이낸스 API 데이터 수집기 🔄 리팩토링
│   ├── 📄 features.py              # CSV 기반 피처 엔지니어링 🔄 리팩토링
│   ├── 📄 BTCUSDT_15m.csv          # 15분봉 데이터 (260,577개)
│   └── 📄 BTCUSDT_4h.csv           # 4시간봉 데이터 (16,304개)
│
├── 📂 검증 결과 파일/
│   ├── 🖼️ verification_plot_01_multi_timeframe_ma.png
│   └── 🖼️ verification_plot_02_multi_timeframe_rsi.png
│
└── 📂 테스트 파일/
    ├── 📄 test_mtfa.py
    └── 📄 test_new_periods.py
```

### 2. 바이낸스 API 데이터 수집 (100% 완성) 🚀
- **파일**: `data/collector.py`
- **혁신**: yfinance 제한 극복 → 바이낸스 거래소 공식 API 사용
- **성과**: 7.5년간 고품질 데이터 (2018-2025)
  - **15분봉**: 260,577개 레코드
  - **4시간봉**: 16,304개 레코드
- **품질**: 완벽한 OHLCV 형식, 멀티레벨 헤더 문제 해결

### 3. 다중 시간 프레임 피처 엔지니어링 (100% 완성) ⭐⭐⭐
- **파일**: `data/features.py`
- **핵심 기술**: Multi-Timeframe Analysis (MTFA)
- **총 21개 정교한 피처**:
  - **추세 피처 (6개)**: 4시간봉 기반 (SMA_50, RSI_14, MACD, 볼린저밴드)
  - **실행 피처 (13개)**: 15분봉 기반 (단기 지표, 거래량 분석)
  - **상호작용 피처 (2개)**: 추세-실행 결합 지표
- **데이터 품질**: 결측값 0개, 259,796개 완벽한 레코드

### 4. 데이터 품질 검증 시스템 (100% 완성) ✨ 신규
- **파일**: `verify_features.py`
- **기능**: 3단계 데이터 품질 검증
  1. **기본 정보 확인**: 데이터 형태, 기간, 결측값 분석
  2. **시각화 검증**: 다중 시간 프레임 이동평균선 & RSI 차트
  3. **통계적 분석**: 피처별 분포, 이상값 검출
- **결과**: 완벽한 데이터 품질 확인 (결측값 0개, 합리적 분포)

### 5. CSV 기반 고속 처리 시스템 (100% 완성)
- **혁신**: API 호출 없는 로컬 CSV 기반 피처 생성
- **성능**: 260,577개 레코드를 수초 내 처리
- **안정성**: 네트워크 의존성 제거, 일관된 결과

### 6. Git 버전 관리 (100% 완성)
- **v2.0** (`2848a24`): 🚀 바이낸스 API 고품질 데이터 시스템 완성
- **v1.2** (`266ba5e`): 📋 프로젝트 상태 문서 추가
- **v1.1** (`551f32e`): 🔧 피처 엔지니어링 완성
- **v1.0** (`a09b1c8`): 🐋 Walk-Forward Analysis 백테스팅 시스템

## 🔥 핵심 성과 지표

### 📊 데이터 품질 지표
| 항목 | 수치 | 상태 |
|------|------|------|
| **총 레코드 수** | 259,796개 | ✅ 우수 |
| **데이터 기간** | 7.4년 (2018~2025) | ✅ 충분 |
| **피처 수** | 21개 | ✅ 정교함 |
| **결측값** | 0개 | ✅ 완벽 |
| **메모리 사용량** | 43.6 MB | ✅ 효율적 |

### 🎯 기술적 혁신
1. **yfinance → 바이낸스 API**: 60일 제한 → 7.5년 무제한 데이터
2. **다중 시간 프레임**: 4시간 추세 + 15분 실행의 완벽한 결합
3. **CSV 기반 처리**: API 호출 없는 고속 피처 생성
4. **프로페셔널 검증**: 3단계 품질 검증 시스템

### 🎯 투자 전략 설정 (MTFA 기반)
- **실행 시간 프레임**: 15분봉 (정밀한 진입/청산 신호)
- **추세 시간 프레임**: 4시간봉 (시장 전체 방향성 파악)
- **투자 스타일**: 고빈도 단기 레버리지 트레이딩
- **보유 기간**: 15분-4시간 (평균 1-2시간)
- **목표 수익률**: 시간당 0.5%+ (일일 2%+ 유지)

### 리스크 관리
- **레버리지**: 10x
- **최대 포지션**: 계좌 대비 10%
- **손절매**: 5%
- **익절**: 15%
- **강제청산 버퍼**: 20%

## 🚀 다음 개발 단계: Phase 2 (우선순위)

### 🎯 **즉시 실행 가능한 다음 작업**

#### **우선순위 1: LightGBM 모델 구현 (3-4일 소요)**
```python
# 구현할 파일들
models/lgb_model.py      # LightGBM 분류/회귀 모델
models/predictor.py      # 실시간 예측 엔진
models/target_generator.py  # 타겟 변수 생성

# 주요 기능
- 21개 MTFA 피처를 활용한 수익률 예측
- 타겟 변수: 미래 N분 후 수익률 (분류/회귀)
- 하이퍼파라미터 자동 튜닝 (Optuna)
- 피처 중요도 분석
- Walk-Forward 검증
```

#### **우선순위 2: 백테스팅 시스템 구축 (2-3일)**
- 거래 신호 생성 로직 (LightGBM 예측 → 매수/매도/관망)
- 리스크 관리 시스템 (손절매, 익절, 포지션 사이징)
- 성과 지표 계산 (Sharpe, MDD, 승률 등)
- 실제 데이터로 백테스팅 실행

#### **우선순위 3: 모니터링 대시보드 (2-3일)**
- Streamlit 기반 실시간 대시보드
- 4구역: 상태/포트폴리오, 시스템 로그, 수익 곡선, 거래 이력

## 📊 현재 데이터 현황

### 바이낸스 API 기반 고품질 데이터
- **총 피처 수**: 21개 (추세 6개 + 실행 13개 + 상호작용 2개)
- **데이터 기간**: 2018-2025 (7.4년간)
- **15분봉 데이터**: 260,577개 레코드
- **4시간봉 데이터**: 16,304개 레코드
- **최종 피처 데이터**: 259,796개 레코드 (결측값 제거 후)

### 피처 통계적 특성
**추세 피처 (4시간봉 기반)**
- SMA_50_trend: 평균 $31,989 (표준편차 $26,603)
- RSI_14_trend: 평균 51.26 (표준편차 17.78) - 중립적 수준

**실행 피처 (15분봉 기반)**
- SMA_20_exec: 평균 $32,123 (표준편차 $26,788)
- RSI_14_exec: 평균 51.97 (표준편차 17.78) - 중립적 수준

**상호작용 피처**
- RSI_divergence: 평균 -0.72 (실행 RSI가 추세 RSI보다 약간 낮음)
- MA_above_trend: 52.4% (실행 이평선이 추세 이평선 위에 있는 비율)

## 🔧 실행 방법

### 환경 설정
```bash
cd c:\project
pip install pandas numpy matplotlib seaborn python-binance
```

### 주요 실행 명령어
```bash
# 바이낸스 데이터 수집 (최초 1회만)
python data/collector.py

# 피처 엔지니어링 실행
python data/features.py

# 데이터 품질 검증 (3단계 검증 + 차트 생성)
python verify_features.py

# 설정 확인
python config.py
```

### Git 상태 확인
```bash
git log --oneline
git status
```

## 💡 개발 팁

### 새로운 AI와 대화 시작할 때
1. 이 문서를 먼저 보여주기
2. `git log --oneline`로 커밋 히스토리 확인
3. `python verify_features.py` 실행해서 현재 데이터 상태 확인
4. 다음 단계 (LightGBM 모델) 구현 요청

### 핵심 파일들
- `config.py`: 바이낸스 설정 및 시스템 구성
- `data/collector.py`: 바이낸스 API 데이터 수집기 (완성)
- `data/features.py`: CSV 기반 MTFA 피처 엔지니어링 (완성)
- `verify_features.py`: 데이터 품질 검증 스크립트 (완성)
- `data/BTCUSDT_*.csv`: 바이낸스 고품질 데이터 파일들

## 🏆 프로젝트 성숙도

### **완료된 영역 (90%)**
- ✅ **데이터 수집**: 바이낸스 API 통합
- ✅ **피처 엔지니어링**: 21개 MTFA 피처
- ✅ **데이터 검증**: 3단계 품질 검증
- ✅ **인프라**: CSV 기반 고속 처리

### **다음 단계 (10%)**
- 🔄 **LightGBM 모델**: 타겟 변수 생성 + 모델 훈련
- 🔄 **백테스팅**: 거래 신호 생성 + 성과 측정
- 🔄 **리스크 관리**: 손절매/익절 로직

## 🎯 최종 목표

**Phase 1 완성 후 예상 성과:**
- **연간 수익률**: 20-40% (고빈도 MTFA 전략)
- **샤프 비율**: 1.0-1.5 (리스크 대비 수익)
- **최대 낙폭**: 15-25%
- **BTC 대비 초과 수익**: 15-30%p

---

**📅 마지막 업데이트**: 2024년 12월 (바이낸스 API 고품질 데이터 시스템 완성)
**🚀 다음 목표**: LightGBM 모델 구현 → 백테스팅 → 성과 검증
**💪 핵심 강점**: 프로페셔널 데이터 + MTFA + 완벽한 검증
