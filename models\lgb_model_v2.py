"""
Project LEVIATHAN - LightGBM 모델 v2.0 (트리플 배리어 메소드)
트리플 배리어 타겟을 사용한 전문가 수준의 예측 모델
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import classification_report, confusion_matrix, f1_score
import optuna
import joblib
import sys
from pathlib import Path
from typing import Dict, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 프로젝트 루트를 Python 경로에 추가
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from data.features import generate_features_from_csv
from models.target_generator import TargetGenerator
from config import PROBABILITY_THRESHOLD


class LightGBMModelV2:
    """
    트리플 배리어 메소드 기반 LightGBM 모델 v2.0
    """
    
    def __init__(self):
        """
        LightGBM 모델 v2.0 초기화
        """
        self.model = None
        self.feature_importance = None
        self.best_params = None
        
        # 트리플 배리어 전용 하이퍼파라미터 (클래스 불균형 해결 강화)
        self.default_params = {
            'objective': 'multiclass',
            'metric': 'multi_logloss',
            'num_class': 3,  # 3클래스: -1(손절), 0(시간만료), 1(익절)
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.9,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'class_weight': 'balanced',  # 불균형 데이터 처리 (강화)
            'is_unbalance': True,  # 추가: 불균형 데이터 전용 최적화
            'scale_pos_weight': 10,  # 추가: 소수 클래스 가중치 강화
            'verbose': -1,
            'random_state': 42
        }
        
        print(f"🤖 LightGBM 모델 v2.1 초기화 (클래스 불균형 해결 강화)")
        print(f"   🎯 타겟 클래스: 3개 (-1: 손절, 0: 시간만료, 1: 익절)")
        print(f"   ⚖️ 클래스 가중치: 균형 조정 + 소수 클래스 강화")
        print(f"   🔍 목표: '짚더미 속 바늘 찾기' - 익절 신호 포착 능력 향상")
        
    def prepare_triple_barrier_data(self) -> Tuple[pd.DataFrame, pd.Series]:
        """
        트리플 배리어 타겟으로 데이터 준비
        
        Returns:
            (features, target) 튜플
        """
        print("📊 트리플 배리어 데이터 준비 중...")
        
        # 피처 데이터 로드
        features_df = generate_features_from_csv()
        print(f"   ✅ 피처 데이터: {features_df.shape}")
        
        # 타겟 생성기 초기화
        target_gen = TargetGenerator()
        
        # 가격 데이터 로드
        price_data = target_gen.load_price_data()
        
        # 피처와 가격 데이터 인덱스 맞춤
        common_index = features_df.index.intersection(price_data.index)
        features_aligned = features_df.loc[common_index]
        price_aligned = price_data.loc[common_index]
        
        print(f"   🔗 인덱스 정렬 완료: {len(common_index)}개 공통 시점")
        
        # 트리플 배리어 타겟 생성
        triple_barrier_target = target_gen.generate_triple_barrier_target(price_aligned)
        
        # 타겟을 피처 인덱스에 맞춤
        target_aligned = triple_barrier_target.loc[common_index]
        
        # 결측값 제거
        valid_mask = target_aligned.notna() & features_aligned.notna().all(axis=1)
        
        final_features = features_aligned[valid_mask]
        final_target = target_aligned[valid_mask]
        
        # 타겟 라벨을 0, 1, 2로 변환 (LightGBM 요구사항)
        label_mapping = {-1: 0, 0: 1, 1: 2}  # 손절: 0, 시간만료: 1, 익절: 2
        final_target_mapped = final_target.map(label_mapping)
        
        print(f"   ✅ 최종 데이터: {final_features.shape}, 타겟: {len(final_target_mapped)}")
        
        # 최종 타겟 분포
        target_counts = final_target_mapped.value_counts().sort_index()
        print(f"   📊 최종 타겟 분포:")
        print(f"      클래스 0 (손절): {target_counts.get(0, 0):,}개")
        print(f"      클래스 1 (시간만료): {target_counts.get(1, 0):,}개")
        print(f"      클래스 2 (익절): {target_counts.get(2, 0):,}개")

        # 클래스 불균형 분석
        total_samples = len(final_target_mapped)
        class_ratios = {
            0: target_counts.get(0, 0) / total_samples,
            1: target_counts.get(1, 0) / total_samples,
            2: target_counts.get(2, 0) / total_samples
        }

        print(f"   🔍 클래스 불균형 분석:")
        print(f"      손절 비율: {class_ratios[0]*100:.1f}%")
        print(f"      시간만료 비율: {class_ratios[1]*100:.1f}%")
        print(f"      익절 비율: {class_ratios[2]*100:.1f}% ← 핵심 과제!")

        # 익절 클래스 희소성 강조
        rarity_factor = class_ratios[1] / class_ratios[2]  # 시간만료 대비 익절 희소성
        print(f"   ⚠️ 익절 희소성: 시간만료 대비 {rarity_factor:.0f}배 희소")

        return final_features, final_target_mapped
    
    def train_with_triple_barrier(self, optimize: bool = True, n_trials: int = 30) -> None:
        """
        트리플 배리어 타겟으로 모델 훈련
        
        Args:
            optimize: 하이퍼파라미터 최적화 여부
            n_trials: 최적화 시도 횟수
        """
        print(f"🚀 트리플 배리어 LightGBM 모델 훈련 시작...")
        
        # 데이터 준비
        X, y = self.prepare_triple_barrier_data()
        
        # 하이퍼파라미터 최적화
        if optimize:
            params = self.optimize_hyperparameters(X, y, n_trials)
        else:
            params = self.default_params.copy()
            
        # 시계열 분할 (80% 훈련, 20% 검증)
        split_idx = int(len(X) * 0.8)
        X_train, X_val = X.iloc[:split_idx], X.iloc[split_idx:]
        y_train, y_val = y.iloc[:split_idx], y.iloc[split_idx:]
        
        print(f"   📊 훈련 데이터: {X_train.shape}")
        print(f"   📊 검증 데이터: {X_val.shape}")
        
        # LightGBM 데이터셋 생성
        train_data = lgb.Dataset(X_train, label=y_train)
        val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
        
        # 모델 훈련
        self.model = lgb.train(
            params,
            train_data,
            valid_sets=[val_data],
            num_boost_round=500,
            callbacks=[lgb.early_stopping(50), lgb.log_evaluation(50)]
        )
        
        # 피처 중요도 저장
        self.feature_importance = pd.DataFrame({
            'feature': X.columns,
            'importance': self.model.feature_importance()
        }).sort_values('importance', ascending=False)
        
        # 검증 성능 평가
        y_pred = self.model.predict(X_val)
        y_pred_class = np.argmax(y_pred, axis=1)
        
        # F1 스코어 계산 (매크로 평균)
        f1_macro = f1_score(y_val, y_pred_class, average='macro')
        f1_weighted = f1_score(y_val, y_pred_class, average='weighted')
        
        print(f"   ✅ F1 스코어 (매크로): {f1_macro:.4f}")
        print(f"   ✅ F1 스코어 (가중): {f1_weighted:.4f}")
        
        # 분류 보고서
        class_names = ['손절(-1)', '시간만료(0)', '익절(1)']
        print(f"\n   📊 분류 보고서:")
        print(classification_report(y_val, y_pred_class, target_names=class_names))
        
        # 혼동 행렬
        cm = confusion_matrix(y_val, y_pred_class)
        print(f"\n   🔍 혼동 행렬:")
        print(f"        예측→  손절  시간만료  익절")
        for i, actual_class in enumerate(class_names):
            print(f"   {actual_class:>8}: {cm[i]}")
        
        # 피처 중요도 상위 10개
        print(f"\n   🔝 상위 10개 중요 피처:")
        for i, row in self.feature_importance.head(10).iterrows():
            print(f"      {row['feature']}: {row['importance']}")
            
        print(f"\n🎉 트리플 배리어 모델 훈련 완료!")
    
    def optimize_hyperparameters(self, X: pd.DataFrame, y: pd.Series, 
                                n_trials: int = 30) -> Dict:
        """
        Optuna를 사용한 하이퍼파라미터 최적화
        
        Args:
            X: 피처 데이터
            y: 타겟 데이터
            n_trials: 최적화 시도 횟수
            
        Returns:
            최적 하이퍼파라미터
        """
        print(f"🔧 하이퍼파라미터 최적화 시작... ({n_trials}회 시도)")
        
        def objective(trial):
            # 하이퍼파라미터 탐색 공간 정의
            params = {
                'objective': 'multiclass',
                'metric': 'multi_logloss',
                'num_class': 3,
                'boosting_type': 'gbdt',
                'num_leaves': trial.suggest_int('num_leaves', 10, 100),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                'feature_fraction': trial.suggest_float('feature_fraction', 0.4, 1.0),
                'bagging_fraction': trial.suggest_float('bagging_fraction', 0.4, 1.0),
                'bagging_freq': trial.suggest_int('bagging_freq', 1, 7),
                'min_child_samples': trial.suggest_int('min_child_samples', 5, 100),
                'class_weight': 'balanced',
                'verbose': -1,
                'random_state': 42
            }
            
            # 시계열 교차 검증
            tscv = TimeSeriesSplit(n_splits=3)
            scores = []
            
            for train_idx, val_idx in tscv.split(X):
                X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
                y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
                
                # 모델 훈련
                train_data = lgb.Dataset(X_train, label=y_train)
                val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
                
                model = lgb.train(
                    params,
                    train_data,
                    valid_sets=[val_data],
                    num_boost_round=100,
                    callbacks=[lgb.early_stopping(10), lgb.log_evaluation(0)]
                )
                
                # 예측 및 평가 (F1 스코어 사용)
                y_pred = model.predict(X_val)
                y_pred_class = np.argmax(y_pred, axis=1)
                score = f1_score(y_val, y_pred_class, average='macro')
                scores.append(score)
            
            return np.mean(scores)
        
        # Optuna 최적화 실행
        study = optuna.create_study(direction='maximize')
        study.optimize(objective, n_trials=n_trials, show_progress_bar=True)
        
        self.best_params = study.best_params.copy()
        self.best_params.update({
            'objective': 'multiclass',
            'metric': 'multi_logloss',
            'num_class': 3,
            'boosting_type': 'gbdt',
            'class_weight': 'balanced',
            'verbose': -1,
            'random_state': 42
        })
            
        print(f"   ✅ 최적화 완료! 최고 F1 스코어: {study.best_value:.4f}")
        print(f"   🎯 최적 파라미터: {self.best_params}")
        
        return self.best_params
    
    def save_model(self, filepath: str = 'models/lgb_model_v2_1.pkl') -> None:
        """
        모델 저장

        Args:
            filepath: 저장할 파일 경로
        """
        model_data = {
            'model': self.model,
            'feature_importance': self.feature_importance,
            'best_params': self.best_params,
            'model_version': 'v2.1_class_imbalance_enhanced',
            'class_mapping': {0: '손절(-1)', 1: '시간만료(0)', 2: '익절(1)'},
            'enhancement': 'class_weight_balanced + is_unbalance + scale_pos_weight'
        }

        joblib.dump(model_data, filepath)
        print(f"✅ 클래스 불균형 해결 강화 모델이 '{filepath}'에 저장되었습니다.")


if __name__ == "__main__":
    """
    트리플 배리어 LightGBM 모델 v2.1 테스트 (클래스 불균형 해결 강화)
    """
    print("🎯 Project LEVIATHAN - LightGBM 모델 v2.1 (클래스 불균형 해결)")
    print("=" * 60)
    print("🔍 목표: '짚더미 속 바늘 찾기' - 익절 신호 포착 능력 향상")
    print("⚖️ 전략: class_weight=balanced + is_unbalance + scale_pos_weight")
    print("=" * 60)

    try:
        # 모델 초기화
        model_v2_1 = LightGBMModelV2()

        # 클래스 불균형 해결 강화 훈련
        model_v2_1.train_with_triple_barrier(optimize=False)

        # 모델 저장
        model_v2_1.save_model()

        print(f"\n🎉 클래스 불균형 해결 강화 모델 v2.1 테스트 완료!")
        print(f"📊 기대 효과: 익절 재현율 및 정밀도 향상")

    except Exception as e:
        print(f"❌ 오류 발생: {e}")
        import traceback
        traceback.print_exc()
