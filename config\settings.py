"""
Project LEVIATHAN - 통합 설정 관리
모든 설정값을 중앙에서 관리하여 코드 중복 제거 및 유지보수성 향상
"""

from dataclasses import dataclass
from typing import Dict, Any
import os
from pathlib import Path


@dataclass
class DataConfig:
    """데이터 관련 설정"""
    # 파일 경로
    PROJECT_ROOT: Path = Path(__file__).parent.parent
    DATA_DIR: Path = PROJECT_ROOT / "data"
    PRICE_DATA_FILE: str = "BTCUSDT_15m.csv"
    TREND_DATA_FILE: str = "BTCUSDT_4h.csv"
    
    # 시간 프레임
    EXECUTION_TIMEFRAME: str = "15m"
    TREND_TIMEFRAME: str = "4h"
    
    # 데이터 분할
    TRAIN_TEST_SPLIT_DATE: str = "2024-01-01"


@dataclass
class TradingConfig:
    """거래 관련 설정"""
    # 레버리지 및 포지션
    LEVERAGE: int = 4
    MAX_POSITION_SIZE: float = 0.1  # 계좌 대비 10%
    
    # 손익절 설정 (가격 변동률 기준)
    STOP_LOSS_PCT: float = 0.02    # -2%
    TAKE_PROFIT_PCT: float = 0.05  # +5%
    
    # 거래 비용
    FEE_RATE: float = 0.0004       # 0.04% (Taker)
    SLIPPAGE_RATE: float = 0.0002  # 0.02%
    
    # 신호 임계값
    SIGNAL_THRESHOLDS: Dict[str, float] = None
    
    def __post_init__(self):
        if self.SIGNAL_THRESHOLDS is None:
            self.SIGNAL_THRESHOLDS = {
                'high_confidence': 0.6,    # 60% 이상: STRONG_BUY
                'medium_confidence': 0.5,  # 50% 이상: BUY  
                'low_confidence': 0.4,     # 40% 이상: WEAK_BUY
                'exit_threshold': 0.15     # 15% 미만: signal_exit
            }


@dataclass
class ModelConfig:
    """모델 관련 설정"""
    # LightGBM 기본 파라미터
    DEFAULT_PARAMS: Dict[str, Any] = None
    
    # 하이퍼파라미터 최적화
    OPTIMIZATION_TRIALS: int = 30
    EARLY_STOPPING_ROUNDS: int = 10
    
    # 모델 저장 경로
    MODEL_DIR: Path = Path(__file__).parent.parent / "models" / "saved"
    BINARY_MODEL_PATH: str = "binary_model.pkl"
    MULTICLASS_MODEL_PATH: str = "multiclass_model.pkl"
    
    def __post_init__(self):
        if self.DEFAULT_PARAMS is None:
            self.DEFAULT_PARAMS = {
                'objective': 'binary',
                'metric': 'binary_logloss',
                'boosting_type': 'gbdt',
                'num_leaves': 31,
                'learning_rate': 0.1,
                'feature_fraction': 0.9,
                'bagging_fraction': 0.8,
                'bagging_freq': 5,
                'min_child_samples': 20,
                'is_unbalance': True,
                'verbose': -1,
                'random_state': 42
            }


@dataclass
class FeatureConfig:
    """피처 엔지니어링 설정"""
    # 기술적 지표 파라미터
    RSI_PERIOD: int = 14
    MACD_FAST: int = 12
    MACD_SLOW: int = 26
    MACD_SIGNAL: int = 9
    BB_PERIOD: int = 20
    BB_STD: float = 2.0
    SMA_PERIODS: list = None
    
    # 상호작용 피처 설정
    ENABLE_INTERACTION_FEATURES: bool = True
    INTERACTION_FEATURES: list = None
    
    def __post_init__(self):
        if self.SMA_PERIODS is None:
            self.SMA_PERIODS = [20, 50, 200]
        
        if self.INTERACTION_FEATURES is None:
            self.INTERACTION_FEATURES = [
                'Volatility_Trend_Filter'  # 성능 검증된 피처만
            ]


@dataclass
class BacktestConfig:
    """백테스팅 설정"""
    # 초기 자본
    INITIAL_CAPITAL: float = 10000.0
    
    # 백테스팅 기간 (기본값)
    DEFAULT_START_DATE: str = "2022-05-01"
    DEFAULT_END_DATE: str = "2022-07-31"
    
    # 성과 지표 계산 설정
    RISK_FREE_RATE: float = 0.02  # 2% (연간)
    TRADING_DAYS_PER_YEAR: int = 365


@dataclass
class LoggingConfig:
    """로깅 설정"""
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_DIR: Path = Path(__file__).parent.parent / "logs"
    
    # 로그 파일 설정
    MAIN_LOG_FILE: str = "leviathan.log"
    BACKTEST_LOG_FILE: str = "backtest.log"
    MODEL_LOG_FILE: str = "model.log"


class Settings:
    """통합 설정 클래스"""
    
    def __init__(self):
        self.data = DataConfig()
        self.trading = TradingConfig()
        self.model = ModelConfig()
        self.feature = FeatureConfig()
        self.backtest = BacktestConfig()
        self.logging = LoggingConfig()
        
        # 디렉토리 생성
        self._create_directories()
    
    def _create_directories(self):
        """필요한 디렉토리 생성"""
        directories = [
            self.data.DATA_DIR,
            self.model.MODEL_DIR,
            self.logging.LOG_DIR
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def update_trading_config(self, **kwargs):
        """거래 설정 업데이트"""
        for key, value in kwargs.items():
            if hasattr(self.trading, key):
                setattr(self.trading, key, value)
            else:
                raise ValueError(f"Unknown trading config: {key}")
    
    def update_backtest_period(self, start_date: str, end_date: str):
        """백테스팅 기간 업데이트"""
        self.backtest.DEFAULT_START_DATE = start_date
        self.backtest.DEFAULT_END_DATE = end_date
    
    def get_model_path(self, model_type: str) -> Path:
        """모델 파일 경로 반환"""
        if model_type == "binary":
            return self.model.MODEL_DIR / self.model.BINARY_MODEL_PATH
        elif model_type == "multiclass":
            return self.model.MODEL_DIR / self.model.MULTICLASS_MODEL_PATH
        else:
            raise ValueError(f"Unknown model type: {model_type}")
    
    def get_data_path(self, data_type: str) -> Path:
        """데이터 파일 경로 반환"""
        if data_type == "price":
            return self.data.DATA_DIR / self.data.PRICE_DATA_FILE
        elif data_type == "trend":
            return self.data.DATA_DIR / self.data.TREND_DATA_FILE
        else:
            raise ValueError(f"Unknown data type: {data_type}")
    
    def to_dict(self) -> Dict[str, Any]:
        """설정을 딕셔너리로 변환"""
        return {
            'data': self.data.__dict__,
            'trading': self.trading.__dict__,
            'model': self.model.__dict__,
            'feature': self.feature.__dict__,
            'backtest': self.backtest.__dict__,
            'logging': self.logging.__dict__
        }
    
    def print_summary(self):
        """설정 요약 출력"""
        print("🐋 Project LEVIATHAN - 설정 요약")
        print("=" * 50)
        print(f"📊 데이터 분할: {self.data.TRAIN_TEST_SPLIT_DATE} 기준")
        print(f"⚖️ 레버리지: {self.trading.LEVERAGE}x")
        print(f"📉 손절매: -{self.trading.STOP_LOSS_PCT*100:.1f}%")
        print(f"📈 익절: +{self.trading.TAKE_PROFIT_PCT*100:.1f}%")
        print(f"💸 수수료: {self.trading.FEE_RATE*100:.2f}%")
        print(f"🧠 상호작용 피처: {len(self.feature.INTERACTION_FEATURES)}개")
        print(f"🎯 신호 임계값: {self.trading.SIGNAL_THRESHOLDS}")


# 전역 설정 인스턴스
settings = Settings()


def get_settings() -> Settings:
    """설정 인스턴스 반환"""
    return settings


if __name__ == "__main__":
    # 설정 테스트
    config = get_settings()
    config.print_summary()
    
    # 설정 업데이트 테스트
    config.update_trading_config(LEVERAGE=2, STOP_LOSS_PCT=0.015)
    print("\n📝 설정 업데이트 후:")
    config.print_summary()
