# Project LEVIATHAN - Git Ignore File

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
.venv/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Jupyter Notebook
.ipynb_checkpoints

# Environment variables
.env

# Logs
*.log
logs/*.log

# Data files (large datasets) - 바이낸스 데이터 포함
data/*.csv
data/*.parquet
data/*.h5
data/*.hdf5
data/raw/*.csv
data/processed/*.csv
!data/raw/.gitkeep
!data/processed/.gitkeep

# Model files (can be large)
models/*.pkl
models/*.joblib
models/*.h5
!models/.gitkeep

# Results (generated files)
results/*/
!results/.gitkeep

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# API Keys and Secrets
config_local.py
secrets.json
api_keys.txt
