#!/usr/bin/env python3
"""
🚀 Project LEVIATHAN - 간단한 공격적인 백테스팅
사용자 설정 기반 공격적인 거래 전략 백테스팅 (의존성 최소화)
"""

import pandas as pd
import numpy as np
import joblib
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass
from typing import Dict, List, Optional


@dataclass
class Trade:
    """거래 기록"""
    entry_time: str
    exit_time: str
    entry_price: float
    exit_price: float
    position_size: float
    pnl: float
    pnl_pct: float
    exit_reason: str


class SimpleAggressiveBacktester:
    """간단한 공격적인 백테스터"""
    
    def __init__(self, initial_capital: float = 10000):
        """초기화"""
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.current_position = None
        self.trades = []
        
        # 🚀 사용자 설정: 공격적인 거래 파라미터
        self.leverage = 4.0                    # 4배 레버리지
        self.stop_loss_pct = 0.02             # 2% 손절
        self.take_profit_pct = 0.05           # 5% 익절
        self.fee_rate = 0.0004                # 0.04% 수수료
        self.slippage = 0.0002                # 0.02% 슬리피지
        
        # 🚀 공격적인 포지션 관리
        self.max_position_size_pct = 1.0      # 자본의 100% 사용
        self.confidence_threshold = 0.55      # 55% 확신도 임계값
        
        print(f"🚀 간단한 공격적인 백테스터 초기화")
        print(f"   💰 초기 자본: ${initial_capital:,.0f}")
        print(f"   📊 레버리지: {self.leverage}배")
        print(f"   🎯 포지션 크기: 자본의 {self.max_position_size_pct*100:.0f}%")
        print(f"   ⚡ 확신도 임계값: {self.confidence_threshold:.1%}")
        
    def run_backtest(self, start_date: str = '2024-10-01', end_date: str = '2025-06-01') -> Dict:
        """백테스팅 실행"""
        print(f"\n🚀 공격적인 백테스팅 시작: {start_date} ~ {end_date}")
        
        try:
            # 1. 과적합 방지 검증
            self._validate_backtest_period(start_date, end_date)
            
            # 2. 데이터 로드
            price_data = self._load_price_data(start_date, end_date)
            
            # 3. 간단한 신호 생성 (더미 신호)
            signals = self._generate_dummy_signals(price_data)
            
            # 4. 백테스팅 실행
            self._execute_backtest(signals, price_data)
            
            # 5. 결과 분석
            results = self._analyze_results()
            
            # 6. 결과 출력
            self._print_results(results)
            
            return results
            
        except Exception as e:
            print(f"❌ 백테스팅 실패: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _validate_backtest_period(self, start_date: str, end_date: str):
        """과적합 방지 검증"""
        print(f"🔧 과적합 방지 검증 중...")
        
        model_train_end = pd.to_datetime('2024-09-20')
        backtest_start = pd.to_datetime(start_date)
        
        if backtest_start <= model_train_end:
            print(f"   ⚠️ 경고: 미래 데이터 참조 가능성!")
        else:
            print(f"   ✅ 미래 데이터 참조 없음")
            
        backtest_end = pd.to_datetime(end_date)
        backtest_days = (backtest_end - backtest_start).days
        print(f"   📊 백테스팅 기간: {backtest_days}일")
        
        total_cost = self.fee_rate + self.slippage
        print(f"   💰 총 거래 비용: {total_cost*100:.3f}%")
    
    def _load_price_data(self, start_date: str, end_date: str):
        """가격 데이터 로드"""
        print(f"📊 가격 데이터 로드 중...")
        
        try:
            price_data = pd.read_csv('data/BTCUSDT_15m.csv', 
                                   index_col='timestamp', parse_dates=True)
            
            # 백테스팅 기간 필터링
            mask = (price_data.index >= start_date) & (price_data.index <= end_date)
            price_data = price_data[mask]
            
            print(f"✅ 가격 데이터 로드: {len(price_data):,}개 포인트")
            return price_data
            
        except Exception as e:
            print(f"❌ 가격 데이터 로드 실패: {e}")
            raise
    
    def _generate_dummy_signals(self, price_data):
        """더미 신호 생성 (테스트용)"""
        print(f"🔮 더미 신호 생성 중...")
        
        # 간단한 기술적 지표 기반 신호
        signals = pd.DataFrame(index=price_data.index)
        
        # RSI 계산
        delta = price_data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        # 이동평균
        sma_20 = price_data['close'].rolling(20).mean()
        sma_50 = price_data['close'].rolling(50).mean()
        
        # 신호 생성 (RSI 과매도 + 상승 추세)
        signals['confidence'] = 0.0
        
        # RSI 30 이하이고 단기 이평이 장기 이평 위에 있을 때 높은 확신도
        oversold_uptrend = (rsi < 30) & (sma_20 > sma_50)
        signals.loc[oversold_uptrend, 'confidence'] = 0.7
        
        # RSI 30-50이고 상승 추세일 때 중간 확신도
        moderate_uptrend = (rsi >= 30) & (rsi < 50) & (sma_20 > sma_50)
        signals.loc[moderate_uptrend, 'confidence'] = 0.6
        
        # 기본 상승 추세일 때 낮은 확신도
        basic_uptrend = (sma_20 > sma_50) & (signals['confidence'] == 0)
        signals.loc[basic_uptrend, 'confidence'] = 0.4
        
        # 결측값 제거
        signals = signals.dropna()
        
        signal_count = (signals['confidence'] > self.confidence_threshold).sum()
        signal_rate = signal_count / len(signals) * 100
        
        print(f"✅ 더미 신호 생성 완료: {signal_count:,}개 ({signal_rate:.1f}%)")
        
        return signals
    
    def _execute_backtest(self, signals, price_data):
        """백테스팅 실행"""
        print(f"🚀 백테스팅 실행 중...")
        
        # 공통 인덱스로 정렬
        common_index = signals.index.intersection(price_data.index)
        signals_aligned = signals.loc[common_index]
        price_aligned = price_data.loc[common_index]
        
        for i in range(len(signals_aligned)):
            timestamp = signals_aligned.index[i]
            confidence = signals_aligned.iloc[i]['confidence']
            current_price = price_aligned.iloc[i]['close']
            high_price = price_aligned.iloc[i]['high']
            low_price = price_aligned.iloc[i]['low']
            
            # 신호 발생 시 진입
            if confidence > self.confidence_threshold and self.current_position is None:
                self._open_position(timestamp, current_price, confidence)
            
            # 포지션이 있을 때 손절/익절 체크
            elif self.current_position is not None:
                exit_reason = self._check_exit_conditions(current_price, high_price, low_price)
                if exit_reason:
                    self._close_position(timestamp, current_price, exit_reason)
        
        # 마지막 포지션 청산
        if self.current_position is not None:
            final_price = price_aligned.iloc[-1]['close']
            self._close_position(signals_aligned.index[-1], final_price, 'FINAL_EXIT')
        
        print(f"✅ 백테스팅 완료: {len(self.trades)}회 거래")
    
    def _open_position(self, timestamp, price, confidence):
        """포지션 진입"""
        # 🚀 공격적인 포지션 크기 계산
        base_size = self.current_capital * self.max_position_size_pct
        position_size = base_size * self.leverage
        
        # 거래 비용 계산
        total_cost = position_size * (self.fee_rate + self.slippage)
        
        # 자본 부족 체크
        if self.current_capital < total_cost:
            return
        
        self.current_capital -= total_cost
        
        self.current_position = {
            'entry_time': timestamp,
            'entry_price': price,
            'position_size': position_size,
            'confidence': confidence
        }
    
    def _check_exit_conditions(self, current_price, high_price, low_price):
        """청산 조건 체크"""
        if not self.current_position:
            return None
        
        entry_price = self.current_position['entry_price']
        
        # 손절 조건 (우선 체크)
        stop_loss_price = entry_price * (1 - self.stop_loss_pct)
        if low_price <= stop_loss_price:
            return 'STOP_LOSS'
        
        # 익절 조건
        take_profit_price = entry_price * (1 + self.take_profit_pct)
        if high_price >= take_profit_price:
            return 'TAKE_PROFIT'
        
        return None
    
    def _close_position(self, timestamp, price, exit_reason):
        """포지션 청산"""
        if not self.current_position:
            return
        
        position = self.current_position
        entry_price = position['entry_price']
        
        # 손익 계산
        price_change_pct = (price - entry_price) / entry_price
        pnl = position['position_size'] * price_change_pct
        
        # 거래 비용 차감
        total_cost = position['position_size'] * (self.fee_rate + self.slippage)
        net_pnl = pnl - total_cost
        
        # 자본 업데이트
        self.current_capital += net_pnl
        
        # 거래 기록
        trade = Trade(
            entry_time=str(position['entry_time']),
            exit_time=str(timestamp),
            entry_price=entry_price,
            exit_price=price,
            position_size=position['position_size'],
            pnl=net_pnl,
            pnl_pct=price_change_pct * 100,
            exit_reason=exit_reason
        )
        
        self.trades.append(trade)
        self.current_position = None
    
    def _analyze_results(self) -> Dict:
        """결과 분석"""
        if not self.trades:
            return {
                'error': '거래 기록이 없습니다.',
                'total_return': 0.0,
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': 0.0,
                'avg_win': 0.0,
                'avg_loss': 0.0,
                'profit_factor': 0.0,
                'max_drawdown': 0.0,
                'sharpe_ratio': 0.0,
                'final_capital': self.initial_capital
            }
        
        # 기본 통계
        total_trades = len(self.trades)
        winning_trades = len([t for t in self.trades if t.pnl > 0])
        losing_trades = len([t for t in self.trades if t.pnl <= 0])
        
        # 수익률 계산
        total_return = (self.current_capital - self.initial_capital) / self.initial_capital * 100
        
        # 평균 수익/손실
        wins = [t.pnl for t in self.trades if t.pnl > 0]
        losses = [t.pnl for t in self.trades if t.pnl <= 0]
        
        avg_win = np.mean(wins) if wins else 0
        avg_loss = np.mean(losses) if losses else 0
        
        # 손익비
        profit_factor = abs(sum(wins) / sum(losses)) if losses and sum(losses) != 0 else float('inf')
        
        # 최대 낙폭 계산
        equity_curve = [self.initial_capital]
        for trade in self.trades:
            equity_curve.append(equity_curve[-1] + trade.pnl)
        
        peak = equity_curve[0]
        max_drawdown = 0
        for equity in equity_curve:
            if equity > peak:
                peak = equity
            drawdown = (peak - equity) / peak * 100
            max_drawdown = max(max_drawdown, drawdown)
        
        # 샤프 비율 (간단 계산)
        returns = [t.pnl_pct for t in self.trades]
        if len(returns) > 1:
            sharpe_ratio = np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
        else:
            sharpe_ratio = 0
        
        return {
            'total_return': total_return,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': winning_trades / total_trades * 100 if total_trades > 0 else 0,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'final_capital': self.current_capital,
            'equity_curve': equity_curve
        }
    
    def _print_results(self, results: Dict):
        """결과 출력"""
        print(f"\n🏆 공격적인 백테스팅 결과")
        print(f"=" * 60)
        
        if 'error' in results:
            print(f"❌ {results['error']}")
            return
        
        print(f"💰 최종 자본: ${results['final_capital']:,.2f}")
        print(f"📈 총 수익률: {results['total_return']:+.2f}%")
        print(f"📊 총 거래: {results['total_trades']}회")
        print(f"🎯 승률: {results['win_rate']:.1f}% ({results['winning_trades']}승 {results['losing_trades']}패)")
        print(f"💚 평균 수익: ${results['avg_win']:+,.2f}")
        print(f"💔 평균 손실: ${results['avg_loss']:+,.2f}")
        print(f"💎 손익비: {results['profit_factor']:.2f}:1")
        print(f"📉 최대 낙폭: {results['max_drawdown']:.2f}%")
        print(f"⚡ 샤프 비율: {results['sharpe_ratio']:.2f}")


def main():
    """메인 실행 함수"""
    print("🚀 Project LEVIATHAN - 간단한 공격적인 백테스팅")
    print("=" * 70)
    
    try:
        # 백테스터 초기화 및 실행
        backtester = SimpleAggressiveBacktester(initial_capital=10000)
        result = backtester.run_backtest(
            start_date='2024-10-01',  # 훈련 데이터 이후
            end_date='2025-06-01'     # 현재까지
        )
        
        if result:
            print(f"\n🎯 다음 단계:")
            print(f"   - 실제 모델 예측 적용")
            print(f"   - Walk-Forward 검증")
            print(f"   - 숏 모델 개발")
        
    except Exception as e:
        print(f"❌ 메인 실행 실패: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
