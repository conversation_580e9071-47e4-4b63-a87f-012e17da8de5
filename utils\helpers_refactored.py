"""
Project LEVIATHAN - 리팩토링된 헬퍼 클래스들
백테스팅 엔진에서 사용하는 유틸리티 클래스들
"""

import pandas as pd
import numpy as np
from typing import Union, Optional, Tuple, Dict, Any
import logging


class DataValidator:
    """데이터 유효성 검증 클래스"""
    
    @staticmethod
    def validate_dataframe(df: pd.DataFrame, required_columns: list, name: str = "DataFrame") -> bool:
        """DataFrame 유효성 검증"""
        logger = logging.getLogger(__name__)
        
        # 빈 데이터 확인
        if df.empty:
            logger.error(f"{name}이 비어있습니다")
            return False
        
        # 필수 컬럼 확인
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logger.error(f"{name}에서 필수 컬럼이 누락되었습니다: {missing_columns}")
            return False
        
        # 결측값 확인
        null_counts = df[required_columns].isnull().sum()
        if null_counts.any():
            logger.warning(f"{name}에서 결측값이 발견되었습니다:\n{null_counts[null_counts > 0]}")
        
        return True
    
    @staticmethod
    def validate_price_data(price_data: pd.DataFrame) -> bool:
        """가격 데이터 유효성 검증"""
        required_columns = ['open', 'high', 'low', 'close']
        return DataValidator.validate_dataframe(price_data, required_columns, "Price Data")


class FinancialCalculator:
    """금융 계산 클래스"""
    
    @staticmethod
    def calculate_returns(prices: pd.Series, method: str = "simple") -> pd.Series:
        """수익률 계산"""
        if method == "simple":
            return prices.pct_change()
        elif method == "log":
            return np.log(prices / prices.shift(1))
        else:
            raise ValueError("method는 'simple' 또는 'log'여야 합니다")
    
    @staticmethod
    def calculate_sharpe_ratio(
        returns: pd.Series, 
        risk_free_rate: float = 0.02,
        periods_per_year: int = 365 * 24 * 4  # 15분봉 기준
    ) -> float:
        """샤프 비율 계산"""
        if len(returns) == 0 or returns.std() == 0:
            return 0.0
        
        # 연율화된 수익률과 변동성
        annual_return = returns.mean() * periods_per_year
        annual_volatility = returns.std() * np.sqrt(periods_per_year)
        
        # 샤프 비율
        sharpe = (annual_return - risk_free_rate) / annual_volatility
        return sharpe
    
    @staticmethod
    def calculate_max_drawdown(equity_curve: pd.Series) -> Dict[str, Any]:
        """최대 낙폭(MDD) 계산"""
        # 누적 최고점 계산
        peak = equity_curve.expanding().max()
        
        # 낙폭 계산
        drawdown = (equity_curve - peak) / peak
        
        # 최대 낙폭
        max_dd = drawdown.min()
        
        # 최대 낙폭이 발생한 지점
        max_dd_date = drawdown.idxmin()
        
        # 해당 낙폭의 고점 찾기
        peak_date = peak.loc[:max_dd_date].idxmax()
        
        return {
            'max_drawdown_pct': abs(max_dd) * 100,
            'peak_date': peak_date,
            'trough_date': max_dd_date,
            'drawdown_series': drawdown
        }
    
    @staticmethod
    def calculate_profit_factor(winning_pnl: pd.Series, losing_pnl: pd.Series) -> float:
        """수익 팩터 계산"""
        total_profits = winning_pnl.sum() if not winning_pnl.empty else 0
        total_losses = abs(losing_pnl.sum()) if not losing_pnl.empty else 0
        
        if total_losses == 0:
            return float('inf') if total_profits > 0 else 0.0
        
        return total_profits / total_losses
    
    @staticmethod
    def calculate_liquidation_price(
        entry_price: float,
        leverage: int,
        position_type: str,
        margin_ratio: float = 0.05
    ) -> float:
        """강제 청산 가격 계산"""
        if position_type.upper() == "LONG":
            liquidation_price = entry_price * (1 - (1/leverage) + margin_ratio)
        elif position_type.upper() == "SHORT":
            liquidation_price = entry_price * (1 + (1/leverage) - margin_ratio)
        else:
            raise ValueError("position_type은 'LONG' 또는 'SHORT'여야 합니다")
        
        return liquidation_price


class Formatter:
    """포맷팅 유틸리티 클래스"""
    
    @staticmethod
    def format_currency(amount: float, currency: str = "USD") -> str:
        """통화 형식으로 포맷팅"""
        if currency == "USD":
            return f"${amount:,.2f}"
        elif currency == "KRW":
            return f"₩{amount:,.0f}"
        else:
            return f"{amount:,.2f} {currency}"
    
    @staticmethod
    def format_percentage(value: float, decimals: int = 2) -> str:
        """퍼센트 형식으로 포맷팅"""
        return f"{value:.{decimals}f}%"
    
    @staticmethod
    def format_number(value: float, decimals: int = 2) -> str:
        """숫자 형식으로 포맷팅"""
        return f"{value:,.{decimals}f}"


class PerformanceReporter:
    """성과 보고서 생성 클래스"""
    
    @staticmethod
    def create_equity_curve(trades_df: pd.DataFrame, initial_capital: float) -> pd.Series:
        """자산 곡선 생성"""
        if trades_df.empty:
            return pd.Series([initial_capital])
        
        # 거래별 누적 손익
        cumulative_pnl = trades_df['pnl'].cumsum()
        equity_curve = initial_capital + cumulative_pnl
        
        # 시작점 추가
        equity_curve = pd.concat([
            pd.Series([initial_capital], index=[trades_df.index[0] - pd.Timedelta(days=1)]),
            equity_curve
        ])
        
        return equity_curve
    
    @staticmethod
    def generate_performance_summary(
        trades_df: pd.DataFrame, 
        equity_curve: pd.Series,
        initial_capital: float
    ) -> Dict[str, Any]:
        """성과 요약 생성"""
        if trades_df.empty:
            return {
                'total_return': 0.0,
                'total_trades': 0,
                'win_rate': 0.0,
                'avg_win': 0.0,
                'avg_loss': 0.0,
                'profit_factor': 0.0,
                'max_drawdown': 0.0,
                'sharpe_ratio': 0.0
            }
        
        # 기본 지표
        final_capital = equity_curve.iloc[-1]
        total_return = ((final_capital - initial_capital) / initial_capital) * 100
        
        # 거래 통계
        winning_trades = trades_df[trades_df['pnl'] > 0]
        losing_trades = trades_df[trades_df['pnl'] < 0]
        
        win_rate = len(winning_trades) / len(trades_df) * 100
        avg_win = winning_trades['pnl'].mean() if not winning_trades.empty else 0
        avg_loss = losing_trades['pnl'].mean() if not losing_trades.empty else 0
        
        # 손익비
        profit_factor = FinancialCalculator.calculate_profit_factor(
            winning_trades['pnl'], losing_trades['pnl']
        )
        
        # 리스크 지표
        returns = equity_curve.pct_change().dropna()
        max_dd_info = FinancialCalculator.calculate_max_drawdown(equity_curve)
        sharpe_ratio = FinancialCalculator.calculate_sharpe_ratio(returns)
        
        return {
            'initial_capital': initial_capital,
            'final_capital': final_capital,
            'total_return': total_return,
            'total_trades': len(trades_df),
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'max_drawdown': max_dd_info['max_drawdown_pct'],
            'sharpe_ratio': sharpe_ratio,
            'volatility': returns.std() * np.sqrt(365 * 24 * 4) * 100
        }


class SafeMath:
    """안전한 수학 연산 클래스"""
    
    @staticmethod
    def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
        """안전한 나눗셈"""
        if denominator == 0 or np.isnan(denominator) or np.isinf(denominator):
            return default
        return numerator / denominator
    
    @staticmethod
    def safe_percentage(value: float, total: float, default: float = 0.0) -> float:
        """안전한 퍼센트 계산"""
        if total == 0 or np.isnan(total) or np.isinf(total):
            return default
        return (value / total) * 100
    
    @staticmethod
    def clamp(value: float, min_val: float, max_val: float) -> float:
        """값을 범위 내로 제한"""
        return max(min_val, min(value, max_val))
    
    @staticmethod
    def is_valid_number(value: Union[int, float]) -> bool:
        """유효한 숫자인지 확인"""
        return not (np.isnan(value) or np.isinf(value))


if __name__ == "__main__":
    # 테스트 코드
    print("=== 리팩토링된 헬퍼 클래스 테스트 ===")
    
    # 샘플 데이터 생성
    dates = pd.date_range('2024-01-01', periods=100, freq='15T')
    prices = pd.Series(100 * (1 + np.random.randn(100).cumsum() * 0.02), index=dates)
    returns = FinancialCalculator.calculate_returns(prices)
    
    print(f"샤프 비율: {FinancialCalculator.calculate_sharpe_ratio(returns):.3f}")
    
    mdd_info = FinancialCalculator.calculate_max_drawdown(prices)
    print(f"최대 낙폭: {Formatter.format_percentage(mdd_info['max_drawdown_pct'])}")
    
    # 강제 청산가 테스트
    entry_price = 70000
    leverage = 10
    long_liq = FinancialCalculator.calculate_liquidation_price(entry_price, leverage, "LONG")
    
    print(f"진입가: {Formatter.format_currency(entry_price)}")
    print(f"롱 청산가: {Formatter.format_currency(long_liq)}")
    
    print("✅ 모든 테스트 통과!")
