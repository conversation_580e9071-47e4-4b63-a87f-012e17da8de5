# Project LEVIATHAN - Environment Variables Example
# 이 파일을 .env로 복사하고 실제 값으로 수정하세요

# ===== 거래 설정 =====
TRADING_SYMBOL=BTC-USDT
TRADING_TIMEFRAME=1d
TRADING_LEVERAGE=10
TRADING_MAX_POSITION_SIZE=0.1
TRADING_STOP_LOSS_PCT=0.05
TRADING_TAKE_PROFIT_PCT=0.15
TRADING_LIQUIDATION_BUFFER=0.2

# ===== 데이터 설정 =====
DATA_LOOKBACK_DAYS=1000
DATA_FEATURE_WINDOW=20

# ===== 백테스팅 설정 =====
BACKTEST_INITIAL_CAPITAL=100000.0
BACKTEST_MAKER_FEE=0.0002
BACKTEST_TAKER_FEE=0.0004

# Walk-Forward Analysis 설정
BACKTEST_TRAIN_START_DATE=2013-01-01
BACKTEST_TRAIN_END_DATE=2020-12-31
BACKTEST_TEST_START_DATE=2021-01-01
BACKTEST_TEST_END_DATE=2024-12-31

# 윈도우 전략 설정
BACKTEST_WINDOW_TYPE=hybrid
BACKTEST_SLIDING_WINDOW_YEARS=5
BACKTEST_RETRAIN_FREQUENCY=quarterly
BACKTEST_MIN_TRAIN_SAMPLES=1000

# 시장 체제 변화 감지
BACKTEST_REGIME_CHANGE_THRESHOLD=0.3
BACKTEST_VOLATILITY_LOOKBACK=60

# ===== 모니터링 설정 =====
MONITORING_DASHBOARD_PORT=8501
MONITORING_REFRESH_INTERVAL=30
MONITORING_LOG_LEVEL=INFO

# ===== 거래소 API (Phase 2, 3에서 사용) =====
# 주의: 실제 API 키는 절대 공개하지 마세요!
EXCHANGE_API_KEY=your_api_key_here
EXCHANGE_API_SECRET=your_api_secret_here
EXCHANGE_EXCHANGE_NAME=binance
EXCHANGE_TESTNET=true

# ===== 알림 설정 (Phase 3에서 사용) =====
MONITORING_TELEGRAM_BOT_TOKEN=your_telegram_bot_token
MONITORING_TELEGRAM_CHAT_ID=your_telegram_chat_id
