"""
Project LEVIATHAN - ART (Adaptive Risk Trading) 시스템
ML 기반 시장 분석과 동적 리스크 관리를 통합한 적응형 거래 시스템
"""

import pandas as pd
import numpy as np
from typing import Dict, Tuple, Optional, List
import warnings
warnings.filterwarnings('ignore')

from .market_analyzer import MarketAnalyzer
from .dynamic_risk_manager import DynamicRiskManager


class ARTSystem:
    """
    ART (Adaptive Risk Trading) 시스템
    
    핵심 기능:
    1. 실시간 시장 분석 및 분류
    2. 동적 레버리지 조정 (2-10배)
    3. 동적 손절/익절 최적화 (손절: 5%, 익절: 5-15%)
    4. 적응형 포지션 사이징 (30-100%)
    5. 시장 상황별 전략 자동 변경
    6. ML 신호와 리스크 관리 통합
    """
    
    def __init__(self, base_leverage: float = 4.0):
        """
        ART 시스템 초기화
        
        Args:
            base_leverage: 기본 레버리지 (4배)
        """
        self.market_analyzer = MarketAnalyzer()
        self.risk_manager = DynamicRiskManager(base_leverage)
        
        # ART 시스템 설정
        self.art_config = {
            'enable_dynamic_leverage': True,
            'enable_dynamic_stops': True,
            'enable_dynamic_position_sizing': True,
            'enable_market_adaptation': True,
            'min_confidence_threshold': 0.5,
            'max_confidence_threshold': 0.8
        }
        
        # 성과 추적
        self.performance_tracker = {
            'total_trades': 0,
            'successful_adaptations': 0,
            'market_condition_changes': 0,
            'risk_adjustments': 0
        }
        
        print("🎯 ART (Adaptive Risk Trading) 시스템 초기화 완료")
        print("=" * 60)
        print("🧠 시장 분석 엔진: 활성화")
        print("⚖️ 동적 리스크 관리: 활성화")
        print("🔄 적응형 전략 변경: 활성화")
        print("📊 실시간 성과 추적: 활성화")
        print("=" * 60)
    
    def analyze_and_adapt(self, data: pd.DataFrame, signals: pd.DataFrame) -> pd.DataFrame:
        """
        시장 분석 및 적응형 설정 생성
        
        Args:
            data: OHLCV + 피처 데이터
            signals: ML 거래 신호 데이터
            
        Returns:
            ART 적응형 설정이 포함된 데이터프레임
        """
        print("🎯 ART 시스템 분석 시작...")
        
        # 1. 시장 분석
        market_analysis = self.market_analyzer.analyze_market(data)
        
        # 2. 동적 리스크 설정 적용
        art_settings = self.risk_manager.apply_dynamic_settings_to_dataframe(
            market_analysis, signals)
        
        # 3. ML 신호와 통합
        integrated_signals = self._integrate_ml_signals(art_settings, signals)
        
        # 4. 최종 거래 결정
        final_decisions = self._make_trading_decisions(integrated_signals)
        
        print("✅ ART 시스템 분석 완료")
        
        return final_decisions
    
    def _integrate_ml_signals(self, art_settings: pd.DataFrame, 
                            signals: pd.DataFrame) -> pd.DataFrame:
        """
        ML 신호와 ART 설정 통합
        
        Args:
            art_settings: ART 동적 설정
            signals: ML 거래 신호
            
        Returns:
            통합된 신호 데이터프레임
        """
        print("🔗 ML 신호와 ART 설정 통합 중...")
        
        # 기본 데이터프레임 생성
        integrated = art_settings.copy()
        
        # ML 신호 데이터 추가
        for col in signals.columns:
            if col not in integrated.columns:
                integrated[col] = signals[col]
        
        # 동적 확신도 임계값 적용
        integrated['adjusted_confidence_threshold'] = integrated.apply(
            lambda row: self._calculate_dynamic_confidence_threshold(
                row['market_condition'], row.get('volatility', 0.03)), axis=1)
        
        # 신호 강도 조정
        integrated['adjusted_signal_strength'] = integrated.apply(
            lambda row: self._adjust_signal_strength(
                row.get('confidence', 0.5), row['market_condition']), axis=1)
        
        return integrated
    
    def _calculate_dynamic_confidence_threshold(self, market_condition: str, 
                                              volatility: float) -> float:
        """
        동적 확신도 임계값 계산
        
        Args:
            market_condition: 시장 상황
            volatility: 변동성
            
        Returns:
            조정된 확신도 임계값
        """
        base_thresholds = {
            'HIGH_RISK': 0.75,      # 고위험: 높은 임계값
            'BEAR_MARKET': 0.65,    # 하락장: 중간 임계값
            'SIDEWAYS': 0.60,       # 횡보장: 기본 임계값
            'LOW_VOLATILITY': 0.50, # 저변동성: 낮은 임계값
            'NORMAL': 0.60          # 일반: 기본 임계값
        }
        
        threshold = base_thresholds.get(market_condition, 0.60)
        
        # 변동성 기반 추가 조정
        if volatility > 0.05:  # 고변동성
            threshold += 0.05
        elif volatility < 0.02:  # 저변동성
            threshold -= 0.05
        
        return np.clip(threshold, self.art_config['min_confidence_threshold'],
                      self.art_config['max_confidence_threshold'])
    
    def _adjust_signal_strength(self, confidence: float, market_condition: str) -> float:
        """
        시장 상황에 따른 신호 강도 조정
        
        Args:
            confidence: 원본 확신도
            market_condition: 시장 상황
            
        Returns:
            조정된 신호 강도
        """
        # 시장 상황별 신호 강도 조정 계수
        adjustment_factors = {
            'HIGH_RISK': 0.8,       # 신호 강도 감소
            'BEAR_MARKET': 0.9,     # 약간 감소
            'SIDEWAYS': 1.0,        # 유지
            'LOW_VOLATILITY': 1.2,  # 증가
            'NORMAL': 1.1           # 약간 증가
        }
        
        factor = adjustment_factors.get(market_condition, 1.0)
        adjusted_confidence = confidence * factor
        
        return np.clip(adjusted_confidence, 0.0, 1.0)
    
    def _make_trading_decisions(self, integrated_signals: pd.DataFrame) -> pd.DataFrame:
        """
        최종 거래 결정 생성
        
        Args:
            integrated_signals: 통합된 신호 데이터
            
        Returns:
            최종 거래 결정 데이터프레임
        """
        print("🎯 최종 거래 결정 생성 중...")
        
        decisions = integrated_signals.copy()
        
        # 최종 거래 신호 생성
        decisions['art_signal'] = decisions.apply(self._generate_art_signal, axis=1)
        decisions['art_action'] = decisions.apply(self._determine_art_action, axis=1)
        decisions['art_position_size'] = decisions.apply(self._calculate_art_position_size, axis=1)
        
        # 리스크 메트릭 계산
        decisions['risk_reward_ratio'] = decisions.apply(self._calculate_risk_reward_ratio, axis=1)
        decisions['expected_return'] = decisions.apply(self._calculate_expected_return, axis=1)
        
        return decisions
    
    def _generate_art_signal(self, row: pd.Series) -> str:
        """
        ART 신호 생성
        
        Args:
            row: 데이터 행
            
        Returns:
            ART 신호 ('STRONG_BUY', 'BUY', 'HOLD', 'SELL', 'STRONG_SELL')
        """
        confidence = row.get('adjusted_signal_strength', 0.5)
        threshold = row.get('adjusted_confidence_threshold', 0.6)
        market_condition = row.get('market_condition', 'NORMAL')
        
        # 기본 신호 결정
        if confidence >= threshold + 0.1:
            base_signal = 'STRONG_BUY'
        elif confidence >= threshold:
            base_signal = 'BUY'
        elif confidence <= (1 - threshold - 0.1):
            base_signal = 'STRONG_SELL'
        elif confidence <= (1 - threshold):
            base_signal = 'SELL'
        else:
            base_signal = 'HOLD'
        
        # 시장 상황별 신호 조정
        if market_condition == 'HIGH_RISK' and base_signal in ['STRONG_BUY', 'BUY']:
            base_signal = 'HOLD'  # 고위험 시장에서는 보수적
        elif market_condition == 'LOW_VOLATILITY' and base_signal == 'BUY':
            base_signal = 'STRONG_BUY'  # 저변동성에서는 적극적
        
        return base_signal
    
    def _determine_art_action(self, row: pd.Series) -> str:
        """
        ART 액션 결정
        
        Args:
            row: 데이터 행
            
        Returns:
            거래 액션 ('LONG', 'SHORT', 'HOLD', 'EXIT')
        """
        signal = row.get('art_signal', 'HOLD')
        
        action_map = {
            'STRONG_BUY': 'LONG',
            'BUY': 'LONG',
            'HOLD': 'HOLD',
            'SELL': 'SHORT',
            'STRONG_SELL': 'SHORT'
        }
        
        return action_map.get(signal, 'HOLD')
    
    def _calculate_art_position_size(self, row: pd.Series) -> float:
        """
        ART 포지션 크기 계산
        
        Args:
            row: 데이터 행
            
        Returns:
            포지션 크기 (USD)
        """
        base_capital = 10000  # 기본 자본 (설정 가능)
        position_pct = row.get('dynamic_position_size_pct', 0.5)
        leverage = row.get('dynamic_leverage', 4.0)
        
        position_size = base_capital * position_pct * leverage
        
        return round(position_size, 2)
    
    def _calculate_risk_reward_ratio(self, row: pd.Series) -> float:
        """
        리스크 리워드 비율 계산
        
        Args:
            row: 데이터 행
            
        Returns:
            리스크 리워드 비율
        """
        stop_loss = row.get('dynamic_stop_loss_pct', 0.05)
        take_profit_max = row.get('dynamic_take_profit_max', 0.10)
        
        if stop_loss > 0:
            return round(take_profit_max / stop_loss, 2)
        else:
            return 2.0  # 기본값
    
    def _calculate_expected_return(self, row: pd.Series) -> float:
        """
        기대 수익률 계산
        
        Args:
            row: 데이터 행
            
        Returns:
            기대 수익률
        """
        confidence = row.get('adjusted_signal_strength', 0.5)
        take_profit_max = row.get('dynamic_take_profit_max', 0.10)
        leverage = row.get('dynamic_leverage', 4.0)
        
        # 기대 수익률 = 확신도 × 최대 익절 × 레버리지
        expected_return = confidence * take_profit_max * leverage
        
        return round(expected_return, 4)
    
    def get_art_summary(self, art_results: pd.DataFrame) -> Dict[str, any]:
        """
        ART 시스템 성과 요약
        
        Args:
            art_results: ART 결과 데이터프레임
            
        Returns:
            성과 요약 딕셔너리
        """
        summary = {
            'total_signals': len(art_results),
            'market_conditions': art_results['market_condition'].value_counts().to_dict(),
            'signal_distribution': art_results['art_signal'].value_counts().to_dict(),
            'average_leverage': art_results['dynamic_leverage'].mean(),
            'average_position_size': art_results['dynamic_position_size_pct'].mean(),
            'average_risk_reward': art_results['risk_reward_ratio'].mean(),
            'average_expected_return': art_results['expected_return'].mean()
        }
        
        return summary
    
    def print_art_summary(self, art_results: pd.DataFrame):
        """
        ART 시스템 성과 요약 출력
        
        Args:
            art_results: ART 결과 데이터프레임
        """
        summary = self.get_art_summary(art_results)
        
        print("\n🎯 ART 시스템 성과 요약")
        print("=" * 50)
        print(f"📊 총 신호 수: {summary['total_signals']:,}개")
        print(f"⚖️ 평균 레버리지: {summary['average_leverage']:.1f}배")
        print(f"💰 평균 포지션: {summary['average_position_size']*100:.1f}%")
        print(f"📈 평균 리스크 리워드: {summary['average_risk_reward']:.2f}:1")
        print(f"🎯 평균 기대 수익률: {summary['average_expected_return']*100:.2f}%")
        
        print("\n📊 시장 상황 분포:")
        for condition, count in summary['market_conditions'].items():
            pct = count / summary['total_signals'] * 100
            print(f"   {condition}: {count}개 ({pct:.1f}%)")
        
        print("\n🎯 신호 분포:")
        for signal, count in summary['signal_distribution'].items():
            pct = count / summary['total_signals'] * 100
            print(f"   {signal}: {count}개 ({pct:.1f}%)")


if __name__ == "__main__":
    # 테스트 코드
    print("🧪 ART 시스템 테스트")
    
    # 샘플 데이터 생성
    dates = pd.date_range('2024-01-01', periods=100, freq='15min')
    sample_data = pd.DataFrame({
        'close': np.random.randn(100).cumsum() + 50000,
        'high': np.random.randn(100).cumsum() + 50100,
        'low': np.random.randn(100).cumsum() + 49900,
        'volume': np.random.randint(1000, 10000, 100)
    }, index=dates)
    
    sample_signals = pd.DataFrame({
        'confidence': np.random.uniform(0.3, 0.9, 100),
        'signal': np.random.choice(['BUY', 'SELL', 'HOLD'], 100)
    }, index=dates)
    
    # ART 시스템 테스트
    art_system = ARTSystem()
    art_results = art_system.analyze_and_adapt(sample_data, sample_signals)
    
    # 결과 요약
    art_system.print_art_summary(art_results)
    
    print(f"\n📈 ART 결과 샘플:")
    print(art_results[['market_condition', 'art_signal', 'dynamic_leverage', 
                      'dynamic_position_size_pct', 'risk_reward_ratio']].head())
