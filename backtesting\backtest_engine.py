"""
Project LEVIATHAN - 리팩토링된 백테스팅 엔진
깔끔하고 모듈화된 백테스팅 시스템
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import logging

from config.settings import get_settings
from utils.helpers_refactored import (
    DataValidator, FinancialCalculator, Formatter, 
    PerformanceReporter, SafeMath
)

# 설정 로드
settings = get_settings()


@dataclass
class Trade:
    """거래 정보를 담는 데이터 클래스"""
    entry_time: pd.Timestamp
    exit_time: pd.Timestamp
    entry_price: float
    exit_price: float
    position_size: float
    pnl: float
    pnl_pct: float
    exit_reason: str
    confidence: float
    signal_type: str


@dataclass
class BacktestResult:
    """백테스팅 결과를 담는 데이터 클래스"""
    trades: list
    equity_curve: pd.Series
    performance_metrics: Dict[str, Any]
    period_start: str
    period_end: str
    total_bars: int


class TradingSignalGenerator:
    """거래 신호 생성기"""
    
    def __init__(self):
        self.thresholds = settings.trading.SIGNAL_THRESHOLDS
    
    def generate_signal(self, confidence: float) -> Tuple[str, str]:
        """
        신뢰도 기반 거래 신호 생성
        
        Args:
            confidence: 익절 확률 (0.0 ~ 1.0)
            
        Returns:
            (신호 타입, 신호 강도)
        """
        if confidence >= self.thresholds['high_confidence']:
            return 'BUY', 'STRONG_BUY'
        elif confidence >= self.thresholds['medium_confidence']:
            return 'BUY', 'BUY'
        elif confidence >= self.thresholds['low_confidence']:
            return 'BUY', 'WEAK_BUY'
        else:
            return 'HOLD', 'HOLD'
    
    def should_exit(self, confidence: float, current_signal: str) -> bool:
        """
        청산 조건 확인
        
        Args:
            confidence: 현재 익절 확률
            current_signal: 현재 신호
            
        Returns:
            청산 여부
        """
        return (current_signal == 'HOLD' and 
                confidence < self.thresholds['exit_threshold'])


class PositionManager:
    """포지션 관리자"""
    
    def __init__(self, initial_capital: float = None):
        self.initial_capital = initial_capital or settings.backtest.INITIAL_CAPITAL
        self.current_capital = self.initial_capital
        self.current_position = None
        self.leverage = settings.trading.LEVERAGE
        self.max_position_size = settings.trading.MAX_POSITION_SIZE
        
    def calculate_position_size(self, price: float) -> float:
        """
        포지션 크기 계산
        
        Args:
            price: 진입 가격
            
        Returns:
            포지션 크기 (USD)
        """
        max_position_value = self.current_capital * self.max_position_size
        position_size = max_position_value * self.leverage
        return position_size
    
    def open_position(self, entry_time: pd.Timestamp, entry_price: float, 
                     confidence: float, signal_type: str) -> bool:
        """
        포지션 진입
        
        Args:
            entry_time: 진입 시간
            entry_price: 진입 가격
            confidence: 신뢰도
            signal_type: 신호 타입
            
        Returns:
            진입 성공 여부
        """
        if self.current_position is not None:
            return False
        
        position_size = self.calculate_position_size(entry_price)
        
        # 진입 수수료 계산 (거래 금액 기준)
        trade_value = position_size * entry_price
        entry_fee = trade_value * settings.trading.FEE_RATE

        # 진입 슬리피지 계산 (거래 금액 기준)
        entry_slippage = trade_value * settings.trading.SLIPPAGE_RATE

        # 총 진입 비용
        total_entry_cost = entry_fee + entry_slippage
        self.current_capital -= total_entry_cost
        
        self.current_position = {
            'entry_time': entry_time,
            'entry_price': entry_price,
            'position_size': position_size,
            'confidence': confidence,
            'signal_type': signal_type,
            'entry_fee': entry_fee,
            'entry_slippage': entry_slippage,
            'total_entry_cost': total_entry_cost
        }
        
        return True
    
    def close_position(self, exit_time: pd.Timestamp, exit_price: float, 
                      exit_reason: str) -> Optional[Trade]:
        """
        포지션 청산
        
        Args:
            exit_time: 청산 시간
            exit_price: 청산 가격
            exit_reason: 청산 사유
            
        Returns:
            거래 정보 또는 None
        """
        if self.current_position is None:
            return None
        
        position = self.current_position
        
        # 수익률 계산 (레버리지 적용)
        price_change = (exit_price - position['entry_price']) / position['entry_price']
        leveraged_return = price_change * self.leverage
        
        # 총 손익 계산 (레버리지 적용 전)
        gross_pnl = position['position_size'] * leveraged_return

        # 청산 비용 계산
        exit_trade_value = position['position_size'] * exit_price
        exit_fee = exit_trade_value * settings.trading.FEE_RATE
        exit_slippage = exit_trade_value * settings.trading.SLIPPAGE_RATE

        # 총 거래 비용 (진입 + 청산)
        total_fees = position['entry_fee'] + exit_fee
        total_slippage = position['entry_slippage'] + exit_slippage
        total_costs = total_fees + total_slippage

        # 최종 순손익 (모든 비용 차감)
        net_pnl = gross_pnl - total_costs
        
        # 자본 업데이트
        self.current_capital += net_pnl
        
        # 거래 정보 생성
        trade = Trade(
            entry_time=position['entry_time'],
            exit_time=exit_time,
            entry_price=position['entry_price'],
            exit_price=exit_price,
            position_size=position['position_size'],
            pnl=net_pnl,
            pnl_pct=leveraged_return * 100,
            exit_reason=exit_reason,
            confidence=position['confidence'],
            signal_type=position['signal_type']
        )
        
        # 포지션 초기화
        self.current_position = None
        
        return trade


class BacktestEngine:
    """리팩토링된 백테스팅 엔진"""
    
    def __init__(self, initial_capital: float = None):
        self.signal_generator = TradingSignalGenerator()
        self.position_manager = PositionManager(initial_capital)
        self.trades = []
        
        # 설정값들
        self.stop_loss_pct = settings.trading.STOP_LOSS_PCT
        self.take_profit_pct = settings.trading.TAKE_PROFIT_PCT
        
        logging.info("🚀 백테스팅 엔진 초기화 완료")
    
    def run_backtest(self, predictions: pd.DataFrame, price_data: pd.DataFrame,
                    start_date: str = None, end_date: str = None) -> BacktestResult:
        """
        백테스팅 실행
        
        Args:
            predictions: 모델 예측 결과 (timestamp, confidence)
            price_data: 가격 데이터 (timestamp, open, high, low, close)
            start_date: 시작 날짜
            end_date: 종료 날짜
            
        Returns:
            백테스팅 결과
        """
        # 데이터 유효성 검사
        if not self._validate_inputs(predictions, price_data):
            raise ValueError("입력 데이터 유효성 검사 실패")
        
        # 기간 필터링
        if start_date or end_date:
            predictions, price_data = self._filter_by_date(
                predictions, price_data, start_date, end_date
            )
        
        # 데이터 정렬 및 정합성 확인
        aligned_data = self._align_data(predictions, price_data)
        
        logging.info(f"📊 백테스팅 시작: {len(aligned_data)}개 데이터 포인트")
        
        # 메인 백테스팅 루프
        for i, (timestamp, row) in enumerate(aligned_data.iterrows()):
            self._process_timestamp(timestamp, row, aligned_data, i)
        
        # 마지막 포지션 강제 청산 (수정된 버전)
        if self.position_manager.current_position:
            # ❌ 기존: 현재 캔들의 종가 사용 (Lookahead Bias)
            # ✅ 수정: 현실적인 청산 - 마지막 캔들에서는 청산 불가
            # 실제 거래에서는 다음 캔들이 없으면 포지션을 유지하거나
            # 시장가로 즉시 청산해야 함

            last_timestamp = aligned_data.index[-1]
            last_candle = aligned_data.iloc[-1]

            # 현실적인 청산 가격: 마지막 캔들의 종가 (시장 마감 시 청산)
            # 이는 불가피한 경우로, 실제로는 다음 거래일 시가에 청산됨
            emergency_exit_price = last_candle['close']

            trade = self.position_manager.close_position(
                last_timestamp, emergency_exit_price, 'emergency_exit'
            )
            if trade:
                self.trades.append(trade)
                logging.warning(f"긴급 청산: {last_timestamp} @ ${emergency_exit_price:,.2f} (백테스팅 종료)")
        
        # 결과 생성
        return self._generate_result(aligned_data, start_date, end_date)
    
    def _validate_inputs(self, predictions: pd.DataFrame, price_data: pd.DataFrame) -> bool:
        """입력 데이터 유효성 검사"""
        pred_valid = DataValidator.validate_dataframe(
            predictions, ['confidence'], "Predictions"
        )
        price_valid = DataValidator.validate_price_data(price_data)
        
        return pred_valid and price_valid
    
    def _filter_by_date(self, predictions: pd.DataFrame, price_data: pd.DataFrame,
                       start_date: str, end_date: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """날짜 기준 데이터 필터링"""
        if start_date:
            predictions = predictions[predictions.index >= start_date]
            price_data = price_data[price_data.index >= start_date]
        
        if end_date:
            predictions = predictions[predictions.index <= end_date]
            price_data = price_data[price_data.index <= end_date]
        
        return predictions, price_data
    
    def _align_data(self, predictions: pd.DataFrame, price_data: pd.DataFrame) -> pd.DataFrame:
        """데이터 정렬 및 병합"""
        # 공통 인덱스 찾기
        common_index = predictions.index.intersection(price_data.index)
        
        # 데이터 병합
        aligned = pd.concat([
            predictions.loc[common_index],
            price_data.loc[common_index]
        ], axis=1)
        
        # 시간 순서 정렬
        aligned = aligned.sort_index()
        
        logging.info(f"✅ 데이터 정렬 완료: {len(aligned)}개 포인트")
        return aligned
    
    def _process_timestamp(self, timestamp: pd.Timestamp, row: pd.Series,
                          data: pd.DataFrame, index: int):
        """
        각 시점 처리 (Lookahead Bias 완전 제거 버전)

        핵심 원칙:
        1. 현재 시점에서는 신호만 생성
        2. 실제 거래는 다음 캔들에서 체결
        3. 거래 불가능한 경우 스킵
        """
        confidence = row['confidence']

        # 현실적인 거래 가격 계산 (다음 캔들 시가)
        execution_price = self._get_realistic_price(timestamp, data, index)

        # 거래 불가능한 경우 (마지막 캔들 등) 스킵
        if execution_price is None:
            logging.debug(f"거래 불가: {timestamp} (다음 캔들 없음)")
            return

        # 신호 생성 (현재 시점 정보만 사용)
        signal_action, signal_type = self.signal_generator.generate_signal(confidence)

        # 포지션이 없는 경우: 진입 검토
        if self.position_manager.current_position is None:
            if signal_action == 'BUY':
                success = self.position_manager.open_position(
                    timestamp, execution_price, confidence, signal_type
                )
                if success:
                    logging.debug(f"포지션 진입: {timestamp} @ ${execution_price:,.2f}")

        # 포지션이 있는 경우: 청산 검토
        else:
            exit_reason = self._check_exit_conditions(
                execution_price, confidence, signal_type, row
            )

            if exit_reason:
                trade = self.position_manager.close_position(
                    timestamp, execution_price, exit_reason
                )
                if trade:
                    self.trades.append(trade)
                    logging.debug(f"포지션 청산: {timestamp} @ ${execution_price:,.2f} ({exit_reason})")
    
    def _get_realistic_price(self, timestamp: pd.Timestamp, data: pd.DataFrame,
                           index: int) -> Optional[float]:
        """
        현실적인 거래 가격 계산 (Lookahead Bias 완전 제거)

        핵심 원칙: 모든 거래는 다음 캔들의 시가에서만 체결
        """
        # 다음 캔들의 시가 사용 (현실적이고 유일한 올바른 방법)
        if index + 1 < len(data):
            next_timestamp = data.index[index + 1]
            next_open = data.loc[next_timestamp, 'open']

            # 디버깅용 로그 (필요시 활성화)
            # logging.debug(f"거래 체결: {timestamp} -> 다음 캔들 {next_timestamp} 시가 ${next_open:,.2f}")

            return next_open
        else:
            # 마지막 데이터인 경우: 거래 불가 (None 반환)
            # 현실에서는 다음 캔들이 없으면 거래할 수 없음
            return None
    
    def _check_exit_conditions(self, current_price: float, confidence: float,
                              signal_type: str, row: pd.Series) -> Optional[str]:
        """청산 조건 확인"""
        position = self.position_manager.current_position
        entry_price = position['entry_price']
        
        # 가격 변화율 계산
        price_change = (current_price - entry_price) / entry_price
        
        # 손절 조건
        if price_change <= -self.stop_loss_pct:
            return 'stop_loss'
        
        # 익절 조건
        if price_change >= self.take_profit_pct:
            return 'take_profit'
        
        # 신호 기반 청산
        if self.signal_generator.should_exit(confidence, signal_type):
            return 'signal_exit'
        
        return None
    
    def _generate_result(self, data: pd.DataFrame, start_date: str, 
                        end_date: str) -> BacktestResult:
        """백테스팅 결과 생성"""
        # 거래 데이터프레임 생성
        trades_df = pd.DataFrame([trade.__dict__ for trade in self.trades])
        
        # 자산 곡선 생성
        equity_curve = PerformanceReporter.create_equity_curve(
            trades_df, self.position_manager.initial_capital
        )
        
        # 성과 지표 계산
        performance_metrics = self._calculate_performance_metrics(trades_df, equity_curve)
        
        return BacktestResult(
            trades=self.trades,
            equity_curve=equity_curve,
            performance_metrics=performance_metrics,
            period_start=start_date or str(data.index[0].date()),
            period_end=end_date or str(data.index[-1].date()),
            total_bars=len(data)
        )
    
    def _calculate_performance_metrics(self, trades_df: pd.DataFrame, 
                                     equity_curve: pd.Series) -> Dict[str, Any]:
        """성과 지표 계산"""
        if trades_df.empty:
            return {
                'total_return': 0.0,
                'total_trades': 0,
                'win_rate': 0.0,
                'avg_win': 0.0,
                'avg_loss': 0.0,
                'profit_factor': 0.0,
                'max_drawdown': 0.0,
                'sharpe_ratio': 0.0
            }
        
        # 기본 지표
        initial_capital = self.position_manager.initial_capital
        final_capital = self.position_manager.current_capital
        total_return = ((final_capital - initial_capital) / initial_capital) * 100
        
        # 거래 통계
        winning_trades = trades_df[trades_df['pnl'] > 0]
        losing_trades = trades_df[trades_df['pnl'] < 0]
        
        win_rate = len(winning_trades) / len(trades_df) * 100
        avg_win = winning_trades['pnl'].mean() if not winning_trades.empty else 0
        avg_loss = losing_trades['pnl'].mean() if not losing_trades.empty else 0
        
        # 손익비
        profit_factor = FinancialCalculator.calculate_profit_factor(
            winning_trades['pnl'], losing_trades['pnl']
        )
        
        # 리스크 지표
        returns = equity_curve.pct_change().dropna()
        max_dd_info = FinancialCalculator.calculate_max_drawdown(equity_curve)
        sharpe_ratio = FinancialCalculator.calculate_sharpe_ratio(returns)
        
        return {
            'initial_capital': initial_capital,
            'final_capital': final_capital,
            'total_return': total_return,
            'total_trades': len(trades_df),
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'max_drawdown': max_dd_info['max_drawdown_pct'],
            'sharpe_ratio': sharpe_ratio,
            'volatility': returns.std() * np.sqrt(365 * 24 * 4) * 100  # 연간화된 변동성
        }
