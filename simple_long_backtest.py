#!/usr/bin/env python3
"""
🧠 Project LEVIATHAN - 간단한 롱 모델 백테스팅
롱 모델만으로 스윙 v2.0 백테스팅 실행
"""

import pandas as pd
import numpy as np
import joblib
from pathlib import Path
import sys
from datetime import datetime

# 프로젝트 루트 추가
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import get_config

def simple_long_backtest():
    """간단한 롱 모델 백테스팅"""
    print("🧠 Project LEVIATHAN - 간단한 롱 모델 백테스팅")
    print("=" * 60)
    
    # 설정 로드
    config = get_config('swing_s_v2')
    print(f"✅ 설정: {config.strategy_name} {config.version}")
    print(f"📝 설명: {config.description}")
    
    try:
        # 1. 모델 로드
        model_path = "models/swing_s_v2_long_model_final_v2.pkl"
        model = joblib.load(model_path)
        print(f"✅ 롱 모델 로드: {model_path}")
        
        # 2. 피처 데이터 로드
        features_path = "data/processed/features_swing_enhanced_optimized.csv"
        features_df = pd.read_csv(features_path, index_col='timestamp', parse_dates=True)
        print(f"✅ 피처 데이터 로드: {features_df.shape}")
        
        # 3. 백테스팅 기간 필터링 (미래 데이터 참조 방지)
        start_date = '2024-10-01'  # 훈련 데이터 이후
        end_date = '2025-06-01'    # 현재까지
        
        mask = (features_df.index >= start_date) & (features_df.index <= end_date)
        backtest_features = features_df[mask]
        print(f"📅 백테스팅 기간: {start_date} ~ {end_date}")
        print(f"📊 백테스팅 데이터: {backtest_features.shape}")
        
        # 4. 가격 데이터 로드
        price_data = pd.read_csv('data/BTCUSDT_15m.csv', index_col='timestamp', parse_dates=True)
        price_mask = (price_data.index >= start_date) & (price_data.index <= end_date)
        backtest_prices = price_data[price_mask]
        print(f"📊 가격 데이터: {backtest_prices.shape}")
        
        # 5. 공통 인덱스로 정렬
        common_index = backtest_features.index.intersection(backtest_prices.index)
        features_aligned = backtest_features.loc[common_index]
        prices_aligned = backtest_prices.loc[common_index]
        
        print(f"📊 정렬된 데이터: {len(common_index):,}개 포인트")
        
        # 6. 모델 예측
        print("🔮 모델 예측 중...")
        predictions = model.predict(features_aligned)
        
        # 7. 신호 생성 (더 엄격한 임계값)
        threshold = 0.75  # 더 높은 확신도 요구
        signals = (predictions > threshold).astype(int)
        
        signal_count = signals.sum()
        print(f"📊 생성된 신호: {signal_count:,}개 ({signal_count/len(signals)*100:.1f}%)")
        
        # 8. 간단한 백테스팅 실행
        print("🚀 백테스팅 실행 중...")
        
        # 초기 설정 (현실적으로 조정)
        initial_capital = 10000  # $10,000
        current_capital = initial_capital
        leverage = 2.0  # 2배 레버리지 (보수적)
        stop_loss_pct = 0.03  # 3% 손절 (보수적)
        take_profit_pct = 0.06  # 6% 익절 (보수적)
        fee_rate = 0.0004  # 0.04% 수수료 (바이낸스 현물)
        position_size_pct = 0.10  # 자본의 10%만 사용
        
        trades = []
        current_position = None
        
        for i in range(len(signals)):
            timestamp = common_index[i]
            signal = signals[i]
            current_price = prices_aligned.iloc[i]['close']
            
            # 신호 발생 시 진입 (현실적 포지션 크기)
            if signal == 1 and current_position is None:
                # 롱 포지션 진입 (자본의 10%만 사용)
                base_position = current_capital * position_size_pct
                position_size = base_position * leverage
                entry_fee = position_size * fee_rate
                
                current_position = {
                    'entry_time': timestamp,
                    'entry_price': current_price,
                    'position_size': position_size,
                    'type': 'LONG'
                }
                current_capital -= entry_fee
                
            # 포지션이 있을 때 손절/익절 체크
            elif current_position is not None:
                entry_price = current_position['entry_price']
                
                # 수익률 계산
                pnl_pct = (current_price - entry_price) / entry_price
                
                # 손절 조건
                if pnl_pct <= -stop_loss_pct:
                    # 손절 실행
                    pnl = current_position['position_size'] * pnl_pct
                    exit_fee = current_position['position_size'] * fee_rate
                    net_pnl = pnl - exit_fee
                    
                    current_capital += net_pnl
                    
                    trades.append({
                        'entry_time': current_position['entry_time'],
                        'exit_time': timestamp,
                        'entry_price': entry_price,
                        'exit_price': current_price,
                        'pnl_pct': pnl_pct,
                        'net_pnl': net_pnl,
                        'exit_reason': 'STOP_LOSS'
                    })
                    
                    current_position = None
                
                # 익절 조건
                elif pnl_pct >= take_profit_pct:
                    # 익절 실행
                    pnl = current_position['position_size'] * pnl_pct
                    exit_fee = current_position['position_size'] * fee_rate
                    net_pnl = pnl - exit_fee
                    
                    current_capital += net_pnl
                    
                    trades.append({
                        'entry_time': current_position['entry_time'],
                        'exit_time': timestamp,
                        'entry_price': entry_price,
                        'exit_price': current_price,
                        'pnl_pct': pnl_pct,
                        'net_pnl': net_pnl,
                        'exit_reason': 'TAKE_PROFIT'
                    })
                    
                    current_position = None
        
        # 마지막 포지션 청산
        if current_position is not None:
            final_price = prices_aligned.iloc[-1]['close']
            entry_price = current_position['entry_price']
            pnl_pct = (final_price - entry_price) / entry_price
            pnl = current_position['position_size'] * pnl_pct
            exit_fee = current_position['position_size'] * fee_rate
            net_pnl = pnl - exit_fee
            
            current_capital += net_pnl
            
            trades.append({
                'entry_time': current_position['entry_time'],
                'exit_time': common_index[-1],
                'entry_price': entry_price,
                'exit_price': final_price,
                'pnl_pct': pnl_pct,
                'net_pnl': net_pnl,
                'exit_reason': 'FINAL_EXIT'
            })
        
        # 9. 결과 분석
        print("\n📊 백테스팅 결과 분석:")
        print("=" * 50)
        
        if trades:
            trades_df = pd.DataFrame(trades)
            
            # 기본 통계
            total_trades = len(trades_df)
            winning_trades = len(trades_df[trades_df['net_pnl'] > 0])
            losing_trades = len(trades_df[trades_df['net_pnl'] <= 0])
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # 수익률 계산
            total_return = (current_capital - initial_capital) / initial_capital
            
            # 평균 수익/손실
            avg_win = trades_df[trades_df['net_pnl'] > 0]['net_pnl'].mean() if winning_trades > 0 else 0
            avg_loss = trades_df[trades_df['net_pnl'] <= 0]['net_pnl'].mean() if losing_trades > 0 else 0
            
            print(f"💰 최종 자본: ${current_capital:,.2f}")
            print(f"📈 총 수익률: {total_return:.2%}")
            print(f"🎯 총 거래 수: {total_trades}회")
            print(f"🏆 승률: {win_rate:.1%} ({winning_trades}승 {losing_trades}패)")
            print(f"💚 평균 수익: ${avg_win:,.2f}")
            print(f"💔 평균 손실: ${avg_loss:,.2f}")
            
            # 거래 유형별 분석
            exit_reasons = trades_df['exit_reason'].value_counts()
            print(f"\n📋 거래 유형별 분석:")
            for reason, count in exit_reasons.items():
                percentage = count / total_trades * 100
                print(f"   {reason}: {count}회 ({percentage:.1f}%)")
            
            # 월별 성과
            trades_df['month'] = pd.to_datetime(trades_df['exit_time']).dt.to_period('M')
            monthly_pnl = trades_df.groupby('month')['net_pnl'].sum()
            
            print(f"\n📅 월별 성과 (상위 5개월):")
            for month, pnl in monthly_pnl.nlargest(5).items():
                print(f"   {month}: ${pnl:,.2f}")
            
        else:
            print("❌ 거래가 발생하지 않았습니다.")
            print("🔍 가능한 원인:")
            print("   - 신호 임계값이 너무 높음")
            print("   - 모델 예측값이 낮음")
            print("   - 데이터 정렬 문제")
        
        print(f"\n🎉 간단한 롱 모델 백테스팅 완료!")
        
    except Exception as e:
        print(f"❌ 백테스팅 실패: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    simple_long_backtest()
