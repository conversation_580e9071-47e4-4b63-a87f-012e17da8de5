"""
Project LEVIATHAN - 하이브리드 예측 시스템
이진 분류 모델을 활용한 정교한 거래 신호 생성 시스템
"""

import pandas as pd
import numpy as np
import joblib
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 프로젝트 루트를 Python 경로에 추가
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from data.features import generate_features_from_csv
from config import PROBABILITY_THRESHOLD


class HybridPredictor:
    """
    하이브리드 예측 시스템
    이진 분류 모델의 익절 확률을 기반으로 정교한 거래 신호 생성
    """
    
    def __init__(self, binary_model_path: str = 'models/binary_trading_model.pkl'):
        """
        하이브리드 예측기 초기화
        
        Args:
            binary_model_path: 이진 분류 모델 파일 경로
        """
        self.binary_model_path = binary_model_path
        self.binary_model_data = None
        self.binary_model = None
        
        # 신호 생성 설정
        self.signal_config = {
            'high_confidence_threshold': 0.7,   # 70% 이상: 강한 신호
            'medium_confidence_threshold': 0.6, # 60% 이상: 중간 신호  
            'low_confidence_threshold': 0.5,    # 50% 이상: 약한 신호
            'position_sizing': {
                'strong_buy': 1.0,    # 100% 포지션
                'buy': 0.5,           # 50% 포지션
                'weak_buy': 0.25,     # 25% 포지션
                'hold': 0.0           # 포지션 없음
            }
        }
        
        # 모델 로드
        self.load_binary_model()
        
        print(f"🎯 하이브리드 예측 시스템 초기화 완료")
        print(f"   🤖 이진 분류 모델: {binary_model_path}")
        print(f"   📊 신호 임계값: 강함 {self.signal_config['high_confidence_threshold']:.0%}, "
              f"중간 {self.signal_config['medium_confidence_threshold']:.0%}, "
              f"약함 {self.signal_config['low_confidence_threshold']:.0%}")
        
    def load_binary_model(self) -> None:
        """
        이진 분류 모델 로드
        """
        try:
            self.binary_model_data = joblib.load(self.binary_model_path)
            self.binary_model = self.binary_model_data['model']
            print(f"✅ 이진 분류 모델 로드 성공: {self.binary_model_data.get('model_version', 'unknown')}")
        except Exception as e:
            print(f"❌ 이진 분류 모델 로드 실패: {e}")
            raise
    
    def get_latest_features(self, lookback_periods: int = 100) -> pd.DataFrame:
        """
        최신 피처 데이터 가져오기
        
        Args:
            lookback_periods: 가져올 최근 기간 수
            
        Returns:
            최신 피처 데이터프레임
        """
        # 전체 피처 데이터 생성
        features_df = generate_features_from_csv()
        
        # 최근 데이터만 선택
        latest_features = features_df.tail(lookback_periods)
        
        return latest_features
    
    def predict_win_probability(self, features: pd.DataFrame) -> np.ndarray:
        """
        익절 확률 예측
        
        Args:
            features: 피처 데이터프레임
            
        Returns:
            익절 확률 배열
        """
        return self.binary_model.predict(features)
    
    def generate_hybrid_signals(self, features: pd.DataFrame) -> pd.DataFrame:
        """
        하이브리드 신호 생성
        
        Args:
            features: 피처 데이터프레임
            
        Returns:
            신호 데이터프레임
        """
        print(f"🎯 하이브리드 신호 생성 중... ({len(features)}개 시점)")
        
        # 익절 확률 예측
        win_probabilities = self.predict_win_probability(features)
        
        signals = []
        confidences = []
        position_sizes = []
        reasons = []
        
        for i, win_prob in enumerate(win_probabilities):
            # 신호 강도 결정
            if win_prob >= self.signal_config['high_confidence_threshold']:
                signal = 'STRONG_BUY'
                position_size = self.signal_config['position_sizing']['strong_buy']
                reason = f'높은 익절 확률 ({win_prob:.1%})'
                
            elif win_prob >= self.signal_config['medium_confidence_threshold']:
                signal = 'BUY'
                position_size = self.signal_config['position_sizing']['buy']
                reason = f'중간 익절 확률 ({win_prob:.1%})'
                
            elif win_prob >= self.signal_config['low_confidence_threshold']:
                signal = 'WEAK_BUY'
                position_size = self.signal_config['position_sizing']['weak_buy']
                reason = f'약한 익절 확률 ({win_prob:.1%})'
                
            else:
                signal = 'HOLD'
                position_size = self.signal_config['position_sizing']['hold']
                reason = f'낮은 익절 확률 ({win_prob:.1%})'
            
            signals.append(signal)
            confidences.append(win_prob)
            position_sizes.append(position_size)
            reasons.append(reason)
        
        # 결과 데이터프레임 생성
        results_df = pd.DataFrame({
            'timestamp': features.index,
            'signal': signals,
            'win_probability': confidences,
            'position_size': position_sizes,
            'reason': reasons
        })
        
        # 신호 분포 분석
        signal_counts = results_df['signal'].value_counts()
        total_signals = len(results_df)
        
        print(f"   📊 신호 분포:")
        for signal, count in signal_counts.items():
            percentage = count / total_signals * 100
            print(f"      {signal}: {count:,}개 ({percentage:.1f}%)")
        
        # 평균 익절 확률
        avg_win_prob = results_df['win_probability'].mean()
        print(f"   📈 평균 익절 확률: {avg_win_prob:.1%}")
        
        # 거래 신호 비율 (HOLD 제외)
        trading_signals = results_df[results_df['signal'] != 'HOLD']
        trading_ratio = len(trading_signals) / total_signals
        print(f"   🚦 거래 신호 비율: {trading_ratio:.1%}")
        
        return results_df
    
    def analyze_signal_quality(self, signals_df: pd.DataFrame) -> Dict:
        """
        신호 품질 분석
        
        Args:
            signals_df: 신호 데이터프레임
            
        Returns:
            신호 품질 분석 결과
        """
        analysis = {}
        
        # 신호별 통계
        for signal_type in ['STRONG_BUY', 'BUY', 'WEAK_BUY', 'HOLD']:
            signal_data = signals_df[signals_df['signal'] == signal_type]
            
            if len(signal_data) > 0:
                analysis[signal_type] = {
                    'count': len(signal_data),
                    'percentage': len(signal_data) / len(signals_df) * 100,
                    'avg_win_prob': signal_data['win_probability'].mean(),
                    'min_win_prob': signal_data['win_probability'].min(),
                    'max_win_prob': signal_data['win_probability'].max(),
                    'avg_position_size': signal_data['position_size'].mean()
                }
            else:
                analysis[signal_type] = {
                    'count': 0,
                    'percentage': 0,
                    'avg_win_prob': 0,
                    'min_win_prob': 0,
                    'max_win_prob': 0,
                    'avg_position_size': 0
                }
        
        # 전체 통계
        analysis['overall'] = {
            'total_signals': len(signals_df),
            'trading_signals': len(signals_df[signals_df['signal'] != 'HOLD']),
            'trading_ratio': len(signals_df[signals_df['signal'] != 'HOLD']) / len(signals_df),
            'avg_win_probability': signals_df['win_probability'].mean(),
            'high_confidence_signals': len(signals_df[signals_df['win_probability'] >= 0.7]),
            'medium_confidence_signals': len(signals_df[
                (signals_df['win_probability'] >= 0.6) & 
                (signals_df['win_probability'] < 0.7)
            ]),
            'low_confidence_signals': len(signals_df[
                (signals_df['win_probability'] >= 0.5) & 
                (signals_df['win_probability'] < 0.6)
            ])
        }
        
        return analysis
    
    def print_signal_analysis(self, analysis: Dict) -> None:
        """
        신호 분석 결과 출력
        
        Args:
            analysis: 신호 분석 결과
        """
        print(f"\n📊 하이브리드 신호 품질 분석")
        print(f"="*50)
        
        print(f"🎯 전체 통계:")
        overall = analysis['overall']
        print(f"   총 신호: {overall['total_signals']:,}개")
        print(f"   거래 신호: {overall['trading_signals']:,}개 ({overall['trading_ratio']:.1%})")
        print(f"   평균 익절 확률: {overall['avg_win_probability']:.1%}")
        
        print(f"\n📈 신뢰도별 분포:")
        print(f"   높은 신뢰도 (≥70%): {overall['high_confidence_signals']:,}개")
        print(f"   중간 신뢰도 (60-70%): {overall['medium_confidence_signals']:,}개")
        print(f"   낮은 신뢰도 (50-60%): {overall['low_confidence_signals']:,}개")
        
        print(f"\n🚦 신호별 상세:")
        for signal_type in ['STRONG_BUY', 'BUY', 'WEAK_BUY', 'HOLD']:
            data = analysis[signal_type]
            if data['count'] > 0:
                print(f"   {signal_type}:")
                print(f"      개수: {data['count']:,}개 ({data['percentage']:.1f}%)")
                print(f"      평균 익절 확률: {data['avg_win_prob']:.1%}")
                print(f"      확률 범위: {data['min_win_prob']:.1%} ~ {data['max_win_prob']:.1%}")
                print(f"      평균 포지션 크기: {data['avg_position_size']:.1%}")


if __name__ == "__main__":
    """
    하이브리드 예측 시스템 테스트
    """
    print("🎯 Project LEVIATHAN - 하이브리드 예측 시스템")
    print("=" * 60)
    print("🚀 전략: 이진 분류 모델 기반 정교한 신호 생성")
    print("🎯 목표: 익절 확률에 따른 차등 포지션 사이징")
    print("=" * 60)
    
    try:
        # 하이브리드 예측기 초기화
        hybrid_predictor = HybridPredictor()
        
        # 최신 피처 데이터 가져오기 (최근 100개 시점)
        latest_features = hybrid_predictor.get_latest_features(100)
        
        # 하이브리드 신호 생성
        signals_df = hybrid_predictor.generate_hybrid_signals(latest_features)
        
        # 신호 품질 분석
        analysis = hybrid_predictor.analyze_signal_quality(signals_df)
        hybrid_predictor.print_signal_analysis(analysis)
        
        # 최근 10개 신호 출력
        print(f"\n📊 최근 10개 신호:")
        recent_signals = signals_df.tail(10)[['signal', 'win_probability', 'position_size', 'reason']]
        for idx, row in recent_signals.iterrows():
            print(f"   {row['signal']:>10}: {row['win_probability']:.1%} 확률, "
                  f"{row['position_size']:.0%} 포지션 - {row['reason']}")
        
        print(f"\n🎉 하이브리드 예측 시스템 테스트 완료!")
        print(f"📊 핵심 성과: 익절 확률 기반 차등 신호 생성 성공")
        
    except Exception as e:
        print(f"❌ 오류 발생: {e}")
        import traceback
        traceback.print_exc()
