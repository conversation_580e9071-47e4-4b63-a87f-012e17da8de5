"""
Project LEVIATHAN - 헬퍼 함수 단위 테스트
"""

import unittest
import pandas as pd
import numpy as np
import sys
from pathlib import Path

# 프로젝트 루트 경로 추가
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from utils.helpers_refactored import (
    DataValidator, FinancialCalculator, Formatter, 
    SafeMath, PerformanceReporter
)


class TestDataValidator(unittest.TestCase):
    """데이터 검증 클래스 테스트"""
    
    def test_validate_dataframe(self):
        """데이터프레임 유효성 검사 테스트"""
        # 정상 데이터프레임
        df = pd.DataFrame({
            'col1': [1, 2, 3],
            'col2': [4, 5, 6]
        })
        
        result = DataValidator.validate_dataframe(df, ['col1', 'col2'])
        self.assertTrue(result)
        
        # 누락된 컬럼
        result = DataValidator.validate_dataframe(df, ['col1', 'col3'])
        self.assertFalse(result)
        
        # 빈 데이터프레임
        empty_df = pd.DataFrame()
        result = DataValidator.validate_dataframe(empty_df, ['col1'])
        self.assertFalse(result)
    
    def test_validate_price_data(self):
        """가격 데이터 유효성 검사 테스트"""
        # 정상 가격 데이터
        price_data = pd.DataFrame({
            'open': [100, 101, 102],
            'high': [105, 106, 107],
            'low': [95, 96, 97],
            'close': [103, 104, 105],
            'volume': [1000, 1100, 1200]
        })
        
        result = DataValidator.validate_price_data(price_data)
        self.assertTrue(result)
        
        # 비논리적 가격 데이터 (high < low)
        invalid_price_data = pd.DataFrame({
            'open': [100, 101, 102],
            'high': [90, 91, 92],  # high < low
            'low': [95, 96, 97],
            'close': [103, 104, 105],
            'volume': [1000, 1100, 1200]
        })
        
        result = DataValidator.validate_price_data(invalid_price_data)
        self.assertFalse(result)


class TestFinancialCalculator(unittest.TestCase):
    """금융 계산 클래스 테스트"""
    
    def setUp(self):
        """테스트 데이터 설정"""
        self.prices = pd.Series([100, 102, 101, 105, 103])
        self.returns = pd.Series([0.02, -0.01, 0.04, -0.02])
    
    def test_calculate_returns(self):
        """수익률 계산 테스트"""
        # Simple returns (이상치 제거로 인해 약간 다를 수 있음)
        simple_returns = FinancialCalculator.calculate_returns(self.prices, 'simple')
        expected = self.prices.pct_change()

        # 기본적인 속성 확인
        self.assertIsInstance(simple_returns, pd.Series)
        self.assertEqual(len(simple_returns), len(self.prices))

        # 첫 번째 값은 NaN이어야 함
        self.assertTrue(pd.isna(simple_returns.iloc[0]))

        # Log returns
        log_returns = FinancialCalculator.calculate_returns(self.prices, 'log')
        self.assertIsInstance(log_returns, pd.Series)
        self.assertEqual(len(log_returns), len(self.prices))
        self.assertTrue(pd.isna(log_returns.iloc[0]))

        # 잘못된 메서드
        with self.assertRaises(ValueError):
            FinancialCalculator.calculate_returns(self.prices, 'invalid')
    
    def test_calculate_volatility(self):
        """변동성 계산 테스트"""
        vol = FinancialCalculator.calculate_volatility(self.returns, window=3)
        
        # 결과가 Series인지 확인
        self.assertIsInstance(vol, pd.Series)
        
        # 길이 확인
        self.assertEqual(len(vol), len(self.returns))
        
        # 연간화 옵션 테스트
        vol_annual = FinancialCalculator.calculate_volatility(self.returns, window=3, annualize=True)
        vol_no_annual = FinancialCalculator.calculate_volatility(self.returns, window=3, annualize=False)
        
        # 연간화된 값이 더 큰지 확인 (NaN이 아닌 값에 대해)
        valid_mask = ~vol_annual.isna() & ~vol_no_annual.isna()
        if valid_mask.any():
            self.assertTrue((vol_annual[valid_mask] > vol_no_annual[valid_mask]).all())
    
    def test_calculate_sharpe_ratio(self):
        """샤프 비율 계산 테스트"""
        sharpe = FinancialCalculator.calculate_sharpe_ratio(self.returns)
        
        # 결과가 숫자인지 확인
        self.assertIsInstance(sharpe, (int, float))
        
        # 0으로 나누기 방지 테스트
        zero_returns = pd.Series([0, 0, 0, 0])
        sharpe_zero = FinancialCalculator.calculate_sharpe_ratio(zero_returns)
        self.assertEqual(sharpe_zero, 0)
    
    def test_calculate_max_drawdown(self):
        """최대 낙폭 계산 테스트"""
        equity_curve = pd.Series([1000, 1100, 1050, 900, 950, 1200])
        
        mdd_info = FinancialCalculator.calculate_max_drawdown(equity_curve)
        
        # 필수 키 확인
        required_keys = ['max_drawdown_pct', 'start_date', 'bottom_date']
        for key in required_keys:
            self.assertIn(key, mdd_info)
        
        # 최대 낙폭이 양수인지 확인
        self.assertGreater(mdd_info['max_drawdown_pct'], 0)
    
    def test_calculate_profit_factor(self):
        """손익비 계산 테스트"""
        wins = pd.Series([100, 200, 150])
        losses = pd.Series([-50, -80, -30])
        
        pf = FinancialCalculator.calculate_profit_factor(wins, losses)
        expected = wins.sum() / abs(losses.sum())
        self.assertAlmostEqual(pf, expected, places=2)
        
        # 손실이 없는 경우
        no_losses = pd.Series([])
        pf_inf = FinancialCalculator.calculate_profit_factor(wins, no_losses)
        self.assertEqual(pf_inf, float('inf'))


class TestFormatter(unittest.TestCase):
    """포맷터 클래스 테스트"""
    
    def test_format_currency(self):
        """통화 포맷팅 테스트"""
        # USD
        usd_formatted = Formatter.format_currency(1234.56, "USD")
        self.assertEqual(usd_formatted, "$1,234.56")
        
        # BTC
        btc_formatted = Formatter.format_currency(0.12345678, "BTC")
        self.assertEqual(btc_formatted, "₿0.12345678")
        
        # 기타 통화
        other_formatted = Formatter.format_currency(1000, "EUR")
        self.assertEqual(other_formatted, "1,000.00 EUR")
    
    def test_format_percentage(self):
        """퍼센트 포맷팅 테스트"""
        # 기본 소수점 2자리
        pct_formatted = Formatter.format_percentage(12.3456)
        self.assertEqual(pct_formatted, "12.35%")
        
        # 소수점 1자리
        pct_formatted_1 = Formatter.format_percentage(12.3456, 1)
        self.assertEqual(pct_formatted_1, "12.3%")
    
    def test_format_number(self):
        """숫자 포맷팅 테스트"""
        # 기본 소수점 2자리
        num_formatted = Formatter.format_number(1234.5678)
        self.assertEqual(num_formatted, "1,234.57")
        
        # 소수점 0자리
        num_formatted_0 = Formatter.format_number(1234.5678, 0)
        self.assertEqual(num_formatted_0, "1,235")


class TestSafeMath(unittest.TestCase):
    """안전한 수학 연산 클래스 테스트"""
    
    def test_safe_divide(self):
        """안전한 나눗셈 테스트"""
        # 정상 나눗셈
        result = SafeMath.safe_divide(10, 2)
        self.assertEqual(result, 5.0)
        
        # 0으로 나누기
        result_zero = SafeMath.safe_divide(10, 0, default=999)
        self.assertEqual(result_zero, 999)
        
        # Series 나눗셈
        numerator = pd.Series([10, 20, 30])
        denominator = pd.Series([2, 0, 5])
        result_series = SafeMath.safe_divide(numerator, denominator, default=0)
        
        expected = pd.Series([5.0, 0.0, 6.0])
        pd.testing.assert_series_equal(result_series, expected)
    
    def test_clip_outliers(self):
        """이상치 제거 테스트"""
        data = pd.Series([1, 2, 3, 4, 5, 100, 200])  # 100, 200이 이상치

        clipped = SafeMath.clip_outliers(data, 0.01, 0.99)  # 더 극단적인 분위수 사용

        # 이상치가 제거되었는지 확인 (분위수 기준으로 조정)
        self.assertLessEqual(clipped.max(), data.quantile(0.99))
        self.assertGreaterEqual(clipped.min(), data.quantile(0.01))


class TestPerformanceReporter(unittest.TestCase):
    """성과 리포터 클래스 테스트"""
    
    def test_create_equity_curve(self):
        """자산 곡선 생성 테스트"""
        # 거래 데이터
        trades_data = {
            'exit_time': pd.date_range('2023-01-01', periods=3, freq='D'),
            'pnl': [100, -50, 200]
        }
        trades_df = pd.DataFrame(trades_data)
        
        equity_curve = PerformanceReporter.create_equity_curve(trades_df, 10000)
        
        # 결과 확인
        self.assertIsInstance(equity_curve, pd.Series)
        self.assertEqual(equity_curve.name, 'equity')
        
        # 초기 자본 + 누적 손익 확인
        expected_final = 10000 + trades_data['pnl'][0] + trades_data['pnl'][1] + trades_data['pnl'][2]
        self.assertEqual(equity_curve.iloc[-1], expected_final)
        
        # 빈 거래 데이터
        empty_trades = pd.DataFrame()
        empty_equity = PerformanceReporter.create_equity_curve(empty_trades, 5000)
        self.assertEqual(empty_equity.iloc[0], 5000)


if __name__ == '__main__':
    unittest.main()
