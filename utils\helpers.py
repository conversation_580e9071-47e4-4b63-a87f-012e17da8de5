"""
Project LEVIATHAN - Helper Functions
공통 유틸리티 및 헬퍼 함수들
"""

import pandas as pd
import numpy as np
from typing import Union, Optional, Tuple
import logging


def calculate_returns(prices: pd.Series, method: str = "simple") -> pd.Series:
    """
    수익률 계산
    
    Args:
        prices: 가격 시리즈
        method: "simple" 또는 "log"
        
    Returns:
        수익률 시리즈
    """
    if method == "simple":
        return prices.pct_change()
    elif method == "log":
        return np.log(prices / prices.shift(1))
    else:
        raise ValueError("method는 'simple' 또는 'log'여야 합니다")


def calculate_sharpe_ratio(
    returns: pd.Series, 
    risk_free_rate: float = 0.02,
    periods_per_year: int = 365
) -> float:
    """
    샤프 비율 계산
    
    Args:
        returns: 일일 수익률 시리즈
        risk_free_rate: 무위험 수익률 (연율)
        periods_per_year: 연간 기간 수 (일봉: 365, 시간봉: 8760)
        
    Returns:
        샤프 비율
    """
    if len(returns) == 0 or returns.std() == 0:
        return 0.0
    
    # 연율화된 수익률과 변동성
    annual_return = returns.mean() * periods_per_year
    annual_volatility = returns.std() * np.sqrt(periods_per_year)
    
    # 샤프 비율
    sharpe = (annual_return - risk_free_rate) / annual_volatility
    return sharpe


def calculate_max_drawdown(equity_curve: pd.Series) -> Tuple[float, pd.Timestamp, pd.Timestamp]:
    """
    최대 낙폭(MDD) 계산
    
    Args:
        equity_curve: 자산 곡선 시리즈
        
    Returns:
        (최대 낙폭 비율, 고점 날짜, 저점 날짜)
    """
    # 누적 최고점 계산
    peak = equity_curve.expanding().max()
    
    # 낙폭 계산
    drawdown = (equity_curve - peak) / peak
    
    # 최대 낙폭
    max_dd = drawdown.min()
    
    # 최대 낙폭이 발생한 지점
    max_dd_date = drawdown.idxmin()
    
    # 해당 낙폭의 고점 찾기
    peak_date = peak.loc[:max_dd_date].idxmax()
    
    return abs(max_dd), peak_date, max_dd_date


def calculate_profit_factor(trades: pd.DataFrame) -> float:
    """
    수익 팩터 계산 (총 수익 / 총 손실)
    
    Args:
        trades: 거래 내역 DataFrame (pnl 컬럼 필요)
        
    Returns:
        수익 팩터
    """
    if 'pnl' not in trades.columns:
        raise ValueError("trades DataFrame에 'pnl' 컬럼이 필요합니다")
    
    profits = trades[trades['pnl'] > 0]['pnl'].sum()
    losses = abs(trades[trades['pnl'] < 0]['pnl'].sum())
    
    if losses == 0:
        return float('inf') if profits > 0 else 0.0
    
    return profits / losses


def calculate_win_rate(trades: pd.DataFrame) -> float:
    """
    승률 계산
    
    Args:
        trades: 거래 내역 DataFrame (pnl 컬럼 필요)
        
    Returns:
        승률 (0.0 ~ 1.0)
    """
    if 'pnl' not in trades.columns:
        raise ValueError("trades DataFrame에 'pnl' 컬럼이 필요합니다")
    
    if len(trades) == 0:
        return 0.0
    
    winning_trades = len(trades[trades['pnl'] > 0])
    total_trades = len(trades)
    
    return winning_trades / total_trades


def format_currency(amount: float, currency: str = "USD") -> str:
    """
    통화 형식으로 포맷팅
    
    Args:
        amount: 금액
        currency: 통화 코드
        
    Returns:
        포맷된 문자열
    """
    if currency == "USD":
        return f"${amount:,.2f}"
    elif currency == "KRW":
        return f"₩{amount:,.0f}"
    else:
        return f"{amount:,.2f} {currency}"


def format_percentage(value: float, decimals: int = 2) -> str:
    """
    퍼센트 형식으로 포맷팅
    
    Args:
        value: 값 (0.15 = 15%)
        decimals: 소수점 자릿수
        
    Returns:
        포맷된 문자열
    """
    return f"{value * 100:.{decimals}f}%"


def validate_data(data: pd.DataFrame, required_columns: list) -> bool:
    """
    데이터 유효성 검증
    
    Args:
        data: 검증할 DataFrame
        required_columns: 필수 컬럼 리스트
        
    Returns:
        유효성 여부
    """
    logger = logging.getLogger(__name__)
    
    # 빈 데이터 확인
    if data.empty:
        logger.error("데이터가 비어있습니다")
        return False
    
    # 필수 컬럼 확인
    missing_columns = [col for col in required_columns if col not in data.columns]
    if missing_columns:
        logger.error(f"필수 컬럼이 누락되었습니다: {missing_columns}")
        return False
    
    # 결측값 확인
    null_counts = data[required_columns].isnull().sum()
    if null_counts.any():
        logger.warning(f"결측값이 발견되었습니다:\n{null_counts[null_counts > 0]}")
    
    return True


def calculate_liquidation_price(
    entry_price: float,
    leverage: int,
    position_type: str,
    margin_ratio: float = 0.05
) -> float:
    """
    강제 청산 가격 계산
    
    Args:
        entry_price: 진입 가격
        leverage: 레버리지 배수
        position_type: "LONG" 또는 "SHORT"
        margin_ratio: 유지 증거금 비율 (기본 5%)
        
    Returns:
        강제 청산 가격
    """
    if position_type.upper() == "LONG":
        # 롱 포지션: 가격이 하락할 때 청산
        liquidation_price = entry_price * (1 - (1/leverage) + margin_ratio)
    elif position_type.upper() == "SHORT":
        # 숏 포지션: 가격이 상승할 때 청산
        liquidation_price = entry_price * (1 + (1/leverage) - margin_ratio)
    else:
        raise ValueError("position_type은 'LONG' 또는 'SHORT'여야 합니다")
    
    return liquidation_price


def calculate_position_size(
    account_balance: float,
    entry_price: float,
    leverage: int,
    risk_percentage: float = 0.02
) -> float:
    """
    포지션 크기 계산
    
    Args:
        account_balance: 계좌 잔고
        entry_price: 진입 가격
        leverage: 레버리지 배수
        risk_percentage: 리스크 비율 (기본 2%)
        
    Returns:
        포지션 크기 (계약 수)
    """
    # 리스크 금액
    risk_amount = account_balance * risk_percentage
    
    # 포지션 크기 (레버리지 고려)
    position_value = risk_amount * leverage
    position_size = position_value / entry_price
    
    return position_size


if __name__ == "__main__":
    # 테스트 코드
    import pandas as pd
    
    # 샘플 데이터 생성
    dates = pd.date_range('2024-01-01', periods=100, freq='D')
    prices = pd.Series(100 * (1 + np.random.randn(100).cumsum() * 0.02), index=dates)
    returns = calculate_returns(prices)
    
    print("=== 성과 지표 테스트 ===")
    print(f"샤프 비율: {calculate_sharpe_ratio(returns):.3f}")
    
    mdd, peak_date, trough_date = calculate_max_drawdown(prices)
    print(f"최대 낙폭: {format_percentage(mdd)}")
    print(f"고점: {peak_date.strftime('%Y-%m-%d')}")
    print(f"저점: {trough_date.strftime('%Y-%m-%d')}")
    
    # 강제 청산가 테스트
    entry_price = 70000
    leverage = 10
    long_liq = calculate_liquidation_price(entry_price, leverage, "LONG")
    short_liq = calculate_liquidation_price(entry_price, leverage, "SHORT")
    
    print(f"\n=== 강제 청산가 테스트 ===")
    print(f"진입가: {format_currency(entry_price)}")
    print(f"레버리지: {leverage}x")
    print(f"롱 청산가: {format_currency(long_liq)}")
    print(f"숏 청산가: {format_currency(short_liq)}")
