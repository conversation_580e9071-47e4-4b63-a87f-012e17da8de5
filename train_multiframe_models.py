"""
Project LEVIATHAN - 멀티 타임프레임 35개 피처 모델 훈련
15분+4시간+1일+1주 통합 피처로 롱/숏 모델 훈련
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, roc_auc_score
import joblib
import warnings
warnings.filterwarnings('ignore')

import sys
from pathlib import Path

# 프로젝트 루트를 Python 경로에 추가
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def load_multiframe_features():
    """35개 멀티 타임프레임 피처 로드"""
    print("📂 멀티 타임프레임 피처 로드 중...")
    
    try:
        from data.features import generate_features_from_csv
        features_df = generate_features_from_csv()
        
        print(f"✅ 피처 로드 완료: {features_df.shape}")
        print(f"📊 피처 목록 ({len(features_df.columns)}개):")
        for i, col in enumerate(features_df.columns, 1):
            print(f"   {i:2d}. {col}")
        
        return features_df
        
    except Exception as e:
        print(f"❌ 피처 로드 실패: {e}")
        return None


def generate_targets(features_df, target_type='long'):
    """
    타겟 생성 (롱/숏 전용)
    
    Args:
        features_df: 피처 데이터프레임
        target_type: 'long' 또는 'short'
        
    Returns:
        pd.Series: 타겟 시리즈
    """
    print(f"🎯 {target_type.upper()} 타겟 생성 중...")
    
    try:
        if target_type == 'long':
            from models.target_generator import TargetGenerator
            generator = TargetGenerator()
        else:
            from models.short_target_generator import ShortTargetGenerator
            generator = ShortTargetGenerator()
        
        # 가격 데이터 로드 (1시간봉 - 하이브리드 시스템)
        price_data = pd.read_csv('data/BTCUSDT_1h.csv', index_col='timestamp', parse_dates=True)
        
        # 피처와 동일한 기간으로 필터링
        common_index = features_df.index.intersection(price_data.index)
        price_data = price_data.loc[common_index]
        
        # 타겟 생성 (타입별 메서드 사용)
        if target_type == 'long':
            targets = generator.generate_triple_barrier_target(price_data)
        else:  # short
            targets = generator.generate_short_target(price_data)
        
        # 피처와 동일한 인덱스로 정렬
        targets = targets.loc[features_df.index]
        
        print(f"✅ {target_type.upper()} 타겟 생성 완료: {targets.shape}")
        print(f"📊 타겟 분포:")
        print(targets.value_counts().sort_index())
        
        return targets
        
    except Exception as e:
        print(f"❌ {target_type.upper()} 타겟 생성 실패: {e}")
        return None


def train_lightgbm_model(features_df, targets, model_name='long'):
    """
    LightGBM 모델 훈련
    
    Args:
        features_df: 피처 데이터프레임
        targets: 타겟 시리즈
        model_name: 모델 이름 ('long' 또는 'short')
        
    Returns:
        tuple: (모델, 검증 결과)
    """
    print(f"🤖 {model_name.upper()} LightGBM 모델 훈련 시작...")
    
    # 결측값 제거
    valid_idx = targets.notna() & features_df.notna().all(axis=1)
    X = features_df[valid_idx]
    y = targets[valid_idx]

    # 🔧 LightGBM용 라벨 변환: -1,0,1 → 0,1,2
    y = y + 1  # -1→0, 0→1, 1→2
    print(f"📊 변환된 라벨 분포:")
    print(y.value_counts().sort_index())
    
    print(f"📊 유효 데이터: {len(X)}개 샘플")
    print(f"📊 피처 수: {X.shape[1]}개")
    
    # 훈련/검증 분할 (시간 순서 유지)
    split_idx = int(len(X) * 0.8)
    X_train, X_val = X.iloc[:split_idx], X.iloc[split_idx:]
    y_train, y_val = y.iloc[:split_idx], y.iloc[split_idx:]
    
    print(f"📊 훈련 데이터: {len(X_train)}개")
    print(f"📊 검증 데이터: {len(X_val)}개")
    
    # LightGBM 파라미터 (멀티 타임프레임 최적화)
    params = {
        'objective': 'multiclass',
        'num_class': 3,
        'metric': 'multi_logloss',
        'boosting_type': 'gbdt',
        'num_leaves': 31,
        'learning_rate': 0.05,
        'feature_fraction': 0.8,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'verbose': -1,
        'random_state': 42,
        'class_weight': 'balanced',  # 클래스 불균형 해결
        'max_depth': 6,
        'min_data_in_leaf': 100,
        'lambda_l1': 0.1,
        'lambda_l2': 0.1
    }
    
    # 데이터셋 생성
    train_data = lgb.Dataset(X_train, label=y_train)
    val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
    
    # 모델 훈련
    print("🔄 모델 훈련 중...")
    model = lgb.train(
        params,
        train_data,
        valid_sets=[train_data, val_data],
        valid_names=['train', 'val'],
        num_boost_round=1000,
        callbacks=[
            lgb.early_stopping(stopping_rounds=50),
            lgb.log_evaluation(period=100)
        ]
    )
    
    # 검증 성능 평가
    y_pred = model.predict(X_val, num_iteration=model.best_iteration)
    y_pred_class = np.argmax(y_pred, axis=1)
    
    # 성능 메트릭
    auc_scores = []
    for i in range(3):
        if len(np.unique(y_val)) > 1:
            y_binary = (y_val == i).astype(int)
            if y_binary.sum() > 0:
                auc = roc_auc_score(y_binary, y_pred[:, i])
                auc_scores.append(auc)
    
    avg_auc = np.mean(auc_scores) if auc_scores else 0.5
    
    # 피처 중요도
    feature_importance = pd.DataFrame({
        'feature': X.columns,
        'importance': model.feature_importance(importance_type='gain')
    }).sort_values('importance', ascending=False)
    
    print(f"✅ {model_name.upper()} 모델 훈련 완료!")
    print(f"📊 평균 AUC: {avg_auc:.4f}")
    print(f"📊 최적 반복: {model.best_iteration}")
    
    print(f"\n🔝 상위 10개 중요 피처:")
    for i, (_, row) in enumerate(feature_importance.head(10).iterrows(), 1):
        print(f"   {i:2d}. {row['feature']}: {row['importance']:.0f}")
    
    # 모델 저장
    model_path = f'models/{model_name}_multiframe_model.pkl'
    joblib.dump(model, model_path)
    print(f"💾 모델 저장: {model_path}")
    
    # 피처 중요도 저장
    importance_path = f'models/{model_name}_feature_importance.csv'
    feature_importance.to_csv(importance_path, index=False)
    print(f"📊 피처 중요도 저장: {importance_path}")
    
    validation_results = {
        'avg_auc': avg_auc,
        'best_iteration': model.best_iteration,
        'feature_importance': feature_importance,
        'train_samples': len(X_train),
        'val_samples': len(X_val),
        'total_features': X.shape[1]
    }
    
    return model, validation_results


def main():
    """메인 실행 함수"""
    print("🚀 Project LEVIATHAN - 하이브리드 1시간봉 모델 훈련")
    print("=" * 60)
    print("📊 40개 피처 (1시간+4시간+1일+1주)")
    print("🎯 하이브리드 롱/숏 모델 훈련")
    print("⚡ 1시간봉 진입 + 4시간봉 신호 + 장기 추세")
    print("=" * 60)
    
    try:
        # 1. 하이브리드 1시간봉 피처 로드
        features_df = load_multiframe_features()
        if features_df is None:
            return False
        
        # 2. 롱 모델 훈련
        print(f"\n🔥 STEP 1: 롱 모델 훈련")
        print("-" * 40)
        
        long_targets = generate_targets(features_df, 'long')
        if long_targets is not None:
            long_model, long_results = train_lightgbm_model(features_df, long_targets, 'long')
            print(f"✅ 롱 모델 훈련 성공! AUC: {long_results['avg_auc']:.4f}")
        else:
            print("❌ 롱 모델 훈련 실패")
            return False
        
        # 3. 숏 모델 훈련
        print(f"\n🔥 STEP 2: 숏 모델 훈련")
        print("-" * 40)
        
        short_targets = generate_targets(features_df, 'short')
        if short_targets is not None:
            short_model, short_results = train_lightgbm_model(features_df, short_targets, 'short')
            print(f"✅ 숏 모델 훈련 성공! AUC: {short_results['avg_auc']:.4f}")
        else:
            print("❌ 숏 모델 훈련 실패")
            return False
        
        # 4. 결과 요약
        print(f"\n🎉 하이브리드 1시간봉 모델 훈련 완료!")
        print("=" * 60)
        print(f"📊 총 피처 수: {features_df.shape[1]}개")
        print(f"📊 총 데이터: {features_df.shape[0]}개 레코드")
        print(f"⚡ 1시간봉 기준: 하루 2.4개 거래 기회")
        print(f"🎯 롱 모델 AUC: {long_results['avg_auc']:.4f}")
        print(f"🎯 숏 모델 AUC: {short_results['avg_auc']:.4f}")
        print(f"💾 하이브리드 모델 저장 완료")
        print(f"📊 피처 중요도 분석 완료")

        print(f"\n🔄 다음 단계:")
        print(f"   1. 하이브리드 피처 중요도 분석")
        print(f"   2. ART 시스템 1시간봉 적용")
        print(f"   3. Walk-Forward 검증")
        print(f"   4. 거래 빈도 vs 성능 최적화")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 오류 발생: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 멀티 타임프레임 모델 훈련 성공!")
    else:
        print("\n💥 멀티 타임프레임 모델 훈련 실패!")
