"""
Project LEVIATHAN - ART 백테스팅 엔진
ML 동적 시스템이 통합된 ART (Adaptive Risk Trading) 백테스팅 시스템
"""

import pandas as pd
import numpy as np
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 프로젝트 루트를 Python 경로에 추가
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from data.features import generate_features_from_csv
from models.dual_model_predictor import DualModelPredictor
from models.art_system import ARTSystem


class ARTBacktester:
    """
    ART (Adaptive Risk Trading) 백테스터
    
    특징:
    1. ML 동적 레버리지 (2-10배)
    2. 동적 손절/익절 (손절: 5%, 익절: 5-15%)
    3. 동적 포지션 사이징 (30-100%)
    4. 시장 상황별 적응형 전략
    5. 실시간 리스크 관리
    """
    
    def __init__(self, initial_capital: float = 10000.0):
        """
        ART 백테스터 초기화
        
        Args:
            initial_capital: 초기 자본
        """
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        
        # ART 시스템 초기화
        self.art_system = ARTSystem()
        self.dual_model = DualModelPredictor()
        
        # 거래 추적
        self.trades = []
        self.current_position = None
        self.position_history = []
        
        # 성과 추적
        self.equity_curve = []
        self.daily_returns = []
        
        # 기본 거래 비용
        self.base_fee_rate = 0.0004  # 0.04%
        self.base_slippage = 0.0002  # 0.02%
        
        print("🎯 ART 백테스터 초기화 완료")
        print(f"💰 초기 자본: ${initial_capital:,.2f}")
    
    def run_backtest(self, start_date: str = "2023-01-01", 
                    end_date: str = "2024-12-31") -> Dict:
        """
        ART 백테스팅 실행
        
        Args:
            start_date: 백테스팅 시작일
            end_date: 백테스팅 종료일
            
        Returns:
            백테스팅 결과 딕셔너리
        """
        print(f"🚀 ART 백테스팅 시작: {start_date} ~ {end_date}")
        
        # 1. 데이터 로드 및 피처 생성
        print("📊 데이터 로드 및 피처 생성...")
        features_df = generate_features_from_csv()
        
        # 날짜 필터링
        features_df.index = pd.to_datetime(features_df.index)
        mask = (features_df.index >= start_date) & (features_df.index <= end_date)
        features_df = features_df[mask]
        
        if len(features_df) == 0:
            raise ValueError(f"지정된 기간에 데이터가 없습니다: {start_date} ~ {end_date}")
        
        print(f"✅ 데이터 로드 완료: {len(features_df):,}개 레코드")
        
        # 2. ML 신호 생성
        print("🧠 ML 신호 생성...")
        ml_signals = self.dual_model.predict_dual_signals(features_df)
        
        # 3. ART 분석 및 적응형 설정
        print("🎯 ART 분석 및 적응형 설정...")
        # OHLCV 데이터 추가 (ART 시스템에 필요)
        ohlcv_data = self._prepare_ohlcv_data(features_df)
        art_results = self.art_system.analyze_and_adapt(ohlcv_data, ml_signals)
        
        # 4. 백테스팅 실행
        print("⚡ 백테스팅 실행...")
        self._execute_backtest(art_results)
        
        # 5. 결과 분석
        results = self._analyze_results(art_results, start_date, end_date)
        
        print("✅ ART 백테스팅 완료!")
        
        return results
    
    def _prepare_ohlcv_data(self, features_df: pd.DataFrame) -> pd.DataFrame:
        """
        OHLCV 데이터 준비 (ART 시스템용)
        
        Args:
            features_df: 피처 데이터프레임
            
        Returns:
            OHLCV 데이터프레임
        """
        # 원본 데이터에서 OHLCV 추출 (실제로는 별도 로드 필요)
        # 여기서는 피처에서 추정
        ohlcv_data = pd.DataFrame(index=features_df.index)
        
        # 가격 데이터 추정 (실제로는 원본 데이터 사용)
        if 'SMA_20_exec' in features_df.columns:
            ohlcv_data['close'] = features_df['SMA_20_exec'] * 1.001  # 추정값
            ohlcv_data['high'] = ohlcv_data['close'] * 1.002
            ohlcv_data['low'] = ohlcv_data['close'] * 0.998
            ohlcv_data['open'] = ohlcv_data['close'].shift(1).fillna(ohlcv_data['close'])
        else:
            # 기본값 설정
            ohlcv_data['close'] = 50000
            ohlcv_data['high'] = 50100
            ohlcv_data['low'] = 49900
            ohlcv_data['open'] = 50000
        
        # 거래량 추정
        ohlcv_data['volume'] = np.random.randint(1000, 10000, len(ohlcv_data))
        
        # 피처 데이터 추가
        for col in features_df.columns:
            ohlcv_data[col] = features_df[col]
        
        return ohlcv_data
    
    def _execute_backtest(self, art_results: pd.DataFrame):
        """
        백테스팅 실행
        
        Args:
            art_results: ART 분석 결과
        """
        print(f"⚡ 백테스팅 실행: {len(art_results):,}개 데이터 포인트")
        
        for idx, row in art_results.iterrows():
            # 현재 가격 정보
            current_price = row['close']
            
            # ART 신호 및 설정
            art_action = row.get('art_action', 'HOLD')
            dynamic_leverage = row.get('dynamic_leverage', 4.0)
            dynamic_position_size_pct = row.get('dynamic_position_size_pct', 0.5)
            dynamic_stop_loss = row.get('dynamic_stop_loss_pct', 0.05)
            dynamic_take_profit_min = row.get('dynamic_take_profit_min', 0.05)
            dynamic_take_profit_max = row.get('dynamic_take_profit_max', 0.10)
            
            # 포지션이 없는 경우 - 진입 검토
            if self.current_position is None:
                if art_action in ['LONG', 'SHORT']:
                    self._open_position(
                        idx, current_price, art_action, 
                        dynamic_leverage, dynamic_position_size_pct,
                        dynamic_stop_loss, dynamic_take_profit_max, row)
            
            # 포지션이 있는 경우 - 청산 검토
            else:
                exit_reason = self._check_exit_conditions(
                    current_price, dynamic_stop_loss, 
                    dynamic_take_profit_min, dynamic_take_profit_max, row)
                
                if exit_reason:
                    self._close_position(idx, current_price, exit_reason, row)
            
            # 자본 추적
            self.equity_curve.append({
                'timestamp': idx,
                'capital': self.current_capital,
                'position': self.current_position['type'] if self.current_position else 'CASH'
            })
        
        # 마지막 포지션 강제 청산
        if self.current_position:
            last_row = art_results.iloc[-1]
            self._close_position(art_results.index[-1], last_row['close'], 'end_of_backtest', last_row)
    
    def _open_position(self, timestamp, price: float, action: str, 
                      leverage: float, position_size_pct: float,
                      stop_loss_pct: float, take_profit_pct: float, row: pd.Series):
        """
        포지션 진입
        
        Args:
            timestamp: 타임스탬프
            price: 진입 가격
            action: 거래 액션 ('LONG', 'SHORT')
            leverage: 동적 레버리지
            position_size_pct: 동적 포지션 크기
            stop_loss_pct: 동적 손절
            take_profit_pct: 동적 익절
            row: 데이터 행
        """
        # 포지션 크기 계산
        position_value = self.current_capital * position_size_pct * leverage
        
        # 거래 비용 계산
        trading_cost = position_value * (self.base_fee_rate + self.base_slippage)
        
        # 자본에서 거래 비용 차감
        self.current_capital -= trading_cost
        
        # 포지션 정보 저장
        self.current_position = {
            'type': action,
            'entry_time': timestamp,
            'entry_price': price,
            'position_value': position_value,
            'leverage': leverage,
            'stop_loss_pct': stop_loss_pct,
            'take_profit_pct': take_profit_pct,
            'trading_cost': trading_cost,
            'market_condition': row.get('market_condition', 'NORMAL')
        }
        
        print(f"📈 {action} 진입: {timestamp} @ ${price:,.2f} "
              f"(레버리지: {leverage:.1f}x, 포지션: {position_size_pct*100:.0f}%)")
    
    def _close_position(self, timestamp, price: float, reason: str, row: pd.Series):
        """
        포지션 청산
        
        Args:
            timestamp: 타임스탬프
            price: 청산 가격
            reason: 청산 이유
            row: 데이터 행
        """
        if not self.current_position:
            return
        
        # 수익률 계산
        entry_price = self.current_position['entry_price']
        position_type = self.current_position['type']
        leverage = self.current_position['leverage']
        position_value = self.current_position['position_value']
        
        if position_type == 'LONG':
            price_change = (price - entry_price) / entry_price
        else:  # SHORT
            price_change = (entry_price - price) / entry_price
        
        # 레버리지 적용 수익률
        leveraged_return = price_change * leverage
        
        # 실제 손익 계산
        pnl = position_value * leveraged_return
        
        # 청산 거래 비용
        exit_cost = position_value * (self.base_fee_rate + self.base_slippage)
        
        # 최종 손익 (거래 비용 차감)
        net_pnl = pnl - exit_cost
        
        # 자본 업데이트
        self.current_capital += net_pnl
        
        # 거래 기록
        trade_record = {
            'entry_time': self.current_position['entry_time'],
            'exit_time': timestamp,
            'type': position_type,
            'entry_price': entry_price,
            'exit_price': price,
            'position_value': position_value,
            'leverage': leverage,
            'price_change_pct': price_change * 100,
            'leveraged_return_pct': leveraged_return * 100,
            'pnl': net_pnl,
            'exit_reason': reason,
            'market_condition': self.current_position['market_condition'],
            'capital_after': self.current_capital
        }
        
        self.trades.append(trade_record)
        
        print(f"📉 {position_type} 청산: {timestamp} @ ${price:,.2f} "
              f"({reason}) PnL: ${net_pnl:,.2f}")
        
        # 포지션 초기화
        self.current_position = None
    
    def _check_exit_conditions(self, current_price: float, stop_loss_pct: float,
                             take_profit_min: float, take_profit_max: float, 
                             row: pd.Series) -> Optional[str]:
        """
        청산 조건 확인
        
        Args:
            current_price: 현재 가격
            stop_loss_pct: 동적 손절
            take_profit_min: 최소 익절
            take_profit_max: 최대 익절
            row: 데이터 행
            
        Returns:
            청산 이유 (없으면 None)
        """
        if not self.current_position:
            return None
        
        entry_price = self.current_position['entry_price']
        position_type = self.current_position['type']
        
        if position_type == 'LONG':
            price_change = (current_price - entry_price) / entry_price
        else:  # SHORT
            price_change = (entry_price - current_price) / entry_price
        
        # 손절 확인
        if price_change <= -stop_loss_pct:
            return 'stop_loss'
        
        # 익절 확인 (동적 익절 로직)
        confidence = row.get('adjusted_signal_strength', 0.5)
        
        # 확신도에 따른 익절 임계값 결정
        if confidence > 0.7:  # 높은 확신도
            take_profit_threshold = take_profit_max
        elif confidence > 0.6:  # 중간 확신도
            take_profit_threshold = (take_profit_min + take_profit_max) / 2
        else:  # 낮은 확신도
            take_profit_threshold = take_profit_min
        
        if price_change >= take_profit_threshold:
            return 'take_profit'
        
        # 신호 기반 청산
        art_action = row.get('art_action', 'HOLD')
        if art_action == 'HOLD' or (position_type == 'LONG' and art_action == 'SHORT') or \
           (position_type == 'SHORT' and art_action == 'LONG'):
            # 신호 강도가 약해지면 청산
            if confidence < 0.3:
                return 'signal_exit'
        
        return None
    
    def _analyze_results(self, art_results: pd.DataFrame, 
                        start_date: str, end_date: str) -> Dict:
        """
        백테스팅 결과 분석
        
        Args:
            art_results: ART 분석 결과
            start_date: 시작일
            end_date: 종료일
            
        Returns:
            분석 결과 딕셔너리
        """
        if not self.trades:
            return {'error': '거래가 없습니다.'}
        
        # 기본 통계
        trades_df = pd.DataFrame(self.trades)
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['pnl'] > 0])
        losing_trades = len(trades_df[trades_df['pnl'] < 0])
        
        # 수익률 계산
        total_return = (self.current_capital - self.initial_capital) / self.initial_capital
        
        # 승률
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # 평균 손익
        avg_win = trades_df[trades_df['pnl'] > 0]['pnl'].mean() if winning_trades > 0 else 0
        avg_loss = trades_df[trades_df['pnl'] < 0]['pnl'].mean() if losing_trades > 0 else 0
        
        # 손익비
        profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')
        
        # 최대 낙폭 계산
        equity_df = pd.DataFrame(self.equity_curve)
        equity_df['peak'] = equity_df['capital'].cummax()
        equity_df['drawdown'] = (equity_df['capital'] - equity_df['peak']) / equity_df['peak']
        max_drawdown = equity_df['drawdown'].min()
        
        # ART 시스템 성과
        art_summary = self.art_system.get_art_summary(art_results)
        
        results = {
            'period': f"{start_date} ~ {end_date}",
            'initial_capital': self.initial_capital,
            'final_capital': self.current_capital,
            'total_return_pct': total_return * 100,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate_pct': win_rate * 100,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'max_drawdown_pct': abs(max_drawdown) * 100,
            'art_summary': art_summary,
            'trades_detail': trades_df.to_dict('records'),
            'equity_curve': equity_df.to_dict('records')
        }
        
        return results
    
    def print_results(self, results: Dict):
        """
        백테스팅 결과 출력
        
        Args:
            results: 백테스팅 결과
        """
        print("\n🎯 ART 백테스팅 결과")
        print("=" * 60)
        print(f"📅 기간: {results['period']}")
        print(f"💰 초기 자본: ${results['initial_capital']:,.2f}")
        print(f"💰 최종 자본: ${results['final_capital']:,.2f}")
        print(f"📈 총 수익률: {results['total_return_pct']:+.2f}%")
        print(f"📊 총 거래 수: {results['total_trades']}회")
        print(f"🏆 승률: {results['win_rate_pct']:.1f}%")
        print(f"💹 손익비: {results['profit_factor']:.2f}:1")
        print(f"📉 최대 낙폭: {results['max_drawdown_pct']:.2f}%")
        
        # ART 시스템 성과
        art_summary = results['art_summary']
        print(f"\n🎯 ART 시스템 성과:")
        print(f"⚖️ 평균 레버리지: {art_summary['average_leverage']:.1f}배")
        print(f"💰 평균 포지션: {art_summary['average_position_size']*100:.1f}%")
        print(f"📈 평균 리스크 리워드: {art_summary['average_risk_reward']:.2f}:1")


if __name__ == "__main__":
    # ART 백테스팅 실행
    print("🧪 ART 백테스팅 테스트")
    
    backtest = ARTBacktester(initial_capital=10000)
    results = backtest.run_backtest("2024-01-01", "2024-03-31")
    backtest.print_results(results)
