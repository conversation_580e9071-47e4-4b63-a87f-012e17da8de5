#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📡 Project LEVIATHAN - 실시간 데이터 스트리밍

Binance WebSocket을 통한 실시간 데이터 수신 및 처리
15분봉, 4시간봉 데이터를 실시간으로 수집하고 특성 생성

Author: 강현모
Date: 2024-12-15
Version: 1.0
"""

import websocket
import json
import threading
import time
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Callable, Optional
import sys
from pathlib import Path
from collections import deque
import numpy as np

# 프로젝트 루트 추가
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.api_config import api_config
from data.features import TechnicalIndicators
from data.interaction_features import InteractionFeatures


class RealTimeDataStream:
    """실시간 데이터 스트리밍 클래스"""
    
    def __init__(self, symbol: str = "BTCUSDT", callback: Callable = None):
        self.symbol = symbol.lower()
        self.callback = callback
        
        # WebSocket 설정
        self.ws_url = "wss://stream.binancefuture.com/ws/"
        self.ws = None
        self.is_running = False
        
        # 데이터 버퍼 (최근 1000개 캔들 저장)
        self.kline_15m_buffer = deque(maxlen=1000)
        self.kline_4h_buffer = deque(maxlen=1000)
        
        # 특성 생성기
        self.tech_indicators = TechnicalIndicators()
        self.interaction_features = InteractionFeatures()
        
        # 마지막 업데이트 시간
        self.last_15m_update = None
        self.last_4h_update = None
        
        print(f"📡 실시간 데이터 스트림 초기화: {symbol}")
    
    def on_message(self, ws, message):
        """WebSocket 메시지 처리"""
        try:
            data = json.loads(message)
            
            if 'k' in data:  # Kline 데이터
                kline = data['k']
                symbol = kline['s']
                interval = kline['i']
                is_closed = kline['x']  # 캔들이 완료되었는지
                
                if symbol == self.symbol.upper() and is_closed:
                    self._process_kline(kline, interval)
                    
        except Exception as e:
            print(f"❌ 메시지 처리 오류: {e}")
    
    def on_error(self, ws, error):
        """WebSocket 오류 처리"""
        print(f"❌ WebSocket 오류: {error}")
    
    def on_close(self, ws, close_status_code, close_msg):
        """WebSocket 연결 종료"""
        print("🔌 WebSocket 연결 종료")
        self.is_running = False
    
    def on_open(self, ws):
        """WebSocket 연결 시작"""
        print("✅ WebSocket 연결 성공")
        
        # 구독할 스트림 설정
        subscribe_message = {
            "method": "SUBSCRIBE",
            "params": [
                f"{self.symbol}@kline_15m",
                f"{self.symbol}@kline_4h"
            ],
            "id": 1
        }
        
        ws.send(json.dumps(subscribe_message))
        print(f"📊 구독 시작: {self.symbol} 15m, 4h")
        self.is_running = True
    
    def _process_kline(self, kline: Dict, interval: str):
        """K라인 데이터 처리"""
        # K라인 데이터를 DataFrame 형태로 변환
        candle_data = {
            'timestamp': pd.to_datetime(kline['t'], unit='ms'),
            'open': float(kline['o']),
            'high': float(kline['h']),
            'low': float(kline['l']),
            'close': float(kline['c']),
            'volume': float(kline['v'])
        }
        
        if interval == "15m":
            self.kline_15m_buffer.append(candle_data)
            self.last_15m_update = datetime.now()
            print(f"📈 15m 업데이트: {candle_data['timestamp']} | ${candle_data['close']:,.2f}")
            
        elif interval == "4h":
            self.kline_4h_buffer.append(candle_data)
            self.last_4h_update = datetime.now()
            print(f"📊 4h 업데이트: {candle_data['timestamp']} | ${candle_data['close']:,.2f}")
        
        # 콜백 함수 호출 (신호 생성 등)
        if self.callback:
            self.callback(interval, candle_data)
    
    def get_latest_data(self, interval: str = "15m", count: int = 100) -> Optional[pd.DataFrame]:
        """최신 데이터 반환"""
        if interval == "15m":
            buffer = self.kline_15m_buffer
        elif interval == "4h":
            buffer = self.kline_4h_buffer
        else:
            return None
        
        if len(buffer) == 0:
            return None
        
        # 최근 count개 데이터를 DataFrame으로 변환
        data_list = list(buffer)[-count:]
        df = pd.DataFrame(data_list)
        
        if len(df) > 0:
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)
        
        return df
    
    def get_features(self, interval: str = "15m", count: int = 100) -> Optional[pd.DataFrame]:
        """기술적 지표가 포함된 특성 데이터 반환"""
        df = self.get_latest_data(interval, count)
        
        if df is None or len(df) < 50:  # 최소 50개 데이터 필요
            return None
        
        try:
            # 기술적 지표 생성
            df_with_indicators = self.tech_indicators.add_all_indicators(df.copy())
            
            # 상호작용 특성 생성 (15분봉과 4시간봉 모두 있을 때)
            if interval == "15m" and len(self.kline_4h_buffer) > 20:
                df_4h = self.get_latest_data("4h", 50)
                if df_4h is not None:
                    df_with_interactions = self.interaction_features.create_multi_timeframe_features(
                        df_with_indicators, df_4h
                    )
                    return df_with_interactions
            
            return df_with_indicators
            
        except Exception as e:
            print(f"❌ 특성 생성 오류: {e}")
            return df
    
    def start_stream(self):
        """스트리밍 시작"""
        print("🚀 실시간 데이터 스트리밍 시작...")

        websocket.enableTrace(False)  # 디버그 로그 비활성화

        self.ws = websocket.WebSocketApp(
            self.ws_url,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close,
            on_open=self.on_open
        )

        # SSL 설정 추가
        import ssl

        # 별도 스레드에서 실행
        self.ws_thread = threading.Thread(
            target=self.ws.run_forever,
            kwargs={'sslopt': {"cert_reqs": ssl.CERT_NONE}}
        )
        self.ws_thread.daemon = True
        self.ws_thread.start()
    
    def stop_stream(self):
        """스트리밍 중지"""
        print("⏹️ 실시간 데이터 스트리밍 중지...")
        self.is_running = False
        
        if self.ws:
            self.ws.close()
    
    def get_status(self) -> Dict:
        """스트림 상태 반환"""
        return {
            "is_running": self.is_running,
            "symbol": self.symbol,
            "15m_buffer_size": len(self.kline_15m_buffer),
            "4h_buffer_size": len(self.kline_4h_buffer),
            "last_15m_update": self.last_15m_update,
            "last_4h_update": self.last_4h_update
        }
    
    def print_status(self):
        """상태 정보 출력"""
        status = self.get_status()
        
        print(f"\n📡 실시간 데이터 스트림 상태:")
        print(f"   심볼: {status['symbol'].upper()}")
        print(f"   실행 중: {'✅' if status['is_running'] else '❌'}")
        print(f"   15분봉 버퍼: {status['15m_buffer_size']}개")
        print(f"   4시간봉 버퍼: {status['4h_buffer_size']}개")
        
        if status['last_15m_update']:
            print(f"   마지막 15분봉: {status['last_15m_update'].strftime('%H:%M:%S')}")
        if status['last_4h_update']:
            print(f"   마지막 4시간봉: {status['last_4h_update'].strftime('%H:%M:%S')}")


def signal_callback(interval: str, candle_data: Dict):
    """신호 생성 콜백 함수 (예시)"""
    print(f"🔔 새로운 {interval} 캔들: ${candle_data['close']:,.2f}")
    
    # 여기에 실제 신호 생성 로직 추가
    # 예: ML 모델 예측, 거래 신호 생성 등


if __name__ == "__main__":
    # 실시간 데이터 스트림 테스트
    print("📡 실시간 데이터 스트림 테스트")
    print("=" * 50)
    
    # 스트림 시작
    stream = RealTimeDataStream("BTCUSDT", signal_callback)
    stream.start_stream()
    
    try:
        # 30초간 실행
        print("⏰ 30초간 데이터 수신 테스트...")
        time.sleep(30)
        
        # 상태 확인
        stream.print_status()
        
        # 최신 데이터 확인
        latest_15m = stream.get_latest_data("15m", 5)
        if latest_15m is not None:
            print(f"\n📈 최근 15분봉 데이터:")
            print(latest_15m.tail())
        
        # 특성 데이터 확인
        features = stream.get_features("15m", 50)
        if features is not None:
            print(f"\n🧠 특성 데이터 (마지막 행):")
            print(features.tail(1))
        
    except KeyboardInterrupt:
        print("\n⏹️ 사용자 중단")
    
    finally:
        stream.stop_stream()
        print("✅ 테스트 완료")
