#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 Project LEVIATHAN - ML 기반 동적 익절 예측 시스템

머신러닝이 진입 시점에서 최적 익절 범위와 익절 확률을 예측하여
실시간으로 익절 타이밍을 결정하는 지능형 시스템

Author: 강현모
Date: 2024-12-15
Version: 1.0
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
from typing import Dict, Tuple, Optional
import joblib
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import sys
import os
from pathlib import Path

# 프로젝트 루트 경로 추가
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.features import generate_features_from_csv


class MLDynamicExitPredictor:
    """
    🧠 ML 기반 동적 익절 예측기
    
    기능:
    1. 진입 시점에서 최적 익절 범위 예측 (2-15%)
    2. 실시간 익절 확률 계산
    3. 시장 환경별 적응형 익절 전략
    4. 리스크 조정된 익절 타이밍
    """
    
    def __init__(self):
        """초기화"""
        self.exit_range_model = None      # 익절 범위 예측 모델
        self.exit_probability_model = None # 익절 확률 예측 모델
        self.scaler = StandardScaler()
        
        # 익절 범위 설정
        self.min_take_profit = 0.02  # 최소 2%
        self.max_take_profit = 0.15  # 최대 15%
        
        # 모델 저장 경로
        self.model_dir = Path("models/saved")
        self.model_dir.mkdir(parents=True, exist_ok=True)
        
        print("🧠 ML 기반 동적 익절 예측기 초기화 완료")
    
    def prepare_training_data(self) -> Tuple[pd.DataFrame, pd.Series, pd.Series]:
        """
        동적 익절 예측을 위한 훈련 데이터 준비
        
        Returns:
            (features, exit_ranges, exit_probabilities) 튜플
        """
        print("📊 동적 익절 예측 훈련 데이터 준비 중...")
        
        # 기본 피처 데이터 로드
        features_df = generate_features_from_csv()
        print(f"   ✅ 기본 피처: {features_df.shape}")
        
        # 가격 데이터 로드
        price_data = self._load_price_data()
        
        # 익절 타겟 생성
        exit_targets = self._generate_exit_targets(price_data, features_df.index)
        
        # 피처와 타겟 정렬
        common_index = features_df.index.intersection(exit_targets.index)
        features_aligned = features_df.loc[common_index]
        targets_aligned = exit_targets.loc[common_index]
        
        # 동적 익절 피처 추가
        enhanced_features = self._create_exit_prediction_features(features_aligned, price_data)
        
        # NaN 및 무한값 제거
        # 1. NaN 체크
        nan_mask = enhanced_features.notna().all(axis=1) & targets_aligned.notna().all(axis=1)

        # 2. 무한값 체크
        inf_mask = np.isfinite(enhanced_features.select_dtypes(include=[np.number])).all(axis=1)

        # 3. 너무 큰 값 체크 (절댓값이 1e10 이상인 값 제거)
        large_value_mask = (np.abs(enhanced_features.select_dtypes(include=[np.number])) < 1e10).all(axis=1)

        # 4. 모든 조건을 만족하는 데이터만 선택
        valid_mask = nan_mask & inf_mask & large_value_mask
        features_clean = enhanced_features[valid_mask]
        targets_clean = targets_aligned[valid_mask]

        print(f"   🧹 데이터 정제: {len(enhanced_features)} → {len(features_clean)} (제거: {len(enhanced_features) - len(features_clean)}개)")
        
        # 타겟 분리
        exit_ranges = targets_clean['optimal_exit_range']
        exit_probabilities = targets_clean['exit_probability']
        
        print(f"   ✅ 정제된 데이터: {features_clean.shape[0]:,}개")
        print(f"   📈 평균 최적 익절: {exit_ranges.mean()*100:.1f}%")
        print(f"   🎯 평균 익절 확률: {exit_probabilities.mean()*100:.1f}%")
        
        return features_clean, exit_ranges, exit_probabilities
    
    def _load_price_data(self) -> pd.DataFrame:
        """가격 데이터 로드"""
        try:
            price_data = pd.read_csv('data/BTCUSDT_15m.csv')
            price_data['timestamp'] = pd.to_datetime(price_data['timestamp'])
            price_data.set_index('timestamp', inplace=True)
            return price_data
        except FileNotFoundError:
            print("❌ 가격 데이터 파일을 찾을 수 없습니다.")
            raise
    
    def _generate_exit_targets(self, price_data: pd.DataFrame, feature_index: pd.Index) -> pd.DataFrame:
        """
        익절 타겟 생성 (최적화된 버전)

        각 시점에서:
        1. 향후 N개 봉에서 달성 가능한 최대 수익률 계산
        2. 리스크 대비 최적 익절 범위 결정
        3. 해당 범위 달성 확률 계산
        """
        print("   🎯 익절 타겟 생성 중...")

        # 🚀 성능 최적화: 전체 데이터 대신 샘플링 사용
        sample_size = min(50000, len(feature_index))  # 최대 5만개 샘플
        if len(feature_index) > sample_size:
            print(f"   ⚡ 성능 최적화: {len(feature_index):,}개 → {sample_size:,}개 샘플링")
            # 정수 인덱스로 샘플링
            sample_indices = np.random.choice(len(feature_index), size=sample_size, replace=False)
            sample_indices = np.sort(sample_indices)
            feature_index = feature_index[sample_indices]

        exit_targets = []

        # 진행률 표시를 위한 설정
        total_count = len(feature_index)
        progress_interval = max(1, total_count // 20)  # 5% 간격으로 진행률 표시

        for i, timestamp in enumerate(feature_index):
            # 진행률 표시
            if i % progress_interval == 0:
                progress = (i / total_count) * 100
                print(f"   📊 진행률: {progress:.0f}% ({i:,}/{total_count:,})")

            if timestamp not in price_data.index:
                continue

            current_idx = price_data.index.get_loc(timestamp)
            current_price = price_data.iloc[current_idx]['close']

            # 🚀 최적화: 향후 48개 봉 (12시간)으로 단축
            future_window = 48
            end_idx = min(current_idx + future_window, len(price_data) - 1)

            if end_idx <= current_idx:
                continue
            
            future_prices = price_data.iloc[current_idx+1:end_idx+1]
            
            # 1. 향후 최대 수익률 계산
            max_return = (future_prices['high'].max() - current_price) / current_price
            
            # 2. 시간별 수익률 달성 확률 계산
            returns_2pct = ((future_prices['high'] >= current_price * 1.02).sum() / len(future_prices))
            returns_5pct = ((future_prices['high'] >= current_price * 1.05).sum() / len(future_prices))
            returns_10pct = ((future_prices['high'] >= current_price * 1.10).sum() / len(future_prices))
            returns_15pct = ((future_prices['high'] >= current_price * 1.15).sum() / len(future_prices))
            
            # 3. 변동성 기반 최적 익절 범위 계산
            volatility = future_prices['close'].pct_change().std()
            optimal_range = self._calculate_optimal_exit_range(max_return, volatility, returns_5pct)
            
            # 4. 최적 범위 달성 확률 계산
            target_price = current_price * (1 + optimal_range)
            exit_probability = (future_prices['high'] >= target_price).sum() / len(future_prices)
            
            exit_targets.append({
                'timestamp': timestamp,
                'optimal_exit_range': optimal_range,
                'exit_probability': exit_probability,
                'max_possible_return': max_return,
                'volatility': volatility,
                'prob_2pct': returns_2pct,
                'prob_5pct': returns_5pct,
                'prob_10pct': returns_10pct,
                'prob_15pct': returns_15pct
            })
        
        targets_df = pd.DataFrame(exit_targets)
        targets_df.set_index('timestamp', inplace=True)
        
        print(f"   ✅ 익절 타겟 생성 완료: {len(targets_df):,}개")
        
        return targets_df
    
    def _calculate_optimal_exit_range(self, max_return: float, volatility: float, prob_5pct: float) -> float:
        """
        리스크 조정된 최적 익절 범위 계산
        
        Args:
            max_return: 최대 가능 수익률
            volatility: 변동성
            prob_5pct: 5% 달성 확률
            
        Returns:
            최적 익절 범위 (0.02-0.15)
        """
        # 기본 익절 범위 (변동성 기반)
        base_range = min(max_return * 0.6, volatility * 8)
        
        # 확률 기반 조정
        if prob_5pct > 0.7:  # 높은 확률
            base_range *= 1.2
        elif prob_5pct < 0.3:  # 낮은 확률
            base_range *= 0.8
        
        # 범위 제한
        return max(self.min_take_profit, min(self.max_take_profit, base_range))
    
    def _create_exit_prediction_features(self, features_df: pd.DataFrame, price_data: pd.DataFrame) -> pd.DataFrame:
        """
        익절 예측을 위한 특화 피처 생성
        """
        print("   🔧 익절 예측 특화 피처 생성 중...")
        
        enhanced_df = features_df.copy()
        
        # 가격 데이터와 정렬
        common_index = enhanced_df.index.intersection(price_data.index)
        enhanced_df = enhanced_df.loc[common_index]
        aligned_prices = price_data.loc[common_index]
        
        # 1. 단기 모멘텀 피처
        enhanced_df['price_momentum_5'] = aligned_prices['close'].pct_change(5)
        enhanced_df['price_momentum_15'] = aligned_prices['close'].pct_change(15)
        enhanced_df['price_momentum_30'] = aligned_prices['close'].pct_change(30)
        
        # 2. 변동성 피처
        enhanced_df['volatility_5'] = aligned_prices['close'].pct_change().rolling(5).std()
        enhanced_df['volatility_15'] = aligned_prices['close'].pct_change().rolling(15).std()

        # 무한값 방지를 위한 안전한 나눗셈
        volatility_ratio = enhanced_df['volatility_5'] / enhanced_df['volatility_15'].replace(0, np.nan)
        enhanced_df['volatility_ratio'] = volatility_ratio.fillna(1.0)  # 기본값 1.0
        
        # 3. 거래량 기반 피처
        if 'volume' in aligned_prices.columns:
            enhanced_df['volume_momentum'] = aligned_prices['volume'].pct_change(5)
            # 무한값 방지
            volume_price_corr = enhanced_df['price_momentum_5'] * enhanced_df['volume_momentum']
            enhanced_df['volume_price_correlation'] = volume_price_corr.replace([np.inf, -np.inf], 0)
        
        # 4. 기술적 지표 기반 피처
        if 'RSI_14_exec' in enhanced_df.columns:
            enhanced_df['rsi_momentum'] = enhanced_df['RSI_14_exec'].diff(5)
            enhanced_df['rsi_extreme'] = ((enhanced_df['RSI_14_exec'] > 70) | (enhanced_df['RSI_14_exec'] < 30)).astype(int)
        
        # 5. 시간 기반 피처
        enhanced_df['hour'] = enhanced_df.index.hour
        enhanced_df['day_of_week'] = enhanced_df.index.dayofweek
        enhanced_df['is_weekend'] = (enhanced_df['day_of_week'] >= 5).astype(int)
        
        # 최종 데이터 정제
        enhanced_df = self._clean_features(enhanced_df)

        print(f"   ✅ 특화 피처 추가 완료: {enhanced_df.shape[1]}개 피처")

        return enhanced_df

    def _clean_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """피처 데이터 정제 (무한값, NaN, 이상값 처리)"""

        # 1. 무한값을 NaN으로 변환
        df = df.replace([np.inf, -np.inf], np.nan)

        # 2. 너무 큰 값들을 클리핑 (절댓값 1e6 이상)
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            df[col] = df[col].clip(-1e6, 1e6)

        # 3. NaN을 적절한 값으로 채우기
        for col in numeric_columns:
            if df[col].isna().any():
                # 변동성 관련 피처는 0으로
                if 'volatility' in col.lower() or 'momentum' in col.lower():
                    df[col] = df[col].fillna(0)
                # RSI 관련 피처는 50으로 (중립값)
                elif 'rsi' in col.lower():
                    df[col] = df[col].fillna(50)
                # 기타 피처는 중앙값으로
                else:
                    df[col] = df[col].fillna(df[col].median())

        return df
    
    def train_exit_models(self, optimize: bool = True) -> Dict:
        """
        익절 예측 모델 훈련
        
        Args:
            optimize: 하이퍼파라미터 최적화 여부
            
        Returns:
            훈련 결과
        """
        print("🧠 ML 기반 동적 익절 모델 훈련 시작...")
        
        # 훈련 데이터 준비
        features_df, exit_ranges, exit_probabilities = self.prepare_training_data()
        
        # 데이터 분할
        X_train, X_test, y_range_train, y_range_test = train_test_split(
            features_df, exit_ranges, test_size=0.2, random_state=42
        )
        _, _, y_prob_train, y_prob_test = train_test_split(
            features_df, exit_probabilities, test_size=0.2, random_state=42
        )
        
        # 피처 스케일링
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # 1. 익절 범위 예측 모델 훈련 (회귀)
        print("   📈 익절 범위 예측 모델 훈련 중...")
        self.exit_range_model = self._train_range_model(X_train_scaled, y_range_train, X_test_scaled, y_range_test)
        
        # 2. 익절 확률 예측 모델 훈련 (회귀)
        print("   🎯 익절 확률 예측 모델 훈련 중...")
        self.exit_probability_model = self._train_probability_model(X_train_scaled, y_prob_train, X_test_scaled, y_prob_test)
        
        # 모델 저장
        self.save_models()
        
        # 성능 평가
        results = self._evaluate_models(X_test_scaled, y_range_test, y_prob_test)
        
        print("✅ ML 기반 동적 익절 모델 훈련 완료!")
        
        return results

    def _train_range_model(self, X_train, y_train, X_test, y_test):
        """익절 범위 예측 모델 훈련"""

        params = {
            'objective': 'regression',
            'metric': 'rmse',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.8,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'verbose': -1,
            'random_state': 42
        }

        train_data = lgb.Dataset(X_train, label=y_train)
        val_data = lgb.Dataset(X_test, label=y_test, reference=train_data)

        model = lgb.train(
            params,
            train_data,
            valid_sets=[val_data],
            num_boost_round=200,
            callbacks=[lgb.early_stopping(20), lgb.log_evaluation(0)]
        )

        return model

    def _train_probability_model(self, X_train, y_train, X_test, y_test):
        """익절 확률 예측 모델 훈련"""

        params = {
            'objective': 'regression',
            'metric': 'rmse',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.8,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'verbose': -1,
            'random_state': 42
        }

        train_data = lgb.Dataset(X_train, label=y_train)
        val_data = lgb.Dataset(X_test, label=y_test, reference=train_data)

        model = lgb.train(
            params,
            train_data,
            valid_sets=[val_data],
            num_boost_round=200,
            callbacks=[lgb.early_stopping(20), lgb.log_evaluation(0)]
        )

        return model

    def _evaluate_models(self, X_test, y_range_test, y_prob_test) -> Dict:
        """모델 성능 평가"""

        # 익절 범위 예측 성능
        range_pred = self.exit_range_model.predict(X_test)
        range_mae = np.mean(np.abs(range_pred - y_range_test))
        range_rmse = np.sqrt(np.mean((range_pred - y_range_test) ** 2))

        # 익절 확률 예측 성능
        prob_pred = self.exit_probability_model.predict(X_test)
        prob_mae = np.mean(np.abs(prob_pred - y_prob_test))
        prob_rmse = np.sqrt(np.mean((prob_pred - y_prob_test) ** 2))

        results = {
            'range_mae': range_mae,
            'range_rmse': range_rmse,
            'prob_mae': prob_mae,
            'prob_rmse': prob_rmse,
            'range_accuracy': 1 - range_mae / y_range_test.mean(),
            'prob_accuracy': 1 - prob_mae / y_prob_test.mean()
        }

        print(f"   📊 익절 범위 예측 정확도: {results['range_accuracy']*100:.1f}%")
        print(f"   📊 익절 확률 예측 정확도: {results['prob_accuracy']*100:.1f}%")

        return results

    def predict_optimal_exit(self, features: pd.Series, current_price: float,
                           position_type: str = 'LONG', confidence: float = 0.6) -> Dict:
        """
        🧠 실시간 최적 익절 예측 (개선된 버전)

        Args:
            features: 현재 시점 피처
            current_price: 현재 가격
            position_type: 포지션 타입 ('LONG' or 'SHORT')
            confidence: 신호 확신도

        Returns:
            익절 예측 결과
        """
        # 변수 초기화 (스코프 문제 해결)
        predicted_range = 0.05
        predicted_probability = 0.6
        confidence_adjusted_range = 0.05
        final_range = 0.05
        target_price = current_price

        try:
            # 모델 로드 확인
            if self.exit_range_model is None or self.exit_probability_model is None:
                if not self.load_models():
                    raise ValueError("ML 익절 모델을 로드할 수 없습니다")

            # 입력 검증
            if not isinstance(position_type, str) or position_type not in ['LONG', 'SHORT']:
                raise ValueError(f"position_type must be 'LONG' or 'SHORT', got: {position_type}")

            # 피처 전처리 - 안전한 방식
            if isinstance(features, pd.Series):
                # Series를 DataFrame으로 변환
                features_df = pd.DataFrame([features])
            elif isinstance(features, pd.DataFrame):
                features_df = features.copy()
            else:
                # 다른 타입인 경우 Series로 변환 후 DataFrame으로
                features_df = pd.DataFrame([pd.Series(features)])

            # 숫자형 컬럼만 선택
            numeric_cols = features_df.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) == 0:
                raise ValueError("숫자형 피처가 없습니다")

            features_numeric = features_df[numeric_cols]

            # 결측값 처리
            features_numeric = features_numeric.fillna(0)

            # 스케일링 (첫 번째 행만 사용)
            features_array = features_numeric.iloc[0].values.reshape(1, -1)

            # 스케일러 차원 확인 및 조정
            if features_array.shape[1] != self.scaler.n_features_in_:
                # 차원이 맞지 않으면 기본값 사용
                raise ValueError(f"피처 차원 불일치: {features_array.shape[1]} != {self.scaler.n_features_in_}")

            features_scaled = self.scaler.transform(features_array)

            # ML 예측
            predicted_range = float(self.exit_range_model.predict(features_scaled)[0])
            predicted_probability = float(self.exit_probability_model.predict(features_scaled)[0])

            # 확신도 기반 조정 (더 공격적으로)
            confidence_multiplier = 0.8 + confidence * 0.8  # 0.8-1.6배 조정
            confidence_adjusted_range = predicted_range * confidence_multiplier

            # 범위 제한 (더 넓은 범위)
            final_range = max(self.min_take_profit, min(self.max_take_profit * 1.5, confidence_adjusted_range))

            # 목표 가격 계산
            if position_type == 'LONG':
                target_price = current_price * (1 + final_range)
            else:  # SHORT
                target_price = current_price * (1 - final_range)

        except Exception as e:
            # 오류 발생 시 기본값 설정
            print(f"   ⚠️ ML 익절 예측 오류: {e}")

            # 확신도 기반 기본 익절 범위
            base_range = 0.03 + confidence * 0.07  # 3-10% 범위
            final_range = base_range
            predicted_probability = 0.6
            confidence_adjusted_range = final_range

            if position_type == 'LONG':
                target_price = current_price * (1 + final_range)
            else:
                target_price = current_price * (1 - final_range)

        return {
            'optimal_exit_range': final_range,
            'exit_probability': predicted_probability,
            'target_price': target_price,
            'confidence_adjusted': confidence_adjusted_range != predicted_range,
            'ml_raw_prediction': predicted_range,
            'recommendation': self._get_exit_recommendation(predicted_probability, final_range)
        }

    def _get_exit_recommendation(self, probability: float, exit_range: float) -> str:
        """익절 추천 메시지"""

        if probability > 0.8 and exit_range > 0.08:
            return "🚀 고확률 대형 익절 기회"
        elif probability > 0.7:
            return "📈 높은 확률 익절 기회"
        elif probability > 0.5:
            return "😐 중간 확률 익절 기회"
        elif probability > 0.3:
            return "😰 낮은 확률 익절 기회"
        else:
            return "💀 매우 낮은 확률 - 조기 청산 고려"

    def save_models(self):
        """모델 저장"""
        if self.exit_range_model:
            joblib.dump(self.exit_range_model, self.model_dir / "ml_exit_range_model.pkl")
        if self.exit_probability_model:
            joblib.dump(self.exit_probability_model, self.model_dir / "ml_exit_probability_model.pkl")
        joblib.dump(self.scaler, self.model_dir / "ml_exit_scaler.pkl")
        print("   💾 ML 익절 모델 저장 완료")

    def load_models(self):
        """모델 로드"""
        try:
            self.exit_range_model = joblib.load(self.model_dir / "ml_exit_range_model.pkl")
            self.exit_probability_model = joblib.load(self.model_dir / "ml_exit_probability_model.pkl")
            self.scaler = joblib.load(self.model_dir / "ml_exit_scaler.pkl")
            print("   📥 ML 익절 모델 로드 완료")
            return True
        except FileNotFoundError:
            print("   ❌ ML 익절 모델 파일을 찾을 수 없습니다.")
            return False


if __name__ == "__main__":
    """
    ML 기반 동적 익절 예측기 테스트
    """
    print("🧠 Project LEVIATHAN - ML 기반 동적 익절 예측기 테스트")
    print("=" * 70)

    try:
        # 예측기 초기화
        predictor = MLDynamicExitPredictor()

        # 모델 훈련
        results = predictor.train_exit_models()

        print(f"\n🎯 다음 단계: 백테스터에 ML 동적 익절 시스템 통합")

    except Exception as e:
        print(f"❌ 오류 발생: {e}")
        import traceback
        traceback.print_exc()
