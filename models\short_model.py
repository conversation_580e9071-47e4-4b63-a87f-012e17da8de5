"""
🐋 Project LEVIATHAN - 숏 포지션 전문가 모델

이 모듈은 하락장에 특화된 숏 포지션 전문가 모델을 훈련합니다.
기존 통합 모델과는 별도로, 숏 포지션 진입에만 집중하여
하락 패턴과 시장 약세 신호를 더 정밀하게 포착합니다.

Author: 강현모
Date: 2024-12-14
Version: 1.0
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import optuna
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score
import joblib
import sys
import os
from typing import Tuple, Dict, Any

# 프로젝트 루트 경로 추가
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# from models.short_target_generator import ShortTargetGenerator  # 임시 비활성화
from data.features import generate_features_from_csv


class ShortExpertModel:
    """
    숏 포지션 전문가 모델
    
    하락장과 약세 신호에 특화된 이진 분류 모델:
    - 1: 숏 진입 (하락 예상)
    - 0: 진입 안 함 (상승 또는 횡보 예상)
    """
    
    def __init__(self):
        """초기화"""
        self.model = None
        self.feature_importance = None
        self.model_path = "models/short_trading_model.pkl"
        print("🔻 숏 포지션 전문가 모델 초기화 완료")
    
    def prepare_short_data(self) -> <PERSON>ple[pd.DataFrame, pd.Series]:
        """
        숏 전용 데이터 준비
        
        Returns:
            (features, target) 튜플
        """
        print("🔻 숏 전용 데이터 준비 중...")
        
        # 임시로 기본 타겟 생성 (숏 모델 개발 시 수정 예정)
        from models.target_generator import TargetGenerator
        target_gen = TargetGenerator(strategy_config='swing_s_v2')

        # 피처 데이터 로드
        features_df = generate_features_from_csv()
        print(f"   ✅ 피처 데이터: {features_df.shape}")

        # 임시 타겟 생성 (롱 타겟의 반전)
        price_data = target_gen.load_price_data()
        long_target = target_gen.generate_triple_barrier_target(price_data)
        short_target = (long_target == -1).astype(int)  # 손절 신호를 숏 진입으로 변환
        print(f"   ✅ 임시 숏 타겟: {short_target.shape}")
        
        # 인덱스 정렬
        common_index = features_df.index.intersection(short_target.index)
        features_aligned = features_df.loc[common_index]
        target_aligned = short_target.loc[common_index]
        
        # NaN 제거
        valid_mask = features_aligned.notna().all(axis=1) & target_aligned.notna()
        features_clean = features_aligned[valid_mask]
        target_clean = target_aligned[valid_mask]
        
        print(f"   ✅ 정제된 데이터: {features_clean.shape[0]}행 × {features_clean.shape[1]}열")
        print(f"   📊 타겟 분포: {target_clean.value_counts().to_dict()}")
        
        return features_clean, target_clean
    
    def create_short_features(self, features_df: pd.DataFrame) -> pd.DataFrame:
        """
        하락장 특화 피처 생성
        
        Args:
            features_df: 기본 피처 데이터프레임
            
        Returns:
            하락장 특화 피처가 추가된 데이터프레임
        """
        print("📉 하락장 특화 피처 생성 중...")
        
        enhanced_df = features_df.copy()
        
        # 1. 데드크로스 신호 (단기 MA < 장기 MA)
        if 'SMA_20_exec' in enhanced_df.columns and 'SMA_50_trend' in enhanced_df.columns:
            enhanced_df['Death_Cross'] = (enhanced_df['SMA_20_exec'] < enhanced_df['SMA_50_trend']).astype(int)
        
        # 2. RSI 과매수 구간 (하락 전조)
        if 'RSI_14_exec' in enhanced_df.columns:
            enhanced_df['RSI_Overbought'] = (enhanced_df['RSI_14_exec'] > 70).astype(int)
            enhanced_df['RSI_Bearish_Divergence'] = (enhanced_df['RSI_14_exec'] < enhanced_df['RSI_14_exec'].shift(1)).astype(int)
        
        # 3. MACD 하락 신호
        if 'MACD_exec' in enhanced_df.columns and 'MACD_signal_exec' in enhanced_df.columns:
            enhanced_df['MACD_Bearish'] = (enhanced_df['MACD_exec'] < enhanced_df['MACD_signal_exec']).astype(int)
            enhanced_df['MACD_Declining'] = (enhanced_df['MACD_exec'] < enhanced_df['MACD_exec'].shift(1)).astype(int)
        
        # 4. 볼린저 밴드 상단 터치 (하락 전조)
        if 'BB_position_exec' in enhanced_df.columns:
            enhanced_df['BB_Upper_Touch'] = (enhanced_df['BB_position_exec'] > 0.8).astype(int)
        
        # 5. 거래량 급증 + 가격 하락 (공포 매도)
        if 'Volume_ratio_exec' in enhanced_df.columns:
            enhanced_df['Volume_Spike'] = (enhanced_df['Volume_ratio_exec'] > 1.5).astype(int)
        
        # 6. 추세 약화 신호
        if 'SMA_50_trend' in enhanced_df.columns:
            enhanced_df['Trend_Weakening'] = (enhanced_df['SMA_50_trend'] < enhanced_df['SMA_50_trend'].shift(5)).astype(int)
        
        # 7. 복합 하락 신호 (여러 지표 조합)
        bear_signals = []
        for col in ['Death_Cross', 'RSI_Overbought', 'MACD_Bearish', 'BB_Upper_Touch']:
            if col in enhanced_df.columns:
                bear_signals.append(enhanced_df[col])
        
        if bear_signals:
            enhanced_df['Bear_Signal_Count'] = sum(bear_signals)
            enhanced_df['Strong_Bear_Signal'] = (enhanced_df['Bear_Signal_Count'] >= 2).astype(int)
        
        new_features = [col for col in enhanced_df.columns if col not in features_df.columns]
        print(f"   ✅ 추가된 하락장 특화 피처: {len(new_features)}개")
        print(f"   📋 피처 목록: {new_features}")
        
        return enhanced_df
    
    def optimize_hyperparameters(self, X: pd.DataFrame, y: pd.Series, n_trials: int = 50) -> Dict[str, Any]:
        """
        하이퍼파라미터 최적화 (숏 모델 특화)
        
        Args:
            X: 피처 데이터
            y: 타겟 데이터
            n_trials: 최적화 시도 횟수
            
        Returns:
            최적 하이퍼파라미터
        """
        print(f"🔧 숏 모델 하이퍼파라미터 최적화 시작 ({n_trials}회 시도)...")
        
        def objective(trial):
            # 숏 모델에 특화된 파라미터 범위
            params = {
                'objective': 'binary',
                'metric': 'binary_logloss',
                'boosting_type': 'gbdt',
                'num_leaves': trial.suggest_int('num_leaves', 10, 100),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                'feature_fraction': trial.suggest_float('feature_fraction', 0.6, 1.0),
                'bagging_fraction': trial.suggest_float('bagging_fraction', 0.6, 1.0),
                'bagging_freq': trial.suggest_int('bagging_freq', 1, 7),
                'min_child_samples': trial.suggest_int('min_child_samples', 5, 100),
                'reg_alpha': trial.suggest_float('reg_alpha', 0.0, 10.0),
                'reg_lambda': trial.suggest_float('reg_lambda', 0.0, 10.0),
                'class_weight': 'balanced',  # 클래스 불균형 해결
                'verbose': -1,
                'random_state': 42
            }
            
            # 시계열 교차 검증
            tscv = TimeSeriesSplit(n_splits=3)
            scores = []
            
            for train_idx, val_idx in tscv.split(X):
                X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
                y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
                
                train_data = lgb.Dataset(X_train, label=y_train)
                val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
                
                model = lgb.train(
                    params,
                    train_data,
                    valid_sets=[val_data],
                    num_boost_round=100,
                    callbacks=[lgb.early_stopping(10), lgb.log_evaluation(0)]
                )
                
                y_pred = model.predict(X_val)
                auc = roc_auc_score(y_val, y_pred)
                scores.append(auc)
            
            return np.mean(scores)
        
        study = optuna.create_study(direction='maximize')
        study.optimize(objective, n_trials=n_trials)
        
        best_params = study.best_params
        best_params.update({
            'objective': 'binary',
            'metric': 'binary_logloss',
            'boosting_type': 'gbdt',
            'class_weight': 'balanced',
            'verbose': -1,
            'random_state': 42
        })
        
        print(f"   ✅ 최적화 완료! 최고 AUC: {study.best_value:.4f}")
        print(f"   🎯 최적 파라미터: {best_params}")
        
        return best_params
    
    def train_short_model(self, optimize: bool = True, n_trials: int = 30) -> None:
        """
        숏 전문가 모델 훈련
        
        Args:
            optimize: 하이퍼파라미터 최적화 여부
            n_trials: 최적화 시도 횟수
        """
        print("🔻 숏 전문가 모델 훈련 시작...")
        
        # 데이터 준비
        features_df, target_series = self.prepare_short_data()
        
        # 하락장 특화 피처 추가
        enhanced_features = self.create_short_features(features_df)
        
        # 시계열 분할 (미래 데이터 참조 방지)
        split_date = '2023-01-01'
        # pandas 호환성을 위한 수정
        try:
            split_idx = enhanced_features.index.get_loc(split_date, method='nearest')
        except TypeError:
            # 최신 pandas 버전에서는 get_indexer 사용
            split_idx = enhanced_features.index.get_indexer([split_date], method='nearest')[0]
        
        X_train = enhanced_features.iloc[:split_idx]
        y_train = target_series.iloc[:split_idx]
        X_test = enhanced_features.iloc[split_idx:]
        y_test = target_series.iloc[split_idx:]
        
        print(f"   📊 훈련 데이터: {X_train.shape[0]}행 (2018-2022)")
        print(f"   📊 테스트 데이터: {X_test.shape[0]}행 (2023-2024)")
        print(f"   📊 훈련 타겟 분포: {y_train.value_counts().to_dict()}")
        
        # 하이퍼파라미터 최적화
        if optimize:
            best_params = self.optimize_hyperparameters(X_train, y_train, n_trials)
        else:
            best_params = {
                'objective': 'binary',
                'metric': 'binary_logloss',
                'boosting_type': 'gbdt',
                'num_leaves': 31,
                'learning_rate': 0.1,
                'feature_fraction': 0.9,
                'bagging_fraction': 0.8,
                'bagging_freq': 5,
                'class_weight': 'balanced',
                'verbose': -1,
                'random_state': 42
            }
        
        # 모델 훈련
        print("   🚀 최종 모델 훈련 중...")
        train_data = lgb.Dataset(X_train, label=y_train)
        test_data = lgb.Dataset(X_test, label=y_test, reference=train_data)
        
        self.model = lgb.train(
            best_params,
            train_data,
            valid_sets=[test_data],
            num_boost_round=200,
            callbacks=[lgb.early_stopping(20), lgb.log_evaluation(50)]
        )
        
        # 모델 평가
        self.evaluate_model(X_test, y_test)
        
        # 모델 저장
        self.save_model()
        
        print("   ✅ 숏 전문가 모델 훈련 완료!")
    
    def evaluate_model(self, X_test: pd.DataFrame, y_test: pd.Series) -> None:
        """모델 성능 평가"""
        print("📊 숏 전문가 모델 성능 평가...")
        
        # 예측
        y_pred_proba = self.model.predict(X_test)
        y_pred = (y_pred_proba > 0.5).astype(int)
        
        # 성능 지표
        auc = roc_auc_score(y_test, y_pred_proba)
        
        print(f"   🎯 AUC Score: {auc:.4f}")
        print(f"   📊 분류 리포트:")
        print(classification_report(y_test, y_pred, target_names=['진입 안 함', '숏 진입']))
        
        print(f"   📊 혼동 행렬:")
        print(confusion_matrix(y_test, y_pred))
        
        # 피처 중요도
        self.feature_importance = pd.DataFrame({
            'feature': X_test.columns,
            'importance': self.model.feature_importance()
        }).sort_values('importance', ascending=False)
        
        print(f"\n   🔝 상위 10개 중요 피처:")
        print(self.feature_importance.head(10).to_string(index=False))
    
    def save_model(self) -> None:
        """모델 저장"""
        joblib.dump(self.model, self.model_path)
        print(f"   💾 모델 저장 완료: {self.model_path}")
    
    def load_model(self) -> bool:
        """모델 로드"""
        try:
            self.model = joblib.load(self.model_path)
            print(f"   ✅ 모델 로드 완료: {self.model_path}")
            return True
        except FileNotFoundError:
            print(f"   ❌ 모델 파일 없음: {self.model_path}")
            return False
    
    def predict(self, features: pd.DataFrame) -> np.ndarray:
        """예측"""
        if self.model is None:
            if not self.load_model():
                raise ValueError("모델이 로드되지 않았습니다.")
        
        return self.model.predict(features)


if __name__ == "__main__":
    """
    숏 전문가 모델 훈련 실행
    """
    print("🔻 Project LEVIATHAN - 숏 포지션 전문가 모델 훈련")
    print("=" * 70)
    
    try:
        # 숏 모델 초기화
        short_model = ShortExpertModel()
        
        # 모델 훈련
        short_model.train_short_model(optimize=True, n_trials=30)
        
        print(f"\n🎯 다음 단계: 통합 시그널 시스템 구축")
        print(f"   python models/dual_model_predictor.py")
        
    except Exception as e:
        print(f"❌ 오류 발생: {e}")
        import traceback
        traceback.print_exc()
