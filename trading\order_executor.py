#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 Project LEVIATHAN - 자동 주문 실행 시스템

신호를 받아 실제 Futures 주문을 실행하는 시스템
리스크 관리, 포지션 관리, 긴급 정지 기능 포함

Author: 강현모
Date: 2024-12-15
Version: 1.0
"""

import requests
import hmac
import hashlib
import time
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple
import sys
from pathlib import Path

# 프로젝트 루트 추가
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class OrderExecutor:
    """
    자동 주문 실행 시스템
    
    기능:
    - 신호 기반 자동 주문 실행
    - 포지션 관리 (진입/청산)
    - 손절/익절 주문 설정
    - 리스크 관리
    - 긴급 정지 시스템
    """
    
    def __init__(self, api_key: str, secret_key: str, testnet: bool = True):
        self.api_key = api_key
        self.secret_key = secret_key
        
        # API 설정
        if testnet:
            self.base_url = "https://testnet.binancefuture.com"
        else:
            self.base_url = "https://fapi.binance.com"
        
        # 세션 설정
        self.session = requests.Session()
        self.session.headers.update({
            'X-MBX-APIKEY': self.api_key,
            'Content-Type': 'application/x-www-form-urlencoded'
        })
        
        # 리스크 관리 설정
        self.max_daily_loss = 0.05  # 일일 최대 손실 5%
        self.max_position_size = 0.8  # 최대 포지션 크기 80%
        self.max_open_positions = 3  # 최대 동시 포지션 수
        
        # 상태 추적
        self.daily_pnl = 0.0
        self.is_emergency_stop = False
        self.order_history = []
        self.active_positions = {}
        
        # 주문 설정
        self.min_order_size = 0.001  # 최소 주문 크기 (BTC)
        self.max_order_size = 1.0    # 최대 주문 크기 (BTC)
        
        print("🎯 자동 주문 실행 시스템 초기화 완료")
    
    def _generate_signature(self, query_string: str) -> str:
        """API 서명 생성"""
        return hmac.new(
            self.secret_key.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def _make_request(self, method: str, endpoint: str, params: Dict = None) -> Optional[Dict]:
        """API 요청 실행"""
        if params is None:
            params = {}
        
        # 타임스탬프 추가
        params['timestamp'] = int(time.time() * 1000)
        
        # 쿼리 스트링 생성
        query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
        
        # 서명 추가
        params['signature'] = self._generate_signature(query_string)
        
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url, params=params, timeout=10)
            elif method.upper() == 'POST':
                response = self.session.post(url, data=params, timeout=10)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url, params=params, timeout=10)
            else:
                raise ValueError(f"지원하지 않는 HTTP 메서드: {method}")
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ API 요청 실패: {response.status_code} - {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 네트워크 오류: {e}")
            return None
    
    def get_account_info(self) -> Optional[Dict]:
        """계정 정보 조회"""
        return self._make_request('GET', '/fapi/v2/account')
    
    def get_position_info(self, symbol: str = "BTCUSDT") -> Optional[List[Dict]]:
        """포지션 정보 조회"""
        positions = self._make_request('GET', '/fapi/v2/positionRisk')
        
        if positions:
            if symbol:
                return [pos for pos in positions if pos['symbol'] == symbol]
            return positions
        
        return None
    
    def get_current_price(self, symbol: str = "BTCUSDT") -> Optional[float]:
        """현재 가격 조회"""
        response = requests.get(f"{self.base_url}/fapi/v1/ticker/price?symbol={symbol}")
        if response.status_code == 200:
            return float(response.json()['price'])
        return None
    
    def execute_signal(self, signal: Dict) -> bool:
        """신호 기반 주문 실행"""
        print(f"\n🎯 신호 실행: {signal['signal_type']}")
        
        # 긴급 정지 확인
        if self.is_emergency_stop:
            print("🚨 긴급 정지 상태 - 주문 실행 중단")
            return False
        
        # 리스크 검증
        if not self._validate_risk(signal):
            print("⚠️ 리스크 검증 실패 - 주문 실행 중단")
            return False
        
        # 신호 타입에 따른 실행
        if signal['signal_type'] == 'BUY':
            return self._execute_long_entry(signal)
        elif signal['signal_type'] == 'SELL':
            return self._execute_short_entry(signal)
        else:
            print(f"❌ 알 수 없는 신호 타입: {signal['signal_type']}")
            return False
    
    def _validate_risk(self, signal: Dict) -> bool:
        """리스크 검증"""
        # 일일 손실 한도 확인
        if self.daily_pnl <= -self.max_daily_loss:
            print(f"❌ 일일 손실 한도 초과: {self.daily_pnl:.1%}")
            return False
        
        # 최대 포지션 수 확인
        if len(self.active_positions) >= self.max_open_positions:
            print(f"❌ 최대 포지션 수 초과: {len(self.active_positions)}")
            return False
        
        # 포지션 크기 확인
        if signal['position_size'] > self.max_position_size:
            print(f"❌ 포지션 크기 초과: {signal['position_size']:.1%}")
            return False
        
        # 신뢰도 확인
        if signal['confidence'] < 0.2:
            print(f"❌ 신뢰도 부족: {signal['confidence']:.3f}")
            return False
        
        return True
    
    def _execute_long_entry(self, signal: Dict) -> bool:
        """롱 포지션 진입"""
        symbol = signal['symbol']
        leverage = signal['leverage']
        position_size = signal['position_size']
        stop_loss_pct = signal['stop_loss_pct']
        take_profit_pct = signal['take_profit_pct']
        
        print(f"📈 롱 포지션 진입: {symbol}")
        print(f"   레버리지: {leverage:.1f}x")
        print(f"   포지션 크기: {position_size:.1%}")
        
        try:
            # 1. 레버리지 설정
            if not self._set_leverage(symbol, int(leverage)):
                return False
            
            # 2. 계정 정보 조회
            account = self.get_account_info()
            if not account:
                return False
            
            available_balance = float(account['availableBalance'])
            
            # 3. 주문 수량 계산
            current_price = self.get_current_price(symbol)
            if not current_price:
                return False
            
            # 포지션 크기를 USDT 금액으로 변환
            position_value = available_balance * position_size
            quantity = (position_value * leverage) / current_price
            
            # 최소/최대 주문 크기 확인
            quantity = max(self.min_order_size, min(quantity, self.max_order_size))
            quantity = round(quantity, 3)  # 소수점 3자리로 반올림
            
            print(f"   주문 수량: {quantity} BTC")
            print(f"   예상 금액: ${position_value:.2f}")
            
            # 4. 시장가 매수 주문
            order_result = self._place_market_order(symbol, 'BUY', quantity)
            
            if order_result:
                order_id = order_result['orderId']
                executed_price = float(order_result.get('avgPrice', current_price))
                
                print(f"✅ 롱 진입 성공: Order ID {order_id}")
                print(f"   체결가: ${executed_price:.2f}")
                
                # 5. 손절/익절 주문 설정
                self._set_stop_loss_take_profit(
                    symbol, 'LONG', quantity, executed_price,
                    stop_loss_pct, take_profit_pct
                )
                
                # 6. 포지션 추적에 추가
                self.active_positions[symbol] = {
                    'side': 'LONG',
                    'quantity': quantity,
                    'entry_price': executed_price,
                    'leverage': leverage,
                    'stop_loss': executed_price * (1 - stop_loss_pct),
                    'take_profit': executed_price * (1 + take_profit_pct),
                    'timestamp': datetime.now()
                }
                
                # 7. 주문 기록
                self.order_history.append({
                    'timestamp': datetime.now(),
                    'action': 'LONG_ENTRY',
                    'symbol': symbol,
                    'quantity': quantity,
                    'price': executed_price,
                    'order_id': order_id
                })
                
                return True
            else:
                print("❌ 롱 진입 실패")
                return False
                
        except Exception as e:
            print(f"❌ 롱 진입 오류: {e}")
            return False
    
    def _execute_short_entry(self, signal: Dict) -> bool:
        """숏 포지션 진입"""
        symbol = signal['symbol']
        leverage = signal['leverage']
        position_size = signal['position_size']
        stop_loss_pct = signal['stop_loss_pct']
        take_profit_pct = signal['take_profit_pct']
        
        print(f"📉 숏 포지션 진입: {symbol}")
        print(f"   레버리지: {leverage:.1f}x")
        print(f"   포지션 크기: {position_size:.1%}")
        
        try:
            # 1. 레버리지 설정
            if not self._set_leverage(symbol, int(leverage)):
                return False
            
            # 2. 계정 정보 조회
            account = self.get_account_info()
            if not account:
                return False
            
            available_balance = float(account['availableBalance'])
            
            # 3. 주문 수량 계산
            current_price = self.get_current_price(symbol)
            if not current_price:
                return False
            
            # 포지션 크기를 USDT 금액으로 변환
            position_value = available_balance * position_size
            quantity = (position_value * leverage) / current_price
            
            # 최소/최대 주문 크기 확인
            quantity = max(self.min_order_size, min(quantity, self.max_order_size))
            quantity = round(quantity, 3)  # 소수점 3자리로 반올림
            
            print(f"   주문 수량: {quantity} BTC")
            print(f"   예상 금액: ${position_value:.2f}")
            
            # 4. 시장가 매도 주문
            order_result = self._place_market_order(symbol, 'SELL', quantity)
            
            if order_result:
                order_id = order_result['orderId']
                executed_price = float(order_result.get('avgPrice', current_price))
                
                print(f"✅ 숏 진입 성공: Order ID {order_id}")
                print(f"   체결가: ${executed_price:.2f}")
                
                # 5. 손절/익절 주문 설정
                self._set_stop_loss_take_profit(
                    symbol, 'SHORT', quantity, executed_price,
                    stop_loss_pct, take_profit_pct
                )
                
                # 6. 포지션 추적에 추가
                self.active_positions[symbol] = {
                    'side': 'SHORT',
                    'quantity': quantity,
                    'entry_price': executed_price,
                    'leverage': leverage,
                    'stop_loss': executed_price * (1 + stop_loss_pct),
                    'take_profit': executed_price * (1 - take_profit_pct),
                    'timestamp': datetime.now()
                }
                
                # 7. 주문 기록
                self.order_history.append({
                    'timestamp': datetime.now(),
                    'action': 'SHORT_ENTRY',
                    'symbol': symbol,
                    'quantity': quantity,
                    'price': executed_price,
                    'order_id': order_id
                })
                
                return True
            else:
                print("❌ 숏 진입 실패")
                return False
                
        except Exception as e:
            print(f"❌ 숏 진입 오류: {e}")
            return False
    
    def _set_leverage(self, symbol: str, leverage: int) -> bool:
        """레버리지 설정"""
        params = {
            'symbol': symbol,
            'leverage': leverage
        }
        
        result = self._make_request('POST', '/fapi/v1/leverage', params)
        
        if result:
            print(f"✅ 레버리지 설정: {leverage}x")
            return True
        else:
            print(f"❌ 레버리지 설정 실패")
            return False
    
    def _place_market_order(self, symbol: str, side: str, quantity: float) -> Optional[Dict]:
        """시장가 주문 실행"""
        params = {
            'symbol': symbol,
            'side': side,
            'type': 'MARKET',
            'quantity': f"{quantity:.3f}"
        }
        
        print(f"📝 시장가 주문: {side} {quantity} {symbol}")
        
        result = self._make_request('POST', '/fapi/v1/order', params)
        
        if result:
            print(f"✅ 주문 체결: Order ID {result['orderId']}")
        else:
            print(f"❌ 주문 실패")
        
        return result
    
    def _set_stop_loss_take_profit(self, symbol: str, side: str, quantity: float, 
                                  entry_price: float, stop_loss_pct: float, 
                                  take_profit_pct: float):
        """손절/익절 주문 설정"""
        try:
            if side == 'LONG':
                # 롱 포지션: 손절은 매도, 익절도 매도
                stop_price = entry_price * (1 - stop_loss_pct)
                take_profit_price = entry_price * (1 + take_profit_pct)
                
                # 손절 주문 (STOP_MARKET)
                stop_params = {
                    'symbol': symbol,
                    'side': 'SELL',
                    'type': 'STOP_MARKET',
                    'quantity': f"{quantity:.3f}",
                    'stopPrice': f"{stop_price:.2f}"
                }
                
                # 익절 주문 (TAKE_PROFIT_MARKET)
                tp_params = {
                    'symbol': symbol,
                    'side': 'SELL',
                    'type': 'TAKE_PROFIT_MARKET',
                    'quantity': f"{quantity:.3f}",
                    'stopPrice': f"{take_profit_price:.2f}"
                }
                
            else:  # SHORT
                # 숏 포지션: 손절은 매수, 익절도 매수
                stop_price = entry_price * (1 + stop_loss_pct)
                take_profit_price = entry_price * (1 - take_profit_pct)
                
                # 손절 주문 (STOP_MARKET)
                stop_params = {
                    'symbol': symbol,
                    'side': 'BUY',
                    'type': 'STOP_MARKET',
                    'quantity': f"{quantity:.3f}",
                    'stopPrice': f"{stop_price:.2f}"
                }
                
                # 익절 주문 (TAKE_PROFIT_MARKET)
                tp_params = {
                    'symbol': symbol,
                    'side': 'BUY',
                    'type': 'TAKE_PROFIT_MARKET',
                    'quantity': f"{quantity:.3f}",
                    'stopPrice': f"{take_profit_price:.2f}"
                }
            
            # 손절 주문 실행
            stop_result = self._make_request('POST', '/fapi/v1/order', stop_params)
            if stop_result:
                print(f"✅ 손절 주문 설정: ${stop_price:.2f}")
            
            # 익절 주문 실행
            tp_result = self._make_request('POST', '/fapi/v1/order', tp_params)
            if tp_result:
                print(f"✅ 익절 주문 설정: ${take_profit_price:.2f}")
                
        except Exception as e:
            print(f"⚠️ 손절/익절 설정 오류: {e}")
    
    def emergency_stop(self):
        """긴급 정지 - 모든 포지션 즉시 청산"""
        print("🚨 긴급 정지 실행!")
        
        self.is_emergency_stop = True
        
        # 모든 활성 포지션 청산
        for symbol, position in self.active_positions.items():
            self._close_position(symbol, position)
        
        # 모든 미체결 주문 취소
        self._cancel_all_orders()
        
        print("✅ 긴급 정지 완료")
    
    def _close_position(self, symbol: str, position: Dict):
        """포지션 청산"""
        side = 'SELL' if position['side'] == 'LONG' else 'BUY'
        quantity = position['quantity']
        
        print(f"🔄 포지션 청산: {position['side']} {quantity} {symbol}")
        
        result = self._place_market_order(symbol, side, quantity)
        
        if result:
            print(f"✅ 포지션 청산 완료")
            del self.active_positions[symbol]
        else:
            print(f"❌ 포지션 청산 실패")
    
    def _cancel_all_orders(self):
        """모든 미체결 주문 취소"""
        for symbol in self.active_positions.keys():
            params = {'symbol': symbol}
            result = self._make_request('DELETE', '/fapi/v1/allOpenOrders', params)
            
            if result:
                print(f"✅ {symbol} 미체결 주문 취소 완료")
    
    def get_status(self) -> Dict:
        """시스템 상태 반환"""
        return {
            'is_emergency_stop': self.is_emergency_stop,
            'daily_pnl': self.daily_pnl,
            'active_positions': len(self.active_positions),
            'order_history_count': len(self.order_history),
            'max_daily_loss': self.max_daily_loss,
            'max_position_size': self.max_position_size
        }
    
    def print_status(self):
        """상태 정보 출력"""
        status = self.get_status()
        
        print(f"\n📊 주문 실행 시스템 상태:")
        print(f"   긴급 정지: {'🚨 활성' if status['is_emergency_stop'] else '✅ 정상'}")
        print(f"   일일 PnL: {status['daily_pnl']:+.1%}")
        print(f"   활성 포지션: {status['active_positions']}개")
        print(f"   주문 기록: {status['order_history_count']}개")


if __name__ == "__main__":
    # 자동 주문 실행 시스템 테스트
    print("🎯 자동 주문 실행 시스템 테스트")
    print("=" * 60)
    
    # API 키 설정
    API_KEY = "30f3132331bef6575c30cd4ec053b90d6e4c4e005cc827066e424c992ff86ccc"
    SECRET_KEY = "ed604353c26ccc02d6180bf32f5f402eeaa0ebc6963ac02ead2c32c654dbba46"
    
    # 주문 실행기 초기화
    executor = OrderExecutor(API_KEY, SECRET_KEY, testnet=True)
    
    # 상태 확인
    executor.print_status()
    
    # 테스트 신호 생성
    test_signal = {
        'timestamp': datetime.now(),
        'symbol': 'BTCUSDT',
        'signal_type': 'BUY',
        'confidence': 0.75,
        'leverage': 3.0,
        'position_size': 0.1,  # 10%
        'stop_loss_pct': 0.02,  # 2%
        'take_profit_pct': 0.05  # 5%
    }
    
    print(f"\n🧪 테스트 신호:")
    for key, value in test_signal.items():
        print(f"   {key}: {value}")
    
    # 실제 주문 실행은 주석 처리 (안전을 위해)
    print(f"\n⚠️ 실제 주문 실행은 안전을 위해 비활성화됨")
    print(f"💡 실제 실행하려면 다음 줄의 주석을 해제하세요:")
    print(f"# executor.execute_signal(test_signal)")
    
    print(f"\n✅ 자동 주문 실행 시스템 준비 완료!")
    print(f"🚀 실제 신호와 연동하여 자동 거래 가능!")
