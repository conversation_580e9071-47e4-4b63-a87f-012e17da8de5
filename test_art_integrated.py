"""
Project LEVIATHAN - ART 통합 백테스터 테스트
기존 듀얼 모델 백테스터에 ART 기능을 통합한 버전 테스트
"""

import sys
from pathlib import Path

# 프로젝트 루트를 Python 경로에 추가
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from backtesting.dual_model_backtest import DualModelBacktester


def main():
    """ART 통합 백테스터 테스트"""
    print("🎯 Project LEVIATHAN - ART 통합 백테스터 테스트")
    print("=" * 60)
    print("🧠 기존 듀얼 모델 백테스터 + ART 동적 시스템")
    print("📊 동적 레버리지: 2-10배")
    print("📉 손절: 5% (고정)")
    print("📈 익절: 5-15% (동적)")
    print("💰 포지션: 30-100% (동적)")
    print("=" * 60)
    
    try:
        # 1. 기본 백테스터 테스트
        print("\n🔄 기본 백테스터 테스트...")
        basic_backtester = DualModelBacktester(
            initial_capital=10000, 
            enable_art=False
        )
        
        # 2. ART 백테스터 테스트
        print("\n🎯 ART 백테스터 테스트...")
        art_backtester = DualModelBacktester(
            initial_capital=10000, 
            enable_art=True
        )
        
        print("\n✅ ART 통합 백테스터 초기화 성공!")
        print("🎉 모든 ML 동적 시스템이 정상 작동합니다!")
        
        # 간단한 설정 테스트
        print("\n🧪 ART 설정 테스트:")
        
        # 샘플 데이터 생성
        import pandas as pd
        sample_row = pd.Series({
            'confidence': 0.8,
            'long_prob': 0.8,
            'short_prob': 0.2,
            'action': 'BUY'
        })
        
        # ART 설정 계산 테스트
        art_settings = art_backtester.calculate_art_settings(sample_row)
        
        print(f"   📊 높은 확신도 (80%) 설정:")
        print(f"      레버리지: {art_settings['leverage']:.1f}배")
        print(f"      포지션 크기: {art_settings['position_size_pct']*100:.0f}%")
        print(f"      손절: {art_settings['stop_loss_pct']*100:.1f}%")
        print(f"      익절: {art_settings['take_profit_pct']*100:.1f}%")
        
        # 낮은 확신도 테스트
        sample_row['confidence'] = 0.3
        art_settings_low = art_backtester.calculate_art_settings(sample_row)
        
        print(f"\n   📊 낮은 확신도 (30%) 설정:")
        print(f"      레버리지: {art_settings_low['leverage']:.1f}배")
        print(f"      포지션 크기: {art_settings_low['position_size_pct']*100:.0f}%")
        print(f"      손절: {art_settings_low['stop_loss_pct']*100:.1f}%")
        print(f"      익절: {art_settings_low['take_profit_pct']*100:.1f}%")
        
        print(f"\n🎯 ART 동적 조정 확인:")
        print(f"   레버리지 변화: {art_settings_low['leverage']:.1f}배 → {art_settings['leverage']:.1f}배")
        print(f"   포지션 변화: {art_settings_low['position_size_pct']*100:.0f}% → {art_settings['position_size_pct']*100:.0f}%")
        print(f"   익절 변화: {art_settings_low['take_profit_pct']*100:.1f}% → {art_settings['take_profit_pct']*100:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 오류 발생: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 ART 통합 백테스터 테스트 성공!")
        print("✅ 모든 ML 동적 시스템이 정상 작동합니다!")
        print("\n🚀 다음 단계: 실제 백테스팅 실행")
        print("   python backtesting/dual_model_backtest.py")
    else:
        print("\n💥 ART 통합 백테스터 테스트 실패!")
